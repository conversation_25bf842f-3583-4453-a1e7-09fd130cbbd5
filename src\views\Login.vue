<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>雅克菲管理后台</h2>
        <p>欢迎登录管理系统</p>
      </div>

      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form"
        @submit.native.prevent="handleLogin">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" prefix-icon="el-icon-user" size="large"
            @keyup.enter.native="handleLogin" />
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" prefix-icon="el-icon-lock"
            size="large" show-password @keyup.enter.native="handleLogin" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="large" :loading="loading" class="login-button" @click="handleLogin">
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: 'admin',
        password: 'airfit2025'
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      },
      loading: false
    };
  },
  methods: {
    ...mapActions(['login']),

    async handleLogin() {
      try {
        // 表单验证
        await this.$refs.loginForm.validate();

        this.loading = true;

        // 执行登录
        await this.login(this.loginForm);

        this.$success('登录成功');

        // 跳转到目标页面或首页
        const redirect = this.$route.query.redirect || '/';
        this.$router.push(redirect);

      } catch (error) {
        console.error('登录失败:', error);
        this.$error(error.message || '登录失败，请检查用户名和密码');
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    color: #303133;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  p {
    color: #909399;
    font-size: 14px;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input {
    /deep/ .el-input__inner {
      height: 48px;
      line-height: 48px;
      border-radius: 6px;
      border: 1px solid #DCDFE6;

      &:focus {
        border-color: #409EFF;
      }
    }

    /deep/ .el-input__prefix {
      left: 12px;

      .el-input__icon {
        line-height: 48px;
        color: #C0C4CC;
      }
    }
  }
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

.login-footer {
  text-align: center;
  margin-top: 20px;

  p {
    color: #909399;
    font-size: 12px;
  }
}
</style>
