/**
 * 时间格式化工具函数
 */

/**
 * 格式化日期时间
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @param {string} format - 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateString) return ''

  const date = new Date(dateString)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateString)
    return ''
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  // 根据格式返回相应的字符串
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'YYYY-MM-DD HH:mm':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'YYYY-MM-DD HH:mm:ss':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    case 'MM-DD HH:mm':
      return `${month}-${day} ${hours}:${minutes}`
    case 'YYYY年MM月DD日':
      return `${year}年${month}月${day}日`
    case 'YYYY年MM月DD日 HH:mm':
      return `${year}年${month}月${day}日 ${hours}:${minutes}`
    case 'YYYY年MM月DD日 HH:mm:ss':
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
    default:
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
}

/**
 * 格式化相对时间（如：刚刚、5分钟前、1小时前等）
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateString)
    return ''
  }

  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(days / 365)

  if (seconds < 60) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else if (months < 12) {
    return `${months}个月前`
  } else {
    return `${years}年前`
  }
}

/**
 * 获取友好的日期显示
 * 今天显示时分，昨天显示"昨天 时分"，其他显示完整日期时间
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {string} 友好的日期字符串
 */
export function formatFriendlyDate(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date:', dateString)
    return ''
  }

  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  if (dateOnly.getTime() === today.getTime()) {
    return `今天 ${hours}:${minutes}`
  } else if (dateOnly.getTime() === yesterday.getTime()) {
    return `昨天 ${hours}:${minutes}`
  } else {
    return formatDateTime(dateString, 'YYYY-MM-DD HH:mm')
  }
}

/**
 * 默认导出格式化函数（用于详情页面）
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD HH:mm:ss)
 */
export default function formatDate(dateString) {
  return formatDateTime(dateString, 'YYYY-MM-DD HH:mm:ss')
}

