const express = require('express');
const router = express.Router();

// 导入各个模块的前台路由
const productsRouter = require('./products');
const bannersRouter = require('./banners');
const energyCasesRouter = require('./energy-cases');
const newsRouter = require('./news');
const dealersRouter = require('./dealers');
const resourceLibraryRouter = require('./resource-library');
const videoTutorialsRouter = require('./video-tutorials');
const heatingKnowledgeRouter = require('./heating-knowledge');
const aboutUsRouter = require('./about-us');
const contactRouter = require('./contact');
const contactQrCodesRouter = require('./contact-qrcodes');
const contactUsRouter = require('./contact-us');
const pageContentBlocksRouter = require('./page-content-blocks');
const imagesRouter = require('./images');

// 注册各个模块的路由
router.use('/products', productsRouter);
router.use('/banners', bannersRouter);
router.use('/energy-cases', energyCasesRouter);
router.use('/news', newsRouter);
router.use('/dealers', dealersRouter);
router.use('/resource-library', resourceLibraryRouter);
router.use('/video-tutorials', videoTutorialsRouter);
router.use('/heating-knowledge', heatingKnowledgeRouter);
router.use('/about-us', aboutUsRouter);
router.use('/contact-information', contactRouter);
router.use('/contact-qrcodes', contactQrCodesRouter);
router.use('/contact-us', contactUsRouter);
router.use('/page-content-blocks', pageContentBlocksRouter);
router.use('/images', imagesRouter);

// 前台API健康检查
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '前台API服务正常',
    timestamp: new Date().toISOString(),
    service: 'airfit-frontend-api'
  });
});

module.exports = router;
