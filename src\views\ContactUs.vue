<template>
  <div class="contact-us">
    <!-- 页面头部 -->
    <PageHeader tag="联系我们" />

    <!-- 联系我们内容 -->
    <section class="contact-content">
      <div class="container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchContactContent">重试</button>
          </div>
        </div>

        <!-- 联系我们文章内容 -->
        <div v-else class="article-content">
          <div class="article-header">
            <h1>{{ contactData.title }}</h1>
            <p class="article-summary">{{ contactData.summary }}</p>
          </div>

          <div class="article-body">
            <div v-html="contactData.content" class="rich-content"></div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import api from '@/api'

export default {
  name: 'ContactUs',
  components: {
    PageHeader
  },
  data() {
    return {
      loading: true,
      error: null,
      contactData: {
        title: '',
        summary: '',
        content: ''
      }
    }
  },
  async mounted() {
    await this.fetchContactContent()
  },
  methods: {
    async fetchContactContent() {
      this.loading = true
      this.error = null

      try {
        const response = await api.get('/api/front/contact-us')

        if (response.success) {
          this.contactData = response.data.contact || {}
        } else {
          throw new Error(response.message || '获取联系信息失败')
        }
      } catch (error) {
        console.error('获取联系信息失败:', error)
        this.error = error.response?.message || error.message || '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.contact-content {
  padding: 66px 0;
  background: #f8f9fa;

  @media (max-width: 768px) {
    padding: 40px 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

.article-content {
  max-width: 800px;
  margin: 0 auto;
  // background: white;
  border-radius: 8px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0 60px;

  @media (max-width: 768px) {
    padding: 0px 0;
    margin: 0;
  }
}

.article-header {
  text-align: center;
  padding-bottom: 40px;
  border-bottom: 2px solid #f5f7fa;

  h1 {
    font-size: 36px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 24px;
      margin-bottom: 0px;
    }
  }

  .article-summary {
    font-size: 18px;
    color: #909399;
    line-height: 1.6;
    margin: 0;
  }
}

.article-body {
  .rich-content {
    line-height: 1.8;
    color: #303133;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #D80514;
      margin: 30px 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #D80514;
    }

    p {
      margin-bottom: 15px;
      font-size: 14px;
    }

    a {
      color: #D80514;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    img {
      width: 100%;
    }
  }
}

// 加载和错误状态样式
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .loading-spinner,
  .error-content {
    text-align: center;
    color: #909399;

    i {
      font-size: 3rem;
      margin-bottom: 20px;
      color: #D80514;
    }

    h3 {
      margin-bottom: 10px;
      color: #303133;
    }

    p {
      margin-bottom: 20px;
    }
  }
}
</style>