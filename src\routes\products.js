const express = require('express');
const { query, transaction } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取分类及其所有子分类的ID
async function getCategoryAndChildrenIds(categoryId) {
  if (!categoryId) return [];

  const allCategories = await query('SELECT id, parent_id FROM product_categories');
  const categoryIds = [parseInt(categoryId)];

  // 递归查找所有子分类
  function findChildren(parentId) {
    const children = allCategories.filter(cat => cat.parent_id === parentId);
    children.forEach(child => {
      categoryIds.push(child.id);
      findChildren(child.id); // 递归查找子分类的子分类
    });
  }

  findChildren(parseInt(categoryId));
  return categoryIds;
}

// 获取产品列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    name = '',
    category_id = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (name) {
    whereConditions.push('p.name LIKE ?');
    queryParams.push(`%${name}%`);
  }

  if (category_id) {
    // 获取该分类及其所有子分类的ID
    const categoryIds = await getCategoryAndChildrenIds(category_id);
    if (categoryIds.length > 0) {
      const placeholders = categoryIds.map(() => '?').join(',');
      whereConditions.push(`p.category_id IN (${placeholders})`);
      queryParams.push(...categoryIds);
    }
  }

  if (status !== '') {
    whereConditions.push('p.status = ?');
    queryParams.push(status);
  }

  const whereClause = whereConditions.length > 0
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  // 获取总数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM products p
    LEFT JOIN product_categories pc ON p.category_id = pc.id
    ${whereClause}
  `;

  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取产品列表
  const productsQuery = `
    SELECT p.*, pc.name as category_name
    FROM products p
    LEFT JOIN product_categories pc ON p.category_id = pc.id
    ${whereClause}
    ORDER BY p.created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const products = await query(productsQuery, queryParams);

  res.json({
    success: true,
    message: '获取产品列表成功',
    data: {
      products,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取单个产品详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const products = await query(`
    SELECT p.*, pc.name as category_name 
    FROM products p 
    LEFT JOIN product_categories pc ON p.category_id = pc.id 
    WHERE p.id = ?
  `, [id]);
  
  if (products.length === 0) {
    throw new CustomError('产品不存在', 404);
  }
  
  const product = products[0];

  // 增加浏览次数
  await query('UPDATE products SET view_count = view_count + 1 WHERE id = ?', [id]);
  
  res.json({
    success: true,
    message: '获取产品详情成功',
    data: {
      product
    }
  });
}));

// 创建产品
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    name,
    category_id,
    main_image,
    summary,
    description,
    status = 1,
    sort_order = 0
  } = req.body;

  // 参数验证
  if (!name || !category_id) {
    throw new CustomError('产品名称和分类不能为空', 400);
  }

  // 检查分类是否存在
  const categories = await query('SELECT id FROM product_categories WHERE id = ?', [category_id]);
  if (categories.length === 0) {
    throw new CustomError('分类不存在', 400);
  }

  const result = await query(`
    INSERT INTO products (name, category_id, main_image, summary, description, status, sort_order)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [name, category_id, main_image || null, summary || null, description || null, status, sort_order]);

  res.json({
    success: true,
    message: '创建产品成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新产品
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name,
    category_id,
    main_image,
    summary,
    description,
    status,
    sort_order
  } = req.body;
  
  // 检查产品是否存在
  const existingProducts = await query('SELECT * FROM products WHERE id = ?', [id]);
  if (existingProducts.length === 0) {
    throw new CustomError('产品不存在', 404);
  }
  
  // 构建更新字段和参数
  const updateFields = [];
  const updateParams = [];

  if (name !== undefined) {
    if (!name.trim()) {
      throw new CustomError('产品名称不能为空', 400);
    }
    updateFields.push('name = ?');
    updateParams.push(name);
  }

  if (category_id !== undefined) {
    if (!category_id) {
      throw new CustomError('分类不能为空', 400);
    }
    // 检查分类是否存在
    const categories = await query('SELECT id FROM product_categories WHERE id = ?', [category_id]);
    if (categories.length === 0) {
      throw new CustomError('分类不存在', 400);
    }
    updateFields.push('category_id = ?');
    updateParams.push(category_id);
  }

  if (main_image !== undefined) {
    updateFields.push('main_image = ?');
    updateParams.push(main_image);
  }

  if (summary !== undefined) {
    updateFields.push('summary = ?');
    updateParams.push(summary);
  }

  if (description !== undefined) {
    updateFields.push('description = ?');
    updateParams.push(description);
  }

  if (status !== undefined) {
    updateFields.push('status = ?');
    updateParams.push(status);
  }

  if (sort_order !== undefined) {
    updateFields.push('sort_order = ?');
    updateParams.push(sort_order);
  }

  // 如果没有要更新的字段，返回错误
  if (updateFields.length === 0) {
    throw new CustomError('没有要更新的字段', 400);
  }
  
  // 添加更新时间
  updateFields.push('updated_at = NOW()');
  updateParams.push(id);

  await query(`
    UPDATE products
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateParams);
  
  res.json({
    success: true,
    message: '更新产品成功'
  });
}));

// 删除产品
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查产品是否存在
  const existingProducts = await query('SELECT * FROM products WHERE id = ?', [id]);
  if (existingProducts.length === 0) {
    throw new CustomError('产品不存在', 404);
  }
  
  await query('DELETE FROM products WHERE id = ?', [id]);
  
  res.json({
    success: true,
    message: '删除产品成功'
  });
}));

// 批量更新产品状态
router.post('/batch-status', authenticateToken, asyncHandler(async (req, res) => {
  const { ids, status } = req.body;
  
  if (!Array.isArray(ids) || ids.length === 0) {
    throw new CustomError('产品ID列表不能为空', 400);
  }
  
  if (![0, 1].includes(status)) {
    throw new CustomError('状态值无效', 400);
  }
  
  const placeholders = ids.map(() => '?').join(',');
  await query(`UPDATE products SET status = ? WHERE id IN (${placeholders})`, [status, ...ids]);
  
  res.json({
    success: true,
    message: '批量更新状态成功'
  });
}));

module.exports = router;
