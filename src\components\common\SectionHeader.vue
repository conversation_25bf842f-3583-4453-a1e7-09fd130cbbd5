<template>
  <div class="section-header" data-aos="fade-up">
    <h2>{{ displayTitle }}</h2>
    <p v-if="displaySubtitle">{{ displaySubtitle }}</p>
    <slot></slot>
  </div>
</template>

<script>
import { API } from '@/api'

export default {
  name: 'SectionHeader',
  props: {
    // 保持向后兼容
    title: {
      type: String,
      default: ''
    },
    subtitle: {
      type: String,
      default: ''
    },
    // 新增动态内容标识
    pageKey: {
      type: String,
      default: ''
    },
    blockKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dynamicContent: null,
      loading: false
    }
  },
  computed: {
    displayTitle() {
      return this.dynamicContent?.title || this.title
    },
    displaySubtitle() {
      return this.dynamicContent?.subtitle || this.subtitle
    }
  },
  async mounted() {
    if (this.pageKey && this.blockKey) {
      await this.loadDynamicContent()
    }
  },
  methods: {
    async loadDynamicContent() {
      try {
        this.loading = true
        const response = await API.getPageContentBlock(this.pageKey, this.blockKey)
        if (response.success) {
          this.dynamicContent = response.data
        }
      } catch (error) {
        console.warn('加载动态内容失败，使用默认内容:', error)
        // 失败时使用静态内容作为fallback
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 样式已在main.less中定义</style>
