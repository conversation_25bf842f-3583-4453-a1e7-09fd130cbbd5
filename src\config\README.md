# 菜单配置统一管理方案

## 概述

本方案解决了原本需要在两个文件中维护菜单的问题：
- `src/router/index.js` - 路由配置
- `src/layout/index.vue` - 侧边栏菜单

现在只需要在 `src/config/menu.js` 中维护一份配置，即可自动生成路由和侧边栏菜单。

## 文件结构

```
src/
├── config/
│   ├── menu.js                # 统一菜单配置（核心文件）
│   └── README.md              # 本说明文档
├── components/
│   ├── SidebarMenu.vue        # 侧边栏菜单组件
│   └── Breadcrumb.vue         # 面包屑导航组件
├── utils/
│   └── menu.js                # 菜单工具函数
├── router/
│   └── index.js               # 路由配置（自动生成）
└── layout/
    └── index.vue              # 布局文件（使用菜单组件）
```

## 核心配置文件

### menu.js 配置说明

```javascript
export const menuConfig = [
  {
    path: '/dashboard',           // 路由路径
    name: 'Dashboard',            // 路由名称
    component: Dashboard,         // 路由组件
    meta: {
      title: '仪表盘',           // 菜单标题
      icon: 'el-icon-s-home',    // 菜单图标
      requiresAuth: true,        // 是否需要登录
      hidden: false,             // 是否在菜单中隐藏
      permissions: ['admin']     // 所需权限（可选）
    },
    children: [                  // 子菜单（可选）
      // ... 子菜单配置
    ]
  }
]
```

### 配置项说明

- **path**: 路由路径，必填
- **name**: 路由名称，必填
- **component**: 路由组件，叶子节点必填
- **meta**: 路由元信息
  - **title**: 菜单显示标题，必填
  - **icon**: 菜单图标，可选
  - **requiresAuth**: 是否需要登录，默认true
  - **hidden**: 是否在菜单中隐藏，默认false
  - **permissions**: 所需权限数组，可选
- **children**: 子菜单数组，可选

## 使用方式

### 1. 添加新菜单

只需要在 `src/config/menu.js` 中添加配置：

```javascript
{
  path: '/new-module',
  name: 'NewModule',
  component: () => import('@/views/NewModule.vue'),
  meta: {
    title: '新模块',
    icon: 'el-icon-star-on',
    requiresAuth: true
  }
}
```

### 2. 添加子菜单

```javascript
{
  path: '/parent',
  name: 'Parent',
  meta: {
    title: '父菜单',
    icon: 'el-icon-folder'
  },
  children: [
    {
      path: '/parent/child1',
      name: 'Child1',
      component: () => import('@/views/Child1.vue'),
      meta: {
        title: '子菜单1',
        icon: 'el-icon-document'
      }
    }
  ]
}
```

### 3. 隐藏菜单项

```javascript
{
  path: '/hidden-page',
  name: 'HiddenPage',
  component: () => import('@/views/HiddenPage.vue'),
  meta: {
    title: '隐藏页面',
    hidden: true  // 不在侧边栏显示，但路由仍然存在
  }
}
```

### 4. 权限控制

```javascript
{
  path: '/admin-only',
  name: 'AdminOnly',
  component: () => import('@/views/AdminOnly.vue'),
  meta: {
    title: '管理员专用',
    permissions: ['admin', 'super-admin']  // 需要admin或super-admin权限
  }
}
```

## 组件使用

### SidebarMenu 组件

在布局文件中使用：

```vue
<template>
  <SidebarMenu :collapse="sidebarCollapsed" />
</template>

<script>
import SidebarMenu from '@/components/SidebarMenu.vue'

export default {
  components: {
    SidebarMenu
  }
}
</script>
```

### Breadcrumb 组件

在页面中使用面包屑导航：

```vue
<template>
  <div>
    <Breadcrumb />
    <!-- 页面内容 -->
  </div>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb.vue'

export default {
  components: {
    Breadcrumb
  }
}
</script>
```

## 工具函数

### 菜单查找

```javascript
import { findMenuByPath } from '@/utils/menu'
import { menuConfig } from '@/config/menu'

const menuItem = findMenuByPath(menuConfig, '/dashboard')
```

### 权限过滤

```javascript
import { filterMenuByPermission } from '@/utils/menu'
import { menuConfig } from '@/config/menu'

const userPermissions = ['admin', 'user']
const filteredMenu = filterMenuByPermission(menuConfig, userPermissions)
```

### 配置验证

```javascript
import { validateMenuConfig } from '@/utils/menu'
import { menuConfig } from '@/config/menu'

const result = validateMenuConfig(menuConfig)
if (!result.isValid) {
  console.error('菜单配置错误:', result.errors)
}
```

## 优势

1. **单一数据源**: 只需维护一个配置文件
2. **自动同步**: 路由和菜单自动保持一致
3. **类型安全**: 统一的配置结构减少错误
4. **易于扩展**: 支持权限控制、图标、隐藏等功能
5. **工具支持**: 提供丰富的工具函数
6. **组件化**: 可复用的菜单和面包屑组件

## 注意事项

1. 修改菜单配置后需要重启开发服务器
2. 路由路径必须唯一
3. 叶子节点必须配置component
4. 建议使用路由懒加载提高性能
