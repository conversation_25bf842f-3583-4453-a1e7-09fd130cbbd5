<template>
  <div class="news-detail">


    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="fetchNewsDetail">重试</button>
      </div>
    </div>

    <!-- 新闻详情内容 -->
    <div v-else-if="news" class="detail-content">
      <div class="container">
        <!-- 文章头部 -->
        <div class="article-header">

          <h1 class="article-title">{{ news.title }}</h1>
          <div class="article-meta">
            <span class="publish-time">
              <i class="fas fa-calendar-alt"></i>
              {{ formatDate(news.created_at) }}
            </span>
            <span class="view-count">
              <i class="fas fa-eye"></i>
              {{ news.view_count || 0 }} 次浏览
            </span>
          </div>
        </div>

        <!-- 文章内容 -->
        <div class="article-body">
          <div v-if="news.content" class="rich-content" v-html="news.content"></div>
          <div v-else class="no-content">
            <p>暂无详细内容</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'
import api from '@/api'
import formatDate from '@/utils/dateFormat'

export default {
  name: 'NewsDetail',
  components: {
    PageHeader,
    Breadcrumb
  },
  data() {
    return {
      loading: true,
      error: null,
      news: null
    }
  },
  computed: {
    breadcrumbItems() {
      const items = [
        { name: '新闻资讯', path: '/news' }
      ]
      if (this.news) {
        items.push({ name: this.news.title })
      }
      return items
    }
  },
  created() {
    this.fetchNewsDetail()
  },
  watch: {
    '$route'() {
      this.fetchNewsDetail()
    }
  },
  methods: {
    async fetchNewsDetail() {
      try {
        this.loading = true
        this.error = null

        const newsId = this.$route.params.id

        // 获取新闻详情
        const response = await api.get(`/api/front/news/${newsId}`)

        if (response.success) {
          this.news = response.data.news

          // 更新页面标题
          if (this.news.title) {
            document.title = `${this.news.title} - 新闻详情 - 雅克菲采暖`
          }
        } else {
          this.error = response.message || '获取新闻详情失败'
        }
      } catch (error) {
        console.error('获取新闻详情失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },



    formatDate
  }
}
</script>


<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.news-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .loading-spinner,
  .error-content {
    text-align: center;

    i {
      font-size: 48px;
      color: @text-lighter;
      margin-bottom: 16px;
    }

    h3 {
      margin: 16px 0 8px;
      color: @text-color;
    }

    p {
      color: @text-light;
      margin-bottom: 16px;
    }

    .btn {
      background: @primary-color;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: @border-radius-small;
      cursor: pointer;
      transition: @transition;

      &:hover {
        background: @primary-hover;
      }
    }
  }
}

.detail-content {
  padding: @spacing-xxl 0;

  .container {
    max-width: @container-max-width;
    margin: 0 auto;
    padding: 0 @spacing-lg;
  }
}

.article-header {
  text-align: center;
  margin-bottom: @spacing-xxl;
  padding-bottom: @spacing-xl;
  border-bottom: 1px solid @border-color;

  .article-category {
    margin-bottom: @spacing-lg;
  }

  .article-title {
    font-size: @font-size-xxl;
    font-weight: @font-weight-bold;
    color: @text-color;
    margin-bottom: @spacing-lg;
    line-height: 1.3;
    font-family: @font-family-base;
  }

  .article-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: @spacing-xl;

    .publish-time,
    .view-count {
      display: flex;
      align-items: center;
      gap: @spacing-xs;
      color: @text-light;
      font-size: @font-size-sm;

      i {
        font-size: 12px;
      }
    }
  }
}

.article-body {
  .rich-content {
    line-height: 1.8;
    color: @text-color;
    font-size: @font-size-base;

    :deep(img) {
      width: 100%;
      height: auto;
      border-radius: @border-radius-small;
      margin: @spacing-lg 0;
      box-shadow: @shadow-light;
    }

    :deep(p) {
      margin-bottom: @spacing-md;
      text-align: justify;
    }

  }

  .no-content {
    text-align: center;
    padding: @spacing-xxl 0;
    color: @text-lighter;

    p {
      font-size: @font-size-lg;
    }
  }
}

// 移动端适配
@media (max-width: @screen-sm) {
  .detail-content .container {
    padding: 0 @spacing-md;
  }

  .article-header {
    margin-bottom: @spacing-xl;
    padding-bottom: @spacing-lg;

    .article-title {
      font-size: @font-size-xl;
    }

    .article-meta {
      flex-direction: column;
      gap: @spacing-sm;
    }
  }

  .article-body .rich-content {
    :deep(img) {
      width: 100%;
    }

    :deep(h1) {
      font-size: @font-size-lg;
    }

    :deep(h2) {
      font-size: @font-size-base;
    }

    :deep(h3) {
      font-size: @font-size-sm;
    }
  }
}
</style>
