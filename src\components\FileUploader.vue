<template>
  <div class="file-uploader-wrapper">
    <div class="file-upload-section">
      <!-- 文件选择按钮 -->
      <div class="upload-button-section">
        <el-button 
          size="small" 
          type="primary" 
          @click="handleSelectFiles"
          :disabled="disabled"
        >
          选择文件
        </el-button>
        <div class="upload-tip">{{ tip }}</div>
      </div>

      <!-- 文件列表 -->
      <div v-if="fileList.length > 0" class="file-list">
        <div 
          v-for="(file, index) in fileList" 
          :key="index"
          class="file-item"
          :class="{ 'uploading': file.uploading, 'error': file.error }"
        >
          <div class="file-info">
            <i class="file-icon" :class="getFileIcon(file.name)"></i>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
          </div>
          
          <div class="file-status">
            <div v-if="file.uploading" class="upload-progress">
              <i class="el-icon-loading"></i>
              <span>上传中...</span>
            </div>
            <div v-else-if="file.error" class="upload-error">
              <i class="el-icon-warning"></i>
              <span>{{ file.error }}</span>
            </div>
            <div v-else class="upload-success">
              <i class="el-icon-check"></i>
              <span>上传成功</span>
            </div>
          </div>

          <div class="file-actions">
            <el-button 
              v-if="!file.uploading && !disabled"
              size="mini" 
              type="danger" 
              icon="el-icon-delete"
              @click="handleRemoveFile(index)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 隐藏的文件输入框 -->
      <input
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        style="display: none"
        @change="handleFileChange"
      >
    </div>
  </div>
</template>

<script>
import { upload } from '@/api'

export default {
  name: 'FileUploader',
  props: {
    // 当前文件列表
    value: {
      type: Array,
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否支持多文件
    multiple: {
      type: Boolean,
      default: true
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar'
    },
    // 提示文字
    tip: {
      type: String,
      default: '支持PDF、Word、Excel、PowerPoint、文本、压缩包等文件，且不超过50MB'
    },
    // 文件大小限制（MB）
    maxSize: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      fileList: []
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.fileList = newVal ? [...newVal] : []
      }
    }
  },
  methods: {
    handleSelectFiles() {
      if (this.disabled) return
      this.$refs.fileInput.click()
    },

    handleFileChange(event) {
      const files = Array.from(event.target.files)
      if (files.length === 0) return

      // 重置文件输入框
      event.target.value = ''

      // 上传文件
      files.forEach(file => {
        this.uploadFile(file)
      })
    },

    async uploadFile(file) {
      // 文件大小验证
      const fileSizeMB = file.size / 1024 / 1024
      if (fileSizeMB > this.maxSize) {
        this.$message.error(`文件 "${file.name}" 大小不能超过 ${this.maxSize}MB!`)
        return
      }

      // 添加到文件列表
      const fileItem = {
        name: file.originalname || file.name,
        size: file.size,
        uploading: true,
        error: null,
        url: null
      }
      
      this.fileList.push(fileItem)
      this.emitChange()

      try {
        // 使用封装的上传方法
        const response = await upload.uploadFile(file)
        
        if (response.success) {
          // 更新文件信息
          fileItem.uploading = false
          fileItem.url = response.data.url
          fileItem.filename = response.data.filename
          fileItem.originalname = response.data.originalname
          
          this.$message.success(`文件 "${file.name}" 上传成功`)
          this.emitChange()
          this.$emit('success', response.data)
        } else {
          fileItem.uploading = false
          fileItem.error = response.message || '上传失败'
          this.$message.error(`文件 "${file.name}" 上传失败`)
        }
      } catch (error) {
        console.error('文件上传失败:', error)
        fileItem.uploading = false
        fileItem.error = '上传失败，请重试'
        this.$message.error(`文件 "${file.name}" 上传失败`)
        this.$emit('error', error)
      }
    },

    handleRemoveFile(index) {
      this.fileList.splice(index, 1)
      this.emitChange()
    },

    emitChange() {
      this.$emit('input', this.fileList)
      this.$emit('change', this.fileList)
    },

    getFileIcon(filename) {
      const ext = filename.split('.').pop().toLowerCase()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        ppt: 'el-icon-present',
        pptx: 'el-icon-present',
        txt: 'el-icon-document',
        zip: 'el-icon-folder-opened',
        rar: 'el-icon-folder-opened'
      }
      return iconMap[ext] || 'el-icon-document'
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.file-uploader-wrapper {
  width: 100%;
}

.file-upload-section {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  background: #fafafa;
}

.upload-button-section {
  text-align: center;
  margin-bottom: 16px;
}

.upload-tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.3s;
}

.file-item:hover {
  border-color: #409EFF;
}

.file-item.uploading {
  border-color: #409EFF;
  background: #f0f9ff;
}

.file-item.error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.file-status {
  margin-right: 12px;
  font-size: 12px;
}

.upload-progress {
  color: #409EFF;
}

.upload-success {
  color: #67c23a;
}

.upload-error {
  color: #f56c6c;
}

.file-actions {
  flex-shrink: 0;
}
</style>
