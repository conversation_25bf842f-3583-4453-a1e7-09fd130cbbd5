<template>
  <div class="news">
    <!-- 页面头部 -->
    <PageHeader tag="新闻资讯" />

    <!-- 新闻展示 -->
    <section class="news-showcase">
      <div class="container">
        <SectionHeader pageKey="news" blockKey="news_showcase" />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchNews">重试</button>
          </div>
        </div>

        <!-- 新闻列表 -->
        <div v-else>
          <!-- 新闻分类筛选 -->
          <div class="news-filters">
            <div class="filter-tabs">
              <button v-for="newsCategory in newsCategories" :key="newsCategory.key"
                :class="['filter-tab', { active: activeFilter === newsCategory.key }]"
                @click="setActiveFilter(newsCategory.key)">
                {{ newsCategory.name }}
              </button>
            </div>
          </div>

          <!-- 无新闻状态 -->
          <div v-if="news.length === 0" class="no-news">
            <div class="no-news-content">
              <i class="fas fa-newspaper"></i>
              <h3>暂无相关新闻</h3>
              <p>该分类下暂时没有新闻，请选择其他分类查看</p>
              <button class="btn btn-primary" @click="setActiveFilter('all')">
                查看全部新闻
              </button>
            </div>
          </div>

          <!-- 新闻列表 -->
          <div v-else class="news-grid">
            <div v-for="(newsItem, index) in news" :key="newsItem.id" @click="goToDetail(newsItem.id)" class="news-card"
              data-aos="fade-up" :data-aos-delay="index * 100">
              <div class="news-image">
                <img v-if="newsItem.thumbnail" :src="newsItem.thumbnail" :alt="newsItem.title" />
                <div v-else class="no-image">
                  <i class="fas fa-image"></i>
                  <p>暂无图片</p>
                </div>
                <div class="news-category">
                  <span class="category-badge" :style="getCategoryStyle(newsItem.category)">
                    {{ getCategoryName(newsItem.category) }}
                  </span>
                </div>
                <div class="news-overlay">
                  <div class="overlay-content">
                    <button class="view-details-btn">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
              <div class="news-content">
                <h3 class="news-title">{{ newsItem.title }}</h3>
                <p class="news-summary">{{ newsItem.summary }}</p>
                <div class="news-meta">
                  <span class="news-date">
                    <i class="fas fa-calendar-alt"></i>
                    {{ formatDate(newsItem.created_at) }}
                  </span>
                  <span class="news-views">
                    <i class="fas fa-eye"></i>
                    {{ newsItem.view_count || 0 }} 次浏览
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <Pagination v-if="pagination.pages > 1" :current-page="pagination.page" :total-pages="pagination.pages"
            :total-items="pagination.total" @page-change="handlePageChange" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import Pagination from '@/components/common/Pagination.vue'
import api, { API } from '@/api/index.js'
import { formatDateTime } from '@/utils/dateFormat'
import { isMobile } from '@/utils/deviceDetect'

export default {
  name: 'News',
  components: {
    PageHeader,
    SectionHeader,
    Pagination
  },
  data() {
    return {
      news: [],
      newsCategories: [], // 动态新闻分类
      loading: false,
      error: null,
      activeFilter: '',
      pagination: {
        page: 1,
        limit: 9,
        total: 0,
        pages: 0
      }
    }
  },
  mounted() {
    this.pagination.limit = isMobile() ? 9 : 9;
    this.fetchNewsCategories()
    this.fetchNews()
  },
  methods: {
    // 获取新闻分类配置
    async fetchNewsCategories() {
      try {
        const response = await API.getSystemConfig('news_categories')
        if (response.success) {
          // 添加"全部新闻"选项
          this.newsCategories = [
            { key: '', name: '全部新闻' },
            ...(response.data || [])
          ]
        }
      } catch (error) {
        console.error('获取新闻分类配置失败:', error)
      }
    },

    async fetchNews() {
      try {
        this.loading = true
        this.error = null

        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        // 添加筛选参数
        if (this.activeFilter !== 'all') {
          params.category = this.activeFilter
        }

        const response = await api.get('/api/front/news', { params })

        if (response.success) {
          this.news = response.data.news
          this.pagination = response.data.pagination
        } else {
          throw new Error(response.message || '获取数据失败')
        }

      } catch (error) {
        console.error('获取新闻列表失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    setActiveFilter(filter) {
      this.activeFilter = filter
      this.pagination.page = 1
      this.fetchNews()
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.fetchNews()
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    goToDetail(id) {
      window.open(`/news/${id}`, '_blank')
      // this.$router.push(`/news/${id}`)
    },

    formatDate(dateString) {
      return formatDateTime(dateString, 'YYYY-MM-DD')
    },

    // 获取分类中文名称
    getCategoryName(categoryKey) {
      const category = this.newsCategories.find(cat => cat.key === categoryKey);
      return category ? category.name : categoryKey;
    },

    // 根据分类key从颜色池获取样式
    getCategoryStyle(categoryKey) {
      // 新闻分类颜色池（10个精心挑选的颜色）
      const colorPool = [
        '#1890ff', // 蓝色 - 适合公司新闻
        '#52c41a', // 绿色 - 适合行业资讯
        '#fa541c', // 橙色 - 适合热点专题
        '#722ed1', // 紫色 - 适合学采暖
        '#eb2f96', // 粉色 - 适合装采暖
        '#13c2c2', // 青色
        '#faad14', // 金色
        '#f5222d', // 红色
        '#2f54eb', // 深蓝
        '#a0d911'  // 浅绿
      ];

      const index = this.newsCategories.findIndex(cat => cat.key === categoryKey);

      return {
        backgroundColor: colorPool[index],
        color: '#fff',
        border: 'none'
      };
    },
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.news {
  min-height: 100vh;
}

.news-showcase {
  padding: 66px 0;
  background: @bg-light;
}

.container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @spacing-lg;
}

// 加载和错误状态
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-spinner,
.error-content {
  text-align: center;

  i {
    font-size: 48px;
    color: @primary-color;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 筛选器
.news-filters {
  margin-bottom: @spacing-xxl;
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 12px 30px;
  border: 2px solid @border-color;
  background: @bg-color;
  color: @text-light;
  border-radius: 50px;
  cursor: pointer;
  transition: @transition;
  font-weight: @font-weight-medium;

  &.active {
    border-color: @primary-color;
    color: @primary-color;
  }

  &:hover {
    border-color: @primary-color;
    color: @primary-color;
  }
}

// 无新闻状态
.no-news {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.no-news-content {
  text-align: center;

  i {
    font-size: 64px;
    color: @text-lighter;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 新闻网格
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: @spacing-xl;
  margin-bottom: @spacing-xxl;
}

// 新闻卡片
.news-card {
  background: @bg-color;
  border-radius: @border-radius-large;
  box-shadow: @shadow;
  overflow: hidden;
  transition: @transition;
  cursor: pointer;

  &:hover {
    box-shadow: @shadow-hover;
    transform: translateY(-5px);
  }
}

.news-image {
  position: relative;
  height: 220px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: @transition;
  }

  &:hover img {
    transform: scale(1.05);
  }

  .no-image {
    display: flex;
    width: 100%;
    height: 100%;
    background: @bg-light;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: @text-lighter;

    i {
      font-size: 48px;
      margin-bottom: 12px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .news-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .overlay-content {
      text-align: center;
      color: white;

      .btn {
        padding: 8px 20px;
        border: 2px solid white;
        background: transparent;
        color: white;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: white;
          color: @primary-color;
        }
      }
    }
  }

  &:hover .news-overlay {
    opacity: 1;
  }

}

.news-category {
  position: absolute;
  top: @spacing-md;
  left: @spacing-md;
  z-index: 99;
}

.category-badge {
  display: inline-block;
  padding: @spacing-xs @spacing-md;
  border-radius: @border-radius-small;
  font-size: @font-size-xs;
  font-weight: @font-weight-medium;
  color: white;

  &.company {
    background: #1976d2;
  }

  &.industry {
    background: #388e3c;
  }

  &.hot {
    background: #f57c00;
  }

  &.learn {
    background: #7b1fa2;
  }

  &.install {
    background: #d32f2f;
  }

  &.default {
    background: @text-lighter;
  }
}

.news-content {
  padding: @spacing-lg;
}

.news-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-medium;
  color: @text-color;
  margin-bottom: @spacing-sm;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  color: @text-light;
  line-height: 1.6;
  margin-bottom: @spacing-md;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 14px;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: @font-size-sm;
  color: @text-lighter;

  span {
    display: flex;
    align-items: center;
    gap: @spacing-xs;

    i {
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: @screen-md) {

  .news-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: @spacing-lg;
  }

  .filter-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: @spacing-sm;
  }

  .filter-tab {
    flex-shrink: 0;
    padding: @spacing-sm @spacing-lg;
  }

  .news-image {
    height: 180px;
  }
}

@media (max-width: @screen-sm) {
  .container {
    padding: 0 @spacing-md;
  }

  .news-image {
    height: 180px;
  }

  .news-showcase {
    padding: 40px 0;
  }

  .news-grid {
    grid-template-columns: 1fr;
  }

  .news-content {
    padding: @spacing-md;
  }

  .news-meta {
    flex-direction: row;
    align-items: flex-start;
    gap: @spacing-xs;
  }
}
</style>
