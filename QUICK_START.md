# 雅克菲网站 Vue 2版本 - 快速启动指南

## 🚀 快速开始

### 1. 安装依赖
```bash
cd demo4
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```
项目将在 http://localhost:8082 启动

### 3. 构建生产版本
```bash
npm run build
```

## ✅ 问题解决

### 依赖冲突问题已解决
- ✅ 修复了 swiper 版本冲突 (使用 swiper@5.4.5)
- ✅ 修复了 CSS 导入路径
- ✅ 修复了 webpack 配置问题

### 当前配置
- **开发服务器端口**: 8082 (避免端口冲突)
- **Swiper版本**: 5.4.5 (兼容 vue-awesome-swiper@4.1.1)
- **构建工具**: Webpack 5

## 📁 项目结构
```
demo4/
├── src/
│   ├── main.js              # 入口文件
│   ├── App.vue              # 根组件
│   ├── router/index.js      # 路由配置
│   ├── views/               # 页面组件
│   │   ├── Home.vue         # 首页
│   │   ├── Products.vue     # 产品中心
│   │   ├── Academy.vue      # 采暖学堂
│   │   ├── Cases.vue        # 工程案例
│   │   ├── Stores.vue       # 门店查询
│   │   └── About.vue        # 关于我们
│   ├── components/          # 组件
│   │   ├── layout/          # 布局组件
│   │   │   ├── Header.vue   # 导航栏(支持二级菜单)
│   │   │   └── Footer.vue   # 页脚
│   │   └── common/          # 通用组件
│   │       ├── PageHeader.vue
│   │       ├── SectionHeader.vue
│   │       ├── ProductCard.vue
│   │       └── BackToTop.vue
│   └── assets/styles/       # 样式文件
│       ├── variables.less   # Less变量
│       ├── base.less       # 基础样式
│       └── main.less       # 主样式
├── public/index.html        # HTML模板
├── package.json            # 项目配置
├── webpack.config.js       # 构建配置
└── README.md              # 详细文档
```

## 🎯 功能特性

### 页面功能
- ✅ **首页**: Hero Banner、核心服务、智能控制、产品展示、新闻资讯
- ✅ **产品中心**: 产品分类、列表展示、详情查看、优势介绍
- ✅ **采暖学堂**: 学习模块、热门课程、技能大赛、专家专栏
- ✅ **工程案例**: 案例展示、服务流程、客户评价、项目统计
- ✅ **门店查询**: 门店信息、地区筛选、联系方式
- ✅ **关于我们**: 公司简介、企业文化、技术创新、发展历程

### 技术特性
- ✅ **响应式设计**: 完美适配PC、平板、手机
- ✅ **二级菜单**: 导航栏支持下拉菜单
- ✅ **图片懒加载**: vue-lazyload优化加载性能
- ✅ **滚动动画**: AOS库提供丰富的动画效果
- ✅ **数字动效**: vue-count-to数字滚动动画
- ✅ **单页应用**: Vue Router实现无刷新导航

## 🛠️ 开发命令

```bash
# 安装依赖
npm install

# 开发环境 (热重载)
npm run dev

# 生产构建
npm run build

# 代码检查
npm run lint

# 使用部署脚本
./deploy.sh dev   # 开发环境
./deploy.sh prod  # 生产环境
```

## 🌐 访问地址

- **开发环境**: http://localhost:8082
- **生产环境**: 构建后部署到您的Web服务器

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+ (需要polyfill)

## 🎨 设计规范

- **主色调**: #D80514 (企业红)
- **字体**: Noto Sans SC
- **容器宽度**: 1200px
- **响应式断点**: 768px, 1024px, 1200px

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js版本 >= 12.0.0
2. npm版本 >= 6.0.0
3. 端口8081是否被占用
4. 防火墙设置

---

🎉 **项目已成功迁移到Vue 2技术栈！**

现在您可以享受现代化的开发体验，包括热重载、组件化开发、Less预处理器等功能。
