# API 重构说明

## 概述

本次重构将原本分散在各个API文件中的axios配置代码抽离到统一的`http.js`文件中，实现了代码复用和统一管理。

## 文件结构

```
src/api/
├── http.js                    # 统一的HTTP请求配置
├── index.js                   # 主入口文件，导出所有API模块
├── energyCases.js             # 节能案例API
├── dealers.js                 # 经销商API
├── news.js                    # 新闻资讯API
├── resource-library.js        # 资料库API
├── video-tutorials.js         # 视频教程API
├── heating-knowledge.js       # 采暖知识API
└── README.md                  # 本说明文档
```

## 核心改进

### 1. 统一HTTP配置 (http.js)

- **axios实例配置**：统一的baseURL、timeout、headers配置
- **请求拦截器**：自动添加认证token
- **响应拦截器**：统一错误处理和消息提示
- **封装常用方法**：提供get、post、put、patch、delete方法

### 2. 简化API文件

每个API文件现在只需要：
```javascript
import { request } from './http'

export const moduleName = {
  getList(params = {}) {
    return request.get('/api/endpoint', params)
  },
  
  create(data) {
    return request.post('/api/endpoint', data)
  }
  // ... 其他方法
}
```

### 3. 统一导出 (index.js)

所有API模块通过index.js统一导出，方便在组件中使用：
```javascript
import api from '@/api'

// 使用方式
api.energyCases.getList()
api.dealers.create(data)
```

## 使用方式

### 在Vue组件中使用

```javascript
import api from '@/api'

export default {
  methods: {
    async loadData() {
      try {
        const response = await api.energyCases.getList({
          page: 1,
          limit: 10
        })
        this.dataList = response.data.cases
      } catch (error) {
        // 错误已在http.js中统一处理
        console.error('加载数据失败:', error)
      }
    }
  }
}
```

### 直接使用request方法

```javascript
import { request } from '@/api/http'

// 直接使用request方法
const response = await request.get('/api/custom-endpoint', { param: 'value' })
const result = await request.post('/api/custom-endpoint', { data: 'value' })
```

## 优势

1. **代码复用**：消除了重复的axios配置代码
2. **统一管理**：所有HTTP配置集中在一个文件中
3. **易于维护**：修改配置只需要改一个地方
4. **类型安全**：统一的请求方法减少了错误
5. **更好的错误处理**：统一的错误处理逻辑

## 注意事项

1. 所有API调用都会自动添加认证token
2. 错误信息会自动显示给用户
3. 401错误会自动跳转到登录页
4. 请求超时时间设置为10秒
