const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取所有可用的图片标签
router.get('/tags', asyncHandler(async (req, res) => {
  const tagsQuery = `
    SELECT DISTINCT tag 
    FROM qr_codes 
    WHERE status = 1 AND tag IS NOT NULL AND tag != ''
    ORDER BY tag ASC
  `;

  const tags = await query(tagsQuery);

  res.json({
    success: true,
    message: '获取图片标签成功',
    data: tags.map(row => row.tag)
  });
}));

// 按标签获取图片列表
router.get('/by-tag/:tag', asyncHandler(async (req, res) => {
  const { tag } = req.params;
  const {
    page = 1,
    limit = 20
  } = req.query;

  // 构建查询条件 - 只返回启用状态的图片
  const whereConditions = ['status = 1'];
  const queryParams = [];

  if (tag && tag !== 'all') {
    whereConditions.push('tag = ?');
    queryParams.push(tag);
  }

  const whereClause = 'WHERE ' + whereConditions.join(' AND ');

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM qr_codes ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取图片列表
  const imagesQuery = `
    SELECT id, image_url, description, tag, sort_order, created_at
    FROM qr_codes
    ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const images = await query(imagesQuery, queryParams);

  res.json({
    success: true,
    message: '获取图片列表成功',
    data: {
      images,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取所有图片列表（不分标签）
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    tag = ''
  } = req.query;

  // 构建查询条件 - 只返回启用状态的图片
  let whereConditions = ['status = 1'];
  let queryParams = [];

  if (tag && tag !== '') {
    whereConditions.push('tag = ?');
    queryParams.push(tag);
  }

  const whereClause = 'WHERE ' + whereConditions.join(' AND ');

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM qr_codes ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取图片列表
  const imagesQuery = `
    SELECT id, image_url, description, tag, sort_order, created_at
    FROM qr_codes
    ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const images = await query(imagesQuery, queryParams);

  res.json({
    success: true,
    message: '获取图片列表成功',
    data: {
      images,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取单个图片详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query(
    'SELECT id, image_url, description, tag, sort_order, created_at FROM qr_codes WHERE id = ? AND status = 1',
    [id]
  );

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '图片不存在或已下线'
    });
  }

  res.json({
    success: true,
    message: '获取图片详情成功',
    data: rows[0]
  });
}));

module.exports = router;
