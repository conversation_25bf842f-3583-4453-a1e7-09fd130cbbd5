import { request } from './http'

// 公司简介API
export const companyProfile = {
  // 获取公司简介
  get() {
    return request.get('/api/company-profile')
  },

  // 更新公司简介
  update(data) {
    return request.put('/api/company-profile', data)
  }
}

// 公司荣誉API
export const companyHonors = {
  // 获取公司荣誉列表
  getList(params = {}) {
    return request.get('/api/company-honors', params)
  },

  // 获取公司荣誉详情
  getDetail(id) {
    return request.get(`/api/company-honors/${id}`)
  },

  // 创建公司荣誉
  create(data) {
    return request.post('/api/company-honors', data)
  },

  // 更新公司荣誉
  update(id, data) {
    return request.put(`/api/company-honors/${id}`, data)
  },

  // 删除公司荣誉
  delete(id) {
    return request.delete(`/api/company-honors/${id}`)
  }
}

// 发展历程API
export const developmentHistory = {
  // 获取发展历程列表
  getList(params = {}) {
    return request.get('/api/development-history', params)
  },

  // 获取发展历程详情
  getDetail(id) {
    return request.get(`/api/development-history/${id}`)
  },

  // 创建发展历程
  create(data) {
    return request.post('/api/development-history', data)
  },

  // 更新发展历程
  update(id, data) {
    return request.put(`/api/development-history/${id}`, data)
  },

  // 删除发展历程
  delete(id) {
    return request.delete(`/api/development-history/${id}`)
  }
}

// 二维码API
export const qrCodes = {
  // 获取二维码列表
  getList(params = {}) {
    return request.get('/api/qr-codes', params)
  },

  // 获取二维码详情
  getDetail(id) {
    return request.get(`/api/qr-codes/${id}`)
  },

  // 创建二维码
  create(data) {
    return request.post('/api/qr-codes', data)
  },

  // 更新二维码
  update(id, data) {
    return request.put(`/api/qr-codes/${id}`, data)
  },

  // 删除二维码
  delete(id) {
    return request.delete(`/api/qr-codes/${id}`)
  }
}

// 联系信息API
export const contactInformation = {
  // 获取联系信息列表
  getList(params = {}) {
    return request.get('/api/contact-information', params)
  },

  // 获取联系信息详情
  getDetail(id) {
    return request.get(`/api/contact-information/${id}`)
  },

  // 创建联系信息
  create(data) {
    return request.post('/api/contact-information', data)
  },

  // 更新联系信息
  update(id, data) {
    return request.put(`/api/contact-information/${id}`, data)
  },

  // 删除联系信息
  delete(id) {
    return request.delete(`/api/contact-information/${id}`)
  }
}

// 默认导出
export default {
  companyProfile,
  companyHonors,
  developmentHistory,
  qrCodes,
  contactInformation
}
