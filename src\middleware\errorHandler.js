// 统一错误处理中间件
const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // 默认错误信息
  let error = {
    success: false,
    message: '服务器内部错误',
    code: 500
  };

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    error = {
      success: false,
      message: 'Token无效',
      code: 401
    };
  } else if (err.name === 'TokenExpiredError') {
    error = {
      success: false,
      message: 'Token已过期',
      code: 401
    };
  }
  
  // 数据库错误
  else if (err.code === 'ER_DUP_ENTRY') {
    error = {
      success: false,
      message: '数据已存在',
      code: 400
    };
  } else if (err.code === 'ER_NO_SUCH_TABLE') {
    error = {
      success: false,
      message: '数据表不存在',
      code: 500
    };
  }
  
  // 自定义错误
  else if (err.statusCode) {
    error = {
      success: false,
      message: err.message,
      code: err.statusCode
    };
  }

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    error.stack = err.stack;
  }

  res.status(error.code).json(error);
};

// 创建自定义错误
class CustomError extends Error {
  constructor(message, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
    this.name = 'CustomError';
  }
}

// 异步错误处理包装器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  CustomError,
  asyncHandler
};
