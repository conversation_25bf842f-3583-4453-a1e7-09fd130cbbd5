const express = require('express');
const { query } = require('../../config/database');
const { CustomError, asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取联系信息列表
router.get('/', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const offset = (page - 1) * limit;

  // 获取联系信息总数
  const countResult = await query(`
    SELECT COUNT(*) as total
    FROM contact_information
    WHERE status = 1
  `);
  const total = countResult[0].total;

  // 获取联系信息列表
  const contacts = await query(`
    SELECT
      id,
      name,
      image_url,
      address,
      phone,
      fax,
      service_phone,
      sort_order,
      created_at,
      updated_at
    FROM contact_information
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `);

  // 计算分页信息
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    message: '获取联系信息成功',
    data: {
      contacts,
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1
      }
    }
  });
}));

// 获取单个联系信息详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(id)) {
    throw new CustomError('无效的联系信息ID', 400);
  }

  try {
    const contacts = await query(`
      SELECT 
        id,
        name,
        image_url,
        address,
        phone,
        fax,
        service_phone,
        sort_order,
        created_at,
        updated_at
      FROM contact_information 
      WHERE id = ? AND status = 1
    `, [id]);

    if (contacts.length === 0) {
      throw new CustomError('联系信息不存在', 404);
    }

    const contact = contacts[0];

    res.json({
      success: true,
      message: '获取联系信息详情成功',
      data: {
        contact
      }
    });

  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error('获取联系信息详情失败:', error);
    throw new CustomError('获取联系信息详情失败', 500);
  }
}));

module.exports = router;
