const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const menuRoutes = require('./routes/menus');
const frontendMenuRoutes = require('./routes/frontend-menus');
const productCategoryRoutes = require('./routes/product-categories');
const productRoutes = require('./routes/products');
const energyCasesRoutes = require('./routes/energy-cases');
const newsRoutes = require('./routes/news');
const dealersRoutes = require('./routes/dealers');
const resourceLibraryRoutes = require('./routes/resource-library');
const videoTutorialsRoutes = require('./routes/video-tutorials');
const heatingKnowledgeRoutes = require('./routes/heating-knowledge');
const uploadRoutes = require('./routes/upload');
const bannersRoutes = require('./routes/banners');
// 关于我们相关路由
const companyProfileRoutes = require('./routes/company-profile');
const companyHonorsRoutes = require('./routes/company-honors');
const developmentHistoryRoutes = require('./routes/development-history');
const qrCodesRoutes = require('./routes/qr-codes');
const contactInformationRoutes = require('./routes/contact-information');
const contactUsRoutes = require('./routes/contact-us');
const pageContentBlocksRoutes = require('./routes/page-content-blocks');
const systemConfigsRoutes = require('./routes/system-configs');
// 前台API路由
const frontRoutes = require('./routes/front');
const { errorHandler } = require('./middleware/errorHandler');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  crossOriginEmbedderPolicy: false
}));

// 跨域配置
app.use(cors({
  origin: "*", // 允许所有域的请求
  credentials: true
}));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务（提供上传的图片访问）
app.use('/uploads', (req, res, next) => {
  // 为静态文件添加CORS头
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  next();
}, express.static(path.join(__dirname, '../uploads')));

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/menus', menuRoutes);
app.use('/api/frontend-menus', frontendMenuRoutes);
app.use('/api/product-categories', productCategoryRoutes);
app.use('/api/products', productRoutes);
app.use('/api/energy-cases', energyCasesRoutes);
app.use('/api/news', newsRoutes);
app.use('/api/dealers', dealersRoutes);
app.use('/api/resource-library', resourceLibraryRoutes);
app.use('/api/video-tutorials', videoTutorialsRoutes);
app.use('/api/heating-knowledge', heatingKnowledgeRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/banners', bannersRoutes);
// 关于我们相关路由
app.use('/api/company-profile', companyProfileRoutes);
app.use('/api/company-honors', companyHonorsRoutes);
app.use('/api/development-history', developmentHistoryRoutes);
app.use('/api/qr-codes', qrCodesRoutes);
app.use('/api/contact-information', contactInformationRoutes);
app.use('/api/contact-us', contactUsRoutes);
app.use('/api/page-content-blocks', pageContentBlocksRoutes);
app.use('/api/system-configs', systemConfigsRoutes);

// 前台API路由（无需认证）
app.use('/api/front', frontRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'airfit-backend'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 错误处理中间件
app.use(errorHandler);

app.listen(PORT, () => {
  console.log(`🚀 雅克菲后端服务启动成功`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🕐 启动时间: ${new Date().toLocaleString()}`);
});

module.exports = app;
