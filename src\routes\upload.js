const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名：时间戳 + 随机数 + 原扩展名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 获取上传类型参数，默认为图片
  const uploadType = req.query.type || 'image';

  if (uploadType === 'image') {
    // 图片上传：只允许图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new CustomError('只能上传图片文件', 400), false);
    }
  } else if (uploadType === 'document') {
    // 文档上传：允许PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX等
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new CustomError('只能上传PDF、Word、Excel、PowerPoint或文本文件', 400), false);
    }
  } else if (uploadType === 'any') {
    // 任意文件类型（用于资料库等）
    const allowedTypes = [
      // 图片
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      // 文档
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      // 文本
      'text/plain',
      // 压缩包
      'application/zip',
      'application/x-rar-compressed'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new CustomError('不支持的文件类型', 400), false);
    }
  } else {
    cb(new CustomError('无效的上传类型', 400), false);
  }
};

// 动态配置multer
const createUpload = (req) => {
  const uploadType = req.query.type || 'image';
  let fileSize;

  switch (uploadType) {
    case 'image':
      fileSize = 5 * 1024 * 1024; // 5MB
      break;
    case 'document':
      fileSize = 10 * 1024 * 1024; // 10MB
      break;
    case 'any':
      fileSize = 50 * 1024 * 1024; // 50MB
      break;
    default:
      fileSize = 2 * 1024 * 1024; // 默认2MB
  }

  return multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
      fileSize: fileSize,
      files: 1 // 一次只能上传一个文件
    }
  });
};

// 单文件上传接口
router.post('/', authenticateToken, (req, res) => {
  const upload = createUpload(req);
  const uploadType = req.query.type || 'image';

  upload.single('file')(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        let maxSize = '2MB';
        if (uploadType === 'document') {
          maxSize = '10MB';
        } else if (uploadType === 'any') {
          maxSize = '50MB';
        }
        return res.status(400).json({
          success: false,
          message: `文件大小不能超过${maxSize}`,
          code: 400
        });
      }
      return res.status(400).json({
        success: false,
        message: '文件上传失败: ' + err.message,
        code: 400
      });
    } else if (err) {
      return res.status(400).json({
        success: false,
        message: err.message || '文件上传失败',
        code: 400
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '没有选择文件',
        code: 400
      });
    }

    // 返回文件信息
    const fileUrl = `/uploads/${req.file.filename}`;
    res.json({
      success: true,
      message: '文件上传成功',
      data: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        size: req.file.size,
        url: fileUrl,
        fullUrl: `${req.protocol}://${req.get('host')}${fileUrl}`
      }
    });
  });
});

// 文件下载接口
router.get('/download/:filename', asyncHandler(async (req, res) => {
  const { filename } = req.params;

  // 验证文件名（防止路径遍历攻击）
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw new CustomError('无效的文件名', 400);
  }

  const filePath = path.join(uploadDir, filename);

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    throw new CustomError('文件不存在', 404);
  }

  // 获取文件信息
  const stats = fs.statSync(filePath);
  const fileExtension = path.extname(filename).toLowerCase();

  // 设置响应头强制下载
  res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
  res.setHeader('Content-Length', stats.size);

  // 根据文件扩展名设置Content-Type
  const mimeTypes = {
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.zip': 'application/zip',
    '.rar': 'application/x-rar-compressed',
    '.txt': 'text/plain',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif'
  };

  const contentType = mimeTypes[fileExtension] || 'application/octet-stream';
  res.setHeader('Content-Type', contentType);

  // 创建文件流并发送
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);

  fileStream.on('error', (error) => {
    console.error('文件下载错误:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: '文件下载失败'
      });
    }
  });
}));

// 删除文件接口
router.delete('/:filename', authenticateToken, asyncHandler(async (req, res) => {
  const { filename } = req.params;

  // 验证文件名（防止路径遍历攻击）
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw new CustomError('无效的文件名', 400);
  }

  const filePath = path.join(uploadDir, filename);

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    throw new CustomError('文件不存在', 404);
  }

  // 删除文件
  fs.unlinkSync(filePath);

  res.json({
    success: true,
    message: '文件删除成功'
  });
}));

module.exports = router;
