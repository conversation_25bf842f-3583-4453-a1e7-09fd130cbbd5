import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import { menuConfig, getFlatRoutes } from '@/config/menu';

Vue.use(VueRouter);

// 路由组件懒加载
const Login = () => import('@/views/Login.vue');
const Layout = () => import('@/layout/index.vue');

// 从菜单配置生成路由
const dynamicRoutes = getFlatRoutes(menuConfig);

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: dynamicRoutes
  },
  {
    path: '*',
    redirect: '/dashboard'
  }
];

const router = new VueRouter({
  mode: 'history',
  base: '/',
  routes
});

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 雅克菲管理后台`;
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    const token = store.getters.token;
    if (!token) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
    } else {
      // 已登录，检查用户信息
      if (!store.getters.userInfo) {
        // 获取用户信息
        store.dispatch('getUserInfo').then(() => {
          next();
        }).catch(() => {
          // 获取用户信息失败，清除token并跳转到登录页
          store.dispatch('logout');
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          });
        });
      } else {
        next();
      }
    }
  } else {
    // 不需要登录的页面
    if (to.path === '/login' && store.getters.token) {
      // 已登录用户访问登录页，跳转到首页
      next({ path: '/' });
    } else {
      next();
    }
  }
});

export default router;
