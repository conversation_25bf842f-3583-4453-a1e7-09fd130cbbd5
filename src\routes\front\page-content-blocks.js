const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler, CustomError } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取单个页面内容块
router.get('/:pageKey/:blockKey', asyncHandler(async (req, res) => {
  const { pageKey, blockKey } = req.params;

  if (!pageKey || !blockKey) {
    throw new CustomError('页面标识和内容块标识不能为空', 400);
  }

  const blocks = await query(`
    SELECT
      id,
      page_key,
      block_key,
      title,
      subtitle,
      description,
      main_image,
      sub_image,
      sort_order
    FROM page_content_blocks
    WHERE page_key = ? AND block_key = ? AND status = 1
  `, [pageKey, blockKey]);

  if (blocks.length === 0) {
    throw new CustomError('内容块不存在或已禁用', 404);
  }

  res.json({
    success: true,
    message: '获取内容块成功',
    data: blocks[0]
  });
}));

// 获取指定页面的所有内容块
router.get('/:pageKey', asyncHandler(async (req, res) => {
  const { pageKey } = req.params;

  if (!pageKey) {
    throw new CustomError('页面标识不能为空', 400);
  }

  const blocks = await query(`
    SELECT
      id,
      page_key,
      block_key,
      title,
      subtitle,
      description,
      main_image,
      sub_image,
      sort_order
    FROM page_content_blocks
    WHERE page_key = ? AND status = 1
    ORDER BY sort_order ASC, id ASC
  `, [pageKey]);

  res.json({
    success: true,
    message: '获取页面内容块列表成功',
    data: {
      blocks,
      total: blocks.length
    }
  });
}));

// 获取所有页面的内容块（用于管理后台）
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, pageKey, status } = req.query;
  const offset = (page - 1) * limit;

  let whereConditions = [];
  let params = [];

  // 页面筛选
  if (pageKey) {
    whereConditions.push('page_key = ?');
    params.push(pageKey);
  }

  // 状态筛选
  if (status !== undefined) {
    whereConditions.push('status = ?');
    params.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 查询内容块列表
  const blocks = await query(`
    SELECT
      id,
      page_key,
      block_key,
      title,
      subtitle,
      description,
      main_image,
      sub_image,
      sort_order,
      status,
      created_at,
      updated_at
    FROM page_content_blocks
    ${whereClause}
    ORDER BY page_key ASC, sort_order ASC, id ASC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `, params);

  // 查询总数
  const countResult = await query(`
    SELECT COUNT(*) as total 
    FROM page_content_blocks 
    ${whereClause}
  `, params);

  const total = countResult[0].total;
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    message: '获取内容块列表成功',
    data: {
      blocks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages
      }
    }
  });
}));

module.exports = router;
