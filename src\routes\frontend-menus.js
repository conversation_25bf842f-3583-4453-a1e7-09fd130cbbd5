const express = require('express');
const { query } = require('../config/database');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 构建菜单树形结构的辅助函数
function buildMenuTree(menus) {
  const menuMap = new Map();
  const rootMenus = [];

  // 创建菜单映射
  menus.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] });
  });

  // 构建树形结构
  menus.forEach(menu => {
    if (menu.parent_id === 0) {
      rootMenus.push(menuMap.get(menu.id));
    } else {
      const parent = menuMap.get(menu.parent_id);
      if (parent) {
        parent.children.push(menuMap.get(menu.id));
      }
    }
  });

  return rootMenus;
}

// 获取前端官网菜单列表（只返回启用的菜单）
router.get('/', asyncHandler(async (req, res) => {
  const sql = 'SELECT id, name, path, url, parent_id, sort_order FROM menus WHERE status = 1 ORDER BY parent_id ASC, sort_order ASC';
  
  const menus = await query(sql);
  
  // 构建树形结构
  const menuTree = buildMenuTree(menus);
  
  res.json({
    success: true,
    message: '获取菜单列表成功',
    data: {
      menus: menuTree,
      total: menus.length
    }
  });
}));

module.exports = router;
