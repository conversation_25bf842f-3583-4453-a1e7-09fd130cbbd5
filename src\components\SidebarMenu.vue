<template>
  <el-menu
    :default-active="$route.path"
    :collapse="collapse"
    :unique-opened="true"
    router
    class="sidebar-menu"
  >
    <template v-for="item in menuItems">
      <!-- 有子菜单的项 -->
      <el-submenu
        v-if="item.children && item.children.length > 0"
        :key="item.path"
        :index="item.path"
      >
        <template slot="title">
          <i v-if="item.meta.icon" :class="item.meta.icon"></i>
          <span>{{ item.meta.title }}</span>
        </template>
        
        <template v-for="child in item.children">
          <el-menu-item
            v-if="!child.meta.hidden"
            :key="child.path"
            :index="child.path"
          >
            <i v-if="child.meta.icon" :class="child.meta.icon"></i>
            <span slot="title">{{ child.meta.title }}</span>
          </el-menu-item>
        </template>
      </el-submenu>
      
      <!-- 没有子菜单的项 -->
      <el-menu-item
        v-else-if="!item.meta.hidden"
        :key="item.path"
        :index="item.path"
      >
        <i v-if="item.meta.icon" :class="item.meta.icon"></i>
        <span slot="title">{{ item.meta.title }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script>
import { menuConfig } from '@/config/menu'

export default {
  name: 'SidebarMenu',
  props: {
    collapse: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    menuItems() {
      // 过滤掉隐藏的菜单项
      return menuConfig.filter(item => !item.meta.hidden)
    }
  }
}
</script>

<style scoped>
.sidebar-menu {
  border: none;
  background-color: transparent;
}

.sidebar-menu .el-menu-item {
  color: #bfcbd9;
  background-color: transparent !important;
}

.sidebar-menu .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409eff !important;
  color: #fff;
}

.sidebar-menu .el-submenu__title {
  color: #bfcbd9;
  background-color: transparent !important;
}

.sidebar-menu .el-submenu__title:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff;
}

.sidebar-menu .el-submenu .el-menu-item {
  background-color: rgba(0, 0, 0, 0.1) !important;
  min-height: 45px;
}

.sidebar-menu .el-submenu .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar-menu .el-submenu .el-menu-item.is-active {
  background-color: #409eff !important;
}
</style>
