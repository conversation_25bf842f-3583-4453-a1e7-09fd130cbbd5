<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <i class="el-icon-s-home"></i>
          <span v-show="!sidebarCollapsed">雅克菲管理</span>
        </div>
      </div>
      
      <SidebarMenu :collapse="sidebarCollapsed" />
    </div>
    
    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="navbar">
        <div class="navbar-left">
          <el-button
            type="text"
            icon="el-icon-s-fold"
            @click="toggleSidebar"
          />
          
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.meta.title">
              {{ $route.meta.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="navbar-right">
          <!-- 用户信息 -->
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <i class="el-icon-user-solid"></i>
              {{ userInfo ? userInfo.real_name || userInfo.username : '用户' }}
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </div>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="changePasswordVisible"
      width="400px"
      @close="resetPasswordForm"
    >
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="changePasswordVisible = false">取消</el-button>
        <el-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import api from '@/api';
import SidebarMenu from '@/components/SidebarMenu.vue';

export default {
  name: 'Layout',
  components: {
    SidebarMenu
  },
  data() {
    return {
      changePasswordVisible: false,
      passwordLoading: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          {
            validator: (_rule, value, callback) => {
              if (value !== this.passwordForm.newPassword) {
                callback(new Error('两次输入密码不一致'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'sidebarCollapsed'])
  },
  methods: {
    ...mapActions(['logout', 'toggleSidebar']),
    
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人信息功能开发中');
          break;
        case 'changePassword':
          this.changePasswordVisible = true;
          break;
        case 'logout':
          this.handleLogout();
          break;
      }
    },
    
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        await this.logout();
        this.$success('退出登录成功');
        this.$router.push('/login');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败:', error);
        }
      }
    },
    
    async handleChangePassword() {
      try {
        await this.$refs.passwordForm.validate();
        
        this.passwordLoading = true;
        
        await api.auth.changePassword({
          oldPassword: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        });
        
        this.$success('密码修改成功，请重新登录');
        this.changePasswordVisible = false;
        
        // 退出登录
        await this.logout();
        this.$router.push('/login');
        
      } catch (error) {
        console.error('修改密码失败:', error);
        this.$error(error.message || '修改密码失败');
      } finally {
        this.passwordLoading = false;
      }
    },
    
    resetPasswordForm() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.$refs.passwordForm && this.$refs.passwordForm.clearValidate();
    }
  }
};
</script>

<style lang="less" scoped>
.layout-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 200px;
  background: #304156;
  transition: width 0.3s;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #434a50;
    
    .logo {
      display: flex;
      align-items: center;
      color: white;
      font-size: 18px;
      font-weight: 600;
      
      i {
        font-size: 24px;
        margin-right: 8px;
      }
    }
  }
  
  .sidebar-menu {
    border: none;
    background: #304156;

    /deep/ .el-menu-item {
      color: #bfcbd9;

      &:hover {
        background: #263445;
        color: #409EFF;
      }

      &.is-active {
        background: #409EFF;
        color: white;
      }
    }

    /deep/ .el-submenu {
      .el-submenu__title {
        color: #bfcbd9;
        background: #304156;

        &:hover {
          background: #263445;
          color: #409EFF;
        }
      }

      .el-menu {
        background: #1f2d3d;

        .el-menu-item {
          background: #1f2d3d;
          color: #bfcbd9;

          &:hover {
            background: #001528;
            color: #409EFF;
          }

          &.is-active {
            background: #409EFF;
            color: white;
          }
        }
      }

      &.is-opened {
        .el-submenu__title {
          color: #409EFF;
        }
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .breadcrumb {
      margin-left: 20px;
    }
  }
  
  .navbar-right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #606266;
      
      i {
        margin: 0 4px;
      }
      
      &:hover {
        color: #409EFF;
      }
    }
  }
}

.page-content {
  flex: 1;
  overflow: auto;
  background: #f0f2f5;
}
</style>
