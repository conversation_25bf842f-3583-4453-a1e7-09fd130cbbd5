<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>文档资料管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加资料
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="资料标题">
            <el-input v-model="searchForm.title" placeholder="请输入资料标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 120px;">
              <el-option v-for="resourceCategory in resourceCategories" :key="resourceCategory.key"
                :label="resourceCategory.name" :value="resourceCategory.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="已发布" :value="1" />
              <el-option label="草稿" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 资料表格 -->
      <el-table v-loading="loading" :data="resourceList" border class="resource-table">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="title" label="资料标题" min-width="200" />

        <el-table-column prop="summary" label="摘要" min-width="200" show-overflow-tooltip />

        <el-table-column label="分类" width="100" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getCategoryTagType(scope.row.category)">
              {{ getResourceCategoryName(scope.row.category) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="文件数量" width="100" align="center">
          <template slot-scope="scope">
            <el-tag size="mini" type="info">
              {{ scope.row.files ? scope.row.files.length : 0 }} 个文件
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>



        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" align="left">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button v-if="scope.row.files && scope.row.files.length > 0" size="mini" type="success"
              @click="handleDownloadAll(scope.row)">
              <i class="el-icon-download"></i>
              下载
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" />
      </div>
    </div>

    <!-- 添加/编辑资料对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
      <el-form ref="resourceForm" :model="resourceForm" :rules="resourceRules" label-width="100px">
        <el-form-item label="资料标题" prop="title">
          <el-input v-model="resourceForm.title" placeholder="请输入资料标题" />
        </el-form-item>

        <el-form-item label="摘要">
          <el-input v-model="resourceForm.summary" type="textarea" :rows="3" placeholder="请输入资料摘要" />
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-select v-model="resourceForm.category" placeholder="请选择分类" style="width: 100%;">
            <el-option v-for="resourceCategory in resourceCategories" :key="resourceCategory.key"
              :label="resourceCategory.name" :value="resourceCategory.key" />
          </el-select>
        </el-form-item>

        <el-form-item label="PDF文件">
          <FileUploader v-model="resourceForm.files" :multiple="false" accept=".pdf" tip="最多一个文件，支持PDF且不超过50MB"
            :max-size="50" @success="handleFileSuccess" @error="handleFileError" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="resourceForm.status">
                <el-radio :label="1">已发布</el-radio>
                <el-radio :label="0">草稿</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序序号">
              <el-input-number v-model="resourceForm.sort_order" :min="0" :max="9999" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { resourceLibrary } from '@/api/resource-library'
import { systemConfigs } from '@/api/system-configs'
import FileUploader from '@/components/FileUploader.vue'

export default {
  name: 'ResourceLibraryManagement',
  components: {
    FileUploader
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      resourceList: [],
      resourceCategories: [], // 动态资料分类配置
      searchForm: {
        title: '',
        category: '',
        status: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      resourceForm: {
        title: '',
        summary: '',
        category: '',
        files: [],
        status: 1,
        sort_order: 0
      },
      resourceRules: {
        title: [
          { required: true, message: '请输入资料标题', trigger: 'blur' },
          { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑资料' : '添加资料'
    }
  },

  mounted() {
    this.loadResourceCategories()
    this.loadResourceList()
  },

  methods: {
    // 获取资料分类配置
    async loadResourceCategories() {
      try {
        const response = await systemConfigs.getByKey('resource_categories')
        if (response.success && response.data) {
          this.resourceCategories = response.data
          // 设置默认分类为第一个分类
          if (this.resourceCategories.length > 0 && !this.resourceForm.category) {
            this.resourceForm.category = this.resourceCategories[0].key
          }
        }
      } catch (error) {
        console.error('获取资料分类配置失败:', error)
        this.$message.error('获取资料分类配置失败')
      }
    },

    // 根据key获取资料分类中文名称
    getResourceCategoryName(categoryKey) {
      const category = this.resourceCategories.find(cat => cat.key === categoryKey)
      return category ? category.name : categoryKey
    },

    // 加载资料库列表
    async loadResourceList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          title: this.searchForm.title,
          category: this.searchForm.category,
          status: this.searchForm.status
        }

        const response = await resourceLibrary.getList(params)
        if (response.success) {
          this.resourceList = response.data.resources
          this.pagination.total = response.data.pagination.total_items
        }
      } catch (error) {
        console.error('加载资料库列表失败:', error)
        this.$message.error('加载资料库列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadResourceList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        category: '',
        status: ''
      }
      this.pagination.current = 1
      this.loadResourceList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadResourceList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadResourceList()
    },

    // 添加资料
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.dialogVisible = true
    },

    // 编辑资料
    handleEdit(row) {
      this.isEdit = true
      this.editId = row.id
      this.resourceForm = {
        title: row.title,
        summary: row.summary || '',
        category: row.category || '其他',
        files: row.files || [],
        status: row.status,
        sort_order: row.sort_order
      }

      // 设置文件列表
      this.fileList = (row.files || []).map((file, index) => ({
        name: file.name || `文件${index + 1}.pdf`,
        url: file.url,
        uid: index
      }))

      this.dialogVisible = true
    },

    // 删除资料
    handleDelete(row) {
      this.$confirm(`确定要删除资料"${row.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await resourceLibrary.delete(row.id)
          if (response.success) {
            this.$message.success('删除成功')
            this.loadResourceList()
          }
        } catch (error) {
          console.error('删除资料失败:', error)
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        const response = await resourceLibrary.update(row.id, {
          title: row.title,
          summary: row.summary,
          files: row.files,
          status: row.status,
          sort_order: row.sort_order
        })
        if (response.success) {
          this.$message.success('状态更新成功')
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 文件上传成功回调
    handleFileSuccess(fileData) {
      console.log('文件上传成功:', fileData)
    },

    // 文件上传失败回调
    handleFileError(error) {
      console.error('文件上传错误:', error)
    },

    // 提交表单
    handleSubmit() {
      this.$refs.resourceForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const formData = { ...this.resourceForm }

            let response
            if (this.isEdit) {
              response = await resourceLibrary.update(this.editId, formData)
            } else {
              response = await resourceLibrary.create(formData)
            }

            if (response.success) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功')
              this.dialogVisible = false
              this.loadResourceList()
            }
          } catch (error) {
            console.error('提交失败:', error)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      this.resourceForm = {
        title: '',
        summary: '',
        category: this.resourceCategories.length > 0 ? this.resourceCategories[0].key : '',
        files: [],
        status: 1,
        sort_order: 0
      }
      this.fileList = []
      if (this.$refs.resourceForm) {
        this.$refs.resourceForm.resetFields()
      }
    },

    // 获取分类标签类型
    getCategoryTagType(categoryKey) {
      // 根据分类索引分配不同的标签类型
      const index = this.resourceCategories.findIndex(cat => cat.key === categoryKey)
      const tagTypes = ['primary', 'warning', 'success', 'info', 'danger',]
      return index >= 0 ? tagTypes[index % tagTypes.length] : 'info'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 获取文件下载URL
    getFileDownloadUrl(fileUrl) {
      if (!fileUrl) return '#'

      // 提取文件名
      let filename = ''
      if (fileUrl.startsWith('/uploads/')) {
        filename = fileUrl.replace('/uploads/', '')
      } else if (fileUrl.includes('/uploads/')) {
        filename = fileUrl.split('/uploads/')[1]
      } else {
        // 如果不是uploads路径，直接返回原URL
        return fileUrl
      }

      // 构建下载API URL
      const baseUrl = process.env.NODE_ENV === 'production'
        ? 'https://api.airfit.cn'
        : 'http://localhost:3333'

      return `${baseUrl}/api/upload/download/${filename}`
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 B'

      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },

    // 下载单个文件
    async downloadFile(file) {
      try {
        const downloadUrl = this.getFileDownloadUrl(file.url)

        // 获取token
        const token = localStorage.getItem('token')

        // 使用fetch下载文件
        const response = await fetch(downloadUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (!response.ok) {
          throw new Error(`下载失败: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = file.name || '下载文件'

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        return true
      } catch (error) {
        console.error('文件下载失败:', error)
        this.$message.error(`文件 "${file.name}" 下载失败`)
        return false
      }
    },

    // 下载全部文件
    handleDownloadAll(row) {
      if (!row.files || row.files.length === 0) {
        this.$message.warning('该资料没有附件可下载')
        return
      }

      this.$confirm(`确定要下载 "${row.title}" 的全部 ${row.files.length} 个附件吗？`, '批量下载', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        // 显示下载进度
        const loading = this.$loading({
          lock: true,
          text: `正在下载文件 (0/${row.files.length})`,
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        let downloadedCount = 0
        let successCount = 0
        const totalFiles = row.files.length

        // 循环下载每个文件
        for (let i = 0; i < row.files.length; i++) {
          const file = row.files[i]

          // 更新进度
          loading.text = `正在下载文件 (${downloadedCount + 1}/${totalFiles}): ${file.name}`


          // 下载文件
          const success = await this.downloadFile(file)
          if (success) {
            successCount++
          }

          downloadedCount++

          // 添加延迟避免浏览器阻止
          if (i < row.files.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        }

        // 下载完成
        loading.close()

        if (successCount === totalFiles) {
          this.$message.success(`已成功下载全部 ${totalFiles} 个文件`)
        } else if (successCount > 0) {
          this.$message.warning(`成功下载 ${successCount} 个文件，${totalFiles - successCount} 个文件下载失败`)
        } else {
          this.$message.error('所有文件下载失败')
        }
      }).catch(() => {
        // 用户取消下载
      })
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.resource-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.file-upload-section {
  width: 100%;
}

/* 表格样式优化 */
.resource-table .el-table__header {
  background-color: #f5f7fa;
}

.resource-table .el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}
</style>
