<template>
  <nav class="breadcrumb-nav">
    <div class="container">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <router-link to="/">
            <i class="fas fa-home"></i>
            首页
          </router-link>
        </li>
        <li v-for="(item, index) in breadcrumbItems" :key="index" class="breadcrumb-item"
          :class="{ active: index === breadcrumbItems.length - 1 }">
          <router-link v-if="item.path && index !== breadcrumbItems.length - 1" :to="item.path">
            {{ item.name }}
          </router-link>
          <span v-else>{{ item.name }}</span>
        </li>
      </ol>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'Breadcrumb',
  props: {
    items: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    breadcrumbItems() {
      return this.items
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.breadcrumb-nav {
  background: #f8f9fa;
  padding: 15px 0;
  border-bottom: 1px solid #e9ecef;
  padding-top: 90px;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .breadcrumb {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 14px;

    .breadcrumb-item {
      display: flex;
      align-items: center;

      a {
        color: @primary-color;
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          opacity: 0.8;
        }

        i {
          margin-right: 5px;
        }
      }

      span {
        color: #6c757d;
      }

      &.active span {
        color: #495057;
        font-weight: 500;
      }

      &:not(:last-child)::after {
        content: '/';
        margin: 0 8px;
        color: #6c757d;
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    padding: 10px 0;

    .container {
      padding: 0 15px;
    }

    .breadcrumb {
      font-size: 13px;

      .breadcrumb-item {
        &:not(:last-child)::after {
          margin: 0 6px;
        }
      }
    }
  }
}
</style>
