<template>
  <div id="app">
    <Header />
    <main class="main-content">
      <router-view />
    </main>
    <Footer />
    <BackToTop />
  </div>
</template>

<script>
import Header from './components/layout/Header.vue'
import Footer from './components/layout/Footer.vue'
import BackToTop from './components/common/BackToTop.vue'
import { API } from '@/api/index.js'

export default {
  name: 'App',
  components: {
    Header,
    Footer,
    BackToTop
  },
  mounted() {
    // 页面加载完成后的初始化操作
    this.initScrollEffects()
    this.getBannerData()
  },
  methods: {
    async getBannerData() {
      const res = await API.getBannerData()
      if (res.success) {
        localStorage.setItem('bannerData', JSON.stringify(res.data.banners))
        console.log("bannerData ===>", res.data.banners)
      }
    },
    initScrollEffects() {
      // 导航栏滚动效果
      window.addEventListener('scroll', this.handleScroll)
    },
    handleScroll() {
      const navbar = document.querySelector('.navbar')
      if (navbar) {
        if (window.scrollY > 50) {
          navbar.style.background = 'rgba(255, 255, 255, 0.98)'
          navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)'
        } else {
          navbar.style.background = 'rgba(255, 255, 255, 0.95)'
          navbar.style.boxShadow = 'none'
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
  }
}
</script>

<style lang="less">
#app {
  font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;
  overflow-x: hidden;
}

.main-content {
  min-height: calc(100vh - 140px); // 减去header和footer的高度
}
</style>
