# RichTextEditor 富文本编辑器组件

基于 Quill.js 的 Vue 富文本编辑器组件，支持图片上传到服务器。

## 功能特点

- ✅ **完整的富文本编辑功能**：支持文本格式、段落样式、列表、对齐等
- ✅ **图片上传集成**：点击图片按钮直接上传到服务器，只存储URL
- ✅ **双向数据绑定**：支持 v-model 语法
- ✅ **高度可定制**：可自定义工具栏、高度、占位符等
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **内存管理**：组件销毁时自动清理资源

## 基本用法

```vue
<template>
  <div>
    <RichTextEditor 
      v-model="content"
      height="300px"
      placeholder="请输入内容..."
      @change="handleContentChange"
    />
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor.vue';

export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      content: ''
    };
  },
  methods: {
    handleContentChange(content) {
      console.log('内容已更改:', content);
    }
  }
};
</script>
```

## Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 编辑器内容（支持v-model） |
| height | String | '300px' | 编辑器高度 |
| placeholder | String | '请输入内容...' | 占位符文本 |
| readonly | Boolean | false | 是否只读 |
| toolbar | Array | 默认工具栏 | 自定义工具栏配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | content | 内容变化时触发（v-model） |
| change | content | 内容变化时触发 |

## Methods 方法

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getContent | - | String | 获取HTML内容 |
| setContent | content | - | 设置HTML内容 |
| getText | - | String | 获取纯文本内容 |
| clear | - | - | 清空内容 |
| focus | - | - | 聚焦编辑器 |
| setReadonly | readonly | - | 设置只读状态 |

## 自定义工具栏

```vue
<template>
  <RichTextEditor 
    v-model="content"
    :toolbar="customToolbar"
  />
</template>

<script>
export default {
  data() {
    return {
      content: '',
      customToolbar: [
        ['bold', 'italic', 'underline'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['clean']
      ]
    };
  }
};
</script>
```

## 图片上传功能

组件内置了图片上传功能：

1. **自动上传**：点击工具栏的图片按钮，选择图片后自动上传到服务器
2. **进度提示**：显示上传进度和结果提示
3. **URL存储**：只在数据库中存储图片URL，减少存储容量
4. **权限验证**：上传时自动携带JWT token进行身份验证

### 上传配置

- **接口地址**：`/api/upload`
- **文件字段**：`file`
- **支持格式**：image/*
- **大小限制**：2MB
- **认证方式**：Bearer Token

## 使用示例

### 产品描述编辑

```vue
<el-form-item label="产品详情" prop="description">
  <RichTextEditor 
    v-model="productForm.description"
    height="400px"
    placeholder="请输入产品详细描述..."
    @change="handleDescriptionChange"
  />
</el-form-item>
```

### 文章内容编辑

```vue
<el-form-item label="文章内容" prop="content">
  <RichTextEditor 
    v-model="articleForm.content"
    height="500px"
    placeholder="请输入文章内容..."
  />
</el-form-item>
```

### 只读模式

```vue
<RichTextEditor 
  v-model="content"
  :readonly="true"
  height="200px"
/>
```

## 注意事项

1. **依赖安装**：确保已安装 `quill` 依赖
2. **样式引入**：组件会自动引入 Quill 的 CSS 样式
3. **图片上传**：需要后端提供 `/api/upload` 接口
4. **认证状态**：图片上传需要用户已登录（有效的JWT token）
5. **内存清理**：组件销毁时会自动清理编辑器实例

## 技术栈

- **Quill.js**：富文本编辑器核心
- **Vue 2**：组件框架
- **Element UI**：UI组件库（用于消息提示）
- **Axios**：HTTP请求库

## 更新日志

### v1.0.0
- 初始版本
- 支持基本富文本编辑功能
- 集成图片上传功能
- 支持双向数据绑定
- 支持自定义配置
