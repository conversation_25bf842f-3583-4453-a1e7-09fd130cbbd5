# ===================================
# 雅克菲Vue项目 .gitignore
# ===================================

# Dependencies / 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production build / 构建输出
dist/
build/

# Environment files / 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Log files / 日志文件
*.log
logs/

# Package manager lock files / 包管理器锁文件
# 注意：通常建议提交lock文件以确保依赖版本一致性
# 如果团队使用不同的包管理器，可以取消注释相应行
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Cache directories / 缓存目录
.cache/
.parcel-cache/
.npm/
.eslintcache
.stylelintcache

# Coverage directory / 测试覆盖率
coverage/
*.lcov
.nyc_output/

# IDE and Editor files / IDE和编辑器文件
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo

# JetBrains IDEs
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*~

# Emacs
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Visual Studio Code
.history/
*.vsix

# macOS / macOS系统文件
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows / Windows系统文件
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.lnk

# Linux / Linux系统文件
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Temporary files / 临时文件
tmp/
temp/
*.tmp
*.temp
*.backup
*.bak
*.orig
*.rej

# Runtime data / 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Webpack
.webpack/

# TypeScript
*.tsbuildinfo

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===================================
# 项目特定文件 / Project Specific
# ===================================

# Deployment files / 部署文件
deploy/
deployment/

# Documentation build / 文档构建
docs/build/

# Database files / 数据库文件
*.sqlite
*.sqlite3
*.db

# Certificates / 证书文件
*.pem
*.key
*.crt
*.csr

# Archives / 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# Local configuration / 本地配置
config/local.js
config/local.json

# ===================================
# 可选：根据需要取消注释
# ===================================

# 如果不想提交某些图片或媒体文件
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.mp4
# *.mov

# 如果有本地开发用的测试文件
# test-local/
# playground/

# 如果有自动生成的文档
# docs/api/
