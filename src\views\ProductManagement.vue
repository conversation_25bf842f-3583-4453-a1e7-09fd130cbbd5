<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>产品管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加产品
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="产品名称">
            <el-input v-model="searchForm.name" placeholder="请输入产品名称" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="产品分类">
            <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable style="width: 200px;">
              <el-option label="全部分类" value="" class="category-all" />
              <el-option v-for="category in flatCategories" :key="category.id" :label="category.name"
                :value="category.id" :class="{
                  'category-level-1': category.level === 1,
                  'category-level-2': category.level === 2,
                  'category-level-3': category.level >= 3,
                  'category-parent': category.isParent
                }" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="上架" :value="1" />
              <el-option label="下架" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 产品表格 -->
      <el-table v-loading="loading" :data="productList" border class="product-table">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="产品图片" width="100">
          <template slot-scope="scope">
            <el-image v-if="scope.row.main_image" :src="scope.row.main_image" :preview-src-list="[scope.row.main_image]"
              class="product-image" />
            <span v-else class="no-image">暂无图片</span>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="产品名称" min-width="200" />

        <el-table-column prop="category_name" label="所属分类" width="150" />

        <el-table-column prop="summary" label="产品摘要" min-width="200" show-overflow-tooltip />

        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column prop="view_count" label="浏览量" width="80" align="center" />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" />
      </div>
    </div>

    <!-- 添加/编辑产品对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1000px" @close="resetForm">
      <el-form ref="productForm" :model="productForm" :rules="productRules" label-width="100px">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入产品名称" />
        </el-form-item>

        <el-form-item label="所属分类" prop="category_id">
          <el-select v-model="productForm.category_id" placeholder="请选择分类" style="width: 100%;">
            <el-option v-for="category in flatCategories" :key="category.id" :label="category.name" :value="category.id"
              :class="{
                'category-level-1': category.level === 1,
                'category-level-2': category.level === 2,
                'category-level-3': category.level >= 3,
                'category-parent': category.isParent
              }" />
          </el-select>
        </el-form-item>

        <el-form-item label="产品主图" prop="main_image">
          <ImageUploader v-model="productForm.main_image" size="200x150" tip="建议尺寸：400x300像素，支持jpg、png格式"
            :max-size="5" />
        </el-form-item>

        <el-form-item label="产品摘要" prop="summary">
          <el-input v-model="productForm.summary" type="textarea" :rows="3" placeholder="请输入产品摘要" />
        </el-form-item>

        <el-form-item label="产品详情" prop="description">
          <RichTextEditor v-model="productForm.description" height="300px" placeholder="请输入产品详细描述..."
            @change="handleDescriptionChange" />
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="productForm.sort_order" :min="0" :max="999" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="productForm.status">
            <el-radio :label="1">上架</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api';
import RichTextEditor from '@/components/RichTextEditor.vue';
import ImageUploader from '@/components/ImageUploader.vue';

export default {
  name: 'ProductManagement',
  components: {
    RichTextEditor,
    ImageUploader
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      productList: [],
      categories: [],
      searchForm: {
        name: '',
        category_id: '',
        status: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      productForm: {
        name: '',
        category_id: '',
        main_image: '',
        summary: '',
        description: '',
        sort_order: 0,
        status: 1
      },
      productRules: {
        name: [
          { required: true, message: '请输入产品名称', trigger: 'blur' },
          { min: 1, max: 200, message: '产品名称长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        category_id: [
          { required: true, message: '请选择产品分类', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑产品' : '添加产品';
    },

    // 扁平化分类列表
    flatCategories() {
      const flatten = (categories, level = 1) => {
        let result = [];
        categories.forEach(category => {
          // 使用不同的前缀来表示层级
          let prefix = '';
          if (level === 1) {
            prefix = '';
          } else if (level === 2) {
            prefix = '├─ ';
          } else if (level === 3) {
            prefix = '│  ├─ ';
          } else {
            prefix = '│  '.repeat(level - 2) + '├─ ';
          }

          result.push({
            ...category,
            name: prefix + category.name,
            level,
            displayName: category.name, // 保留原始名称用于显示
            isParent: category.children && category.children.length > 0
          });

          if (category.children && category.children.length > 0) {
            result = result.concat(flatten(category.children, level + 1));
          }
        });
        return result;
      };

      return flatten(this.categories);
    }
  },

  created() {
    this.loadCategories();
    this.loadProductList();
  },



  methods: {
    async loadCategories() {
      try {
        const response = await api.productCategory.getList();
        if (response.success) {
          this.categories = response.data.categories;
        } else {
          console.error('获取分类列表失败:', response);
          this.$message.error(response.message || '获取分类列表失败');
        }
      } catch (error) {
        console.error('获取分类列表异常:', error);
        this.$message.error(error.response?.data?.message || '获取分类列表失败');
      }
    },

    async loadProductList() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          ...this.searchForm
        };

        const response = await api.product.getList(params);
        if (response.success) {
          this.productList = response.data.products;
          this.pagination.total = response.data.pagination.total_items;
        } else {
          console.error('获取产品列表失败:', response);
          this.$message.error(response.message || '获取产品列表失败');
        }
      } catch (error) {
        console.error('获取产品列表异常:', error);
        this.$message.error(error.response?.data?.message || '获取产品列表失败');
      } finally {
        this.loading = false;
      }
    },

    handleSearch() {
      this.pagination.current = 1;
      this.loadProductList();
    },

    handleReset() {
      this.searchForm = {
        name: '',
        category_id: '',
        status: ''
      };
      this.pagination.current = 1;
      this.loadProductList();
    },

    handleSizeChange(size) {
      this.pagination.size = size;
      this.pagination.current = 1;
      this.loadProductList();
    },

    handleCurrentChange(page) {
      this.pagination.current = page;
      this.loadProductList();
    },

    handleAdd() {
      this.isEdit = false;
      this.editId = null;
      this.dialogVisible = true;
    },

    handleEdit(product) {
      this.isEdit = true;
      this.editId = product.id;
      this.productForm = {
        name: product.name,
        category_id: product.category_id,
        main_image: product.main_image || '',
        summary: product.summary || '',
        description: product.description || '',
        sort_order: product.sort_order,
        status: product.status
      };
      this.dialogVisible = true;
    },

    async handleDelete(product) {
      try {
        await this.$confirm(`确定要删除产品"${product.name}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await api.product.delete(product.id);
        this.$message.success('删除产品成功');
        this.loadProductList();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除产品失败');
        }
      }
    },

    async handleStatusChange(product) {
      try {
        await api.product.update(product.id, {
          status: product.status
        });
        this.$message.success('状态更新成功');
      } catch (error) {
        this.$message.error('状态更新失败');
        // 恢复原状态
        product.status = product.status === 1 ? 0 : 1;
      }
    },

    async handleSubmit() {
      try {
        await this.$refs.productForm.validate();

        this.submitLoading = true;

        if (this.isEdit) {
          await api.product.update(this.editId, this.productForm);
          this.$message.success('更新产品成功');
        } else {
          await api.product.create(this.productForm);
          this.$message.success('创建产品成功');
        }

        this.dialogVisible = false;
        this.loadProductList();
      } catch (error) {
        if (error.message) {
          this.$message.error(error.message);
        }
      } finally {
        this.submitLoading = false;
      }
    },

    resetForm() {
      this.productForm = {
        name: '',
        category_id: '',
        main_image: '',
        summary: '',
        description: '',
        sort_order: 0,
        status: 1
      };
      this.$refs.productForm && this.$refs.productForm.clearValidate();
    },



    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleString();
    },

    handleDescriptionChange(content) {
      // 富文本编辑器内容变化时的处理
      console.log('产品描述已更新:', content);
    }
  }
};
</script>

<style lang="less" scoped>
.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 4px;
}

.product-table {
  .product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
  }

  .no-image {
    color: #999;
    font-size: 12px;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}



.image-uploader {
  .uploaded-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
  }

  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      border-color: #409EFF;
    }
  }
}

// 分类下拉框样式优化
/deep/ .el-select-dropdown__item {
  &.category-all {
    font-weight: 600;
    color: #409EFF;
    border-bottom: 2px solid #ebeef5;
    margin-bottom: 4px;

    &:hover {
      background-color: #ecf5ff;
    }
  }

  &.category-level-1 {
    font-weight: 600;
    color: #303133;
    background-color: #f8f9fa;

    &:hover {
      background-color: #ecf5ff;
    }
  }

  &.category-level-2 {
    color: #606266;
    font-size: 13px;
    padding-left: 30px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  &.category-level-3 {
    color: #909399;
    font-size: 12px;
    padding-left: 45px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  &.category-parent {
    border-bottom: 1px solid #ebeef5;
  }
}
</style>
