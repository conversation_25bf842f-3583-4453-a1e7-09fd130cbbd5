<template>
  <transition name="modal-fade">
    <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
      <div class="modal-container" @click.stop :style="containerStyle">
        <!-- 模态框头部 -->
        <div class="modal-header" v-if="showHeader">
          <slot name="header">
            <h3 class="modal-title">{{ title }}</h3>
          </slot>
          <button 
            class="modal-close"
            @click="close"
            aria-label="关闭"
            v-if="showCloseButton"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 模态框内容 -->
        <div class="modal-body">
          <slot></slot>
        </div>

        <!-- 模态框底部 -->
        <div class="modal-footer" v-if="$slots.footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </transition>
</template>
  
  <script>
  export default {
    name: 'Modal',
    props: {
      // 控制模态框显示/隐藏
      visible: {
        type: Boolean,
        default: false
      },
      // 模态框标题
      title: {
        type: String,
        default: ''
      },
      // 是否显示头部
      showHeader: {
        type: Boolean,
        default: true
      },
      // 是否显示关闭按钮
      showCloseButton: {
        type: Boolean,
        default: true
      },
      // 点击遮罩层是否关闭
      maskClosable: {
        type: Boolean,
        default: true
      },
      // 模态框宽度
      width: {
        type: String,
        default: '500px'
      },
      // 模态框最大宽度
      maxWidth: {
        type: String,
        default: '90vw'
      },
      // z-index 层级
      zIndex: {
        type: Number,
        default: 1050
      }
    },
      emits: ['update:visible', 'close'],
  computed: {
    containerStyle() {
      return {
        width: this.width,
        maxWidth: this.maxWidth,
        zIndex: this.zIndex
      }
    }
  },
  watch: {
      visible(newVal) {
        if (newVal) {
          this.addBodyClass()
          document.addEventListener('keydown', this.handleEscapeKey)
        } else {
          this.removeBodyClass()
          document.removeEventListener('keydown', this.handleEscapeKey)
        }
      }
    },
    beforeUnmount() {
      this.removeBodyClass()
      document.removeEventListener('keydown', this.handleEscapeKey)
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
        this.$emit('close')
      },
      handleOverlayClick() {
        if (this.maskClosable) {
          this.close()
        }
      },
      handleEscapeKey(event) {
        if (event.key === 'Escape') {
          this.close()
        }
      },
      addBodyClass() {
        document.body.classList.add('modal-open')
      },
      removeBodyClass() {
        document.body.classList.remove('modal-open')
      }
    }
  }
  </script>
  
  <style lang="less" scoped>
  @import '@/assets/styles/variables.less';
  
  .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: @spacing-lg;
}
  
  .modal-container {
  background: @bg-color;
  border-radius: @border-radius-large;
  box-shadow: @shadow-hover;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease;
}
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: @spacing-lg @spacing-xl;
    border-bottom: 1px solid @border-color;
    background: @bg-light;
  
    .modal-title {
      margin: 0;
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: @text-color;
    }
  
    .modal-close {
      background: none;
      border: none;
      font-size: @font-size-lg;
      color: @text-light;
      cursor: pointer;
      padding: @spacing-xs;
      border-radius: @border-radius-small;
      transition: @transition-fast;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
  
      &:hover {
        background: rgba(0, 0, 0, 0.1);
        color: @text-color;
      }
    }
  }
  
  .modal-body {
    padding: @spacing-xl;
    flex: 1;
    overflow-y: auto;
  }
  
  .modal-footer {
    padding: @spacing-lg @spacing-xl;
    border-top: 1px solid @border-color;
    background: @bg-light;
    display: flex;
    justify-content: flex-end;
    gap: @spacing-md;
  }
  
  // 动画效果
  .modal-fade-enter-active,
  .modal-fade-leave-active {
    transition: opacity 0.3s ease;
  }
  
  .modal-fade-enter-from,
  .modal-fade-leave-to {
    opacity: 0;
  }
  
  .modal-fade-enter-active .modal-container {
    animation: modalSlideIn 0.3s ease;
  }
  
  .modal-fade-leave-active .modal-container {
    animation: modalSlideOut 0.3s ease;
  }
  
  @keyframes modalSlideIn {
    from {
      transform: translateY(-50px) scale(0.95);
      opacity: 0;
    }
    to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
  
  @keyframes modalSlideOut {
    from {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
    to {
      transform: translateY(-50px) scale(0.95);
      opacity: 0;
    }
  }
  
  // 移动端适配
  @media (max-width: @screen-sm) {
    .modal-overlay {
      padding: @spacing-md;
    }
  
    .modal-container {
      width: 100%;
      max-width: none;
      margin: 0;
    }
  
    .modal-header,
    .modal-body,
    .modal-footer {
      padding-left: @spacing-lg;
      padding-right: @spacing-lg;
    }
  
    .modal-body {
      padding-top: @spacing-lg;
      padding-bottom: @spacing-lg;
    }
  }
  </style>
  
  <style lang="less">
  // 全局样式，防止页面滚动
  body.modal-open {
    overflow: hidden;
  }
  </style>