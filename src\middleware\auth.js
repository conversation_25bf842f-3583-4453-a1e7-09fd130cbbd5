const jwt = require('jsonwebtoken');
const { query } = require('../config/database');
const { CustomError } = require('./errorHandler');

// JWT认证中间件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new CustomError('访问令牌缺失', 401);
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 查询用户信息
    const users = await query(
      'SELECT id, username, email, real_name, status FROM admins WHERE id = ? AND status = 1',
      [decoded.userId]
    );

    if (users.length === 0) {
      throw new CustomError('用户不存在或已被禁用', 401);
    }

    // 将用户信息添加到请求对象
    req.user = users[0];
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new CustomError('无效的访问令牌', 401));
    } else if (error.name === 'TokenExpiredError') {
      return next(new CustomError('访问令牌已过期', 401));
    }
    next(error);
  }
};

// 生成JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

// 验证token（不抛出错误，用于可选认证）
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    return null;
  }
};

module.exports = {
  authenticateToken,
  generateToken,
  verifyToken
};
