<template>
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-logo">
        <router-link to="/">
          <img v-if="logoUrl" :src="logoUrl" alt="logo" srcset="">
        </router-link>
      </div>

      <ul class="nav-menu" :class="{ active: mobileMenuOpen }">
        <li v-for="item in menuItems" :key="item.name" class="nav-item">
          <div class="nav-link" :class="{ active: $route.path === item.path }" @click="handleNavClick(item, $event)">
            {{ item.name }}
            <i v-if="item.children && item.children.length" class="fas fa-chevron-down submenu-arrow"
              :class="{ 'rotated': activeSubmenu === item.name }"></i>
          </div>

          <!-- 二级菜单 -->
          <ul v-if="item.children && item.children.length" class="sub-menu"
            :class="{ 'expanded': activeSubmenu === item.name }">
            <li v-for="child in item.children" :key="child.name">
              <div class="sub-link" @click="handleSubMenuClick(child)">
                {{ child.name }}
              </div>
            </li>
          </ul>
        </li>
      </ul>

      <div class="nav-toggle" :class="{ active: mobileMenuOpen }" @click="toggleMobileMenu">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>
</template>

<script>
import { API } from '@/api/index.js'
import { isMobileUI } from '@/utils/deviceDetect.js'

export default {
  name: 'Header',
  data() {
    return {
      mobileMenuOpen: false,
      menuItems: [],
      activeSubmenu: null, // 当前展开的子菜单名称
      logoUrl: '',
    }
  },
  methods: {
    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen
      // 关闭菜单时重置子菜单状态
      if (!this.mobileMenuOpen) {
        this.activeSubmenu = null
      }
    },
    closeMobileMenu() {
      this.mobileMenuOpen = false
      this.activeSubmenu = null
    },
    handleNavClick(item, event) {
      // 如果是有子菜单的项目且在移动端，则切换子菜单显示
      if (item.children && item.children.length && isMobileUI()) {
        // 阻止默认行为，避免触发hover状态和其他默认行为
        if (event) {
          event.preventDefault()
          event.stopPropagation()
        }
        // 切换子菜单显示状态
        this.activeSubmenu = this.activeSubmenu === item.name ? null : item.name
      } else {
        // 没有子菜单或在桌面端，检查是否为当前路由，避免重复导航
        if (this.$route.path !== item.path) {
          this.$router.push(item.path)
        }
        this.closeMobileMenu()
      }
    },
    handleSubMenuClick(child) {
      // 二级菜单点击，处理包含查询参数的路径
      const targetPath = child.path || child.url

      // 解析目标路径和查询参数
      const [path, queryString] = targetPath.split('?')
      const query = {}

      if (queryString) {
        // 解析查询参数
        queryString.split('&').forEach(param => {
          const [key, value] = param.split('=')
          if (key && value) {
            query[key] = decodeURIComponent(value)
          }
        })
      }

      // 构建完整的路由对象
      const routeObject = { path, query }

      // 检查是否为当前路由，避免重复导航
      const currentPath = this.$route.path
      const currentQuery = this.$route.query
      const isSamePath = currentPath === path
      const isSameQuery = JSON.stringify(currentQuery) === JSON.stringify(query)

      if (!isSamePath || !isSameQuery) {
        this.$router.push(routeObject)
      }

      this.closeMobileMenu()
    },
    async loadMenuData() {
      try {
        const response = await API.getMenuList()
        if (response.success) {
          this.menuItems = response.data.menus
        } else {
          console.error('获取菜单数据失败:', response.message)
        }
      } catch (error) {
        console.error('获取菜单数据出错:', error)
      }
    },
    // 获取logo
    async getLogo() {
      try {
        const response = await API.getImageByTag('logo')
        if (response.success) {
          console.log(response.data)
          this.logoUrl = response.data.images[0].image_url
        } else {
          throw new Error(response.message || '获取logo失败')
        }
      } catch (error) {
        console.error('获取logo失败:', error)
      }
    }
  },
  async mounted() {
    // 加载菜单数据
    this.getLogo()
    await this.loadMenuData()
    // 点击外部关闭移动端菜单
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.mobileMenuOpen = false
      }
    })
  }
}
</script>

<style lang="less" scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(208, 32, 35, 0.1);
  z-index: 1001;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  width: 100px;
  max-height: 70px;

  img {
    width: 100%;
    height: 100%;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  h2 {
    color: #D80514;
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 -5px 0;
  }

  span {
    color: #666;
    font-size: 12px;
    font-weight: 300;
    letter-spacing: 2px;
  }
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;

  // 只在非触摸设备上启用hover效果，避免移动端hover状态干扰
  @media (hover: hover) and (pointer: fine) {
    &:hover .sub-menu {
      opacity: 1;
      visibility: visible;
      transform: translate(-50%, 0);
    }
  }
}





.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  position: relative;
  padding: 5px 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover,
  &.active {
    color: #D80514;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #D80514;
    transition: width 0.3s ease;
  }

  &:hover::after,
  &.active::after {
    width: 100%;
  }
}

.submenu-arrow {
  display: none;
  font-size: 12px;
  color: #666;
  transition: transform 0.3s ease;
  margin-left: 8px;

  &.rotated {
    transform: rotate(180deg);
  }
}

.sub-menu {
  position: absolute;
  top: 40px;
  left: 50%;
  background: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  // padding: 10px 0;
  width: 120px;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transform: translate(-50%, -10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
  padding-left: 0;

  li {
    padding: 0;
  }

  .sub-link {
    display: block;
    padding: 8px 20px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: #f8f9fa;
      color: #D80514;
    }
  }
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;

  span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
  }

  &.active {
    span:nth-child(1) {
      transform: rotate(-45deg) translate(-5px, 6px);
    }

    span:nth-child(2) {
      opacity: 0;
    }

    span:nth-child(3) {
      transform: rotate(45deg) translate(-5px, -6px);
    }
  }
}

// 移动端样式
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    // height: calc(100vh - 70px);
    background: white;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 0;
    // padding: 20px 0;
    transition: left 0.3s ease;

    &.active {
      left: 0;
    }
  }

  .nav-logo {
    width: 70px;
    max-height: 70px;
  }

  .nav-item {
    width: 100%;
    border-bottom: 1px solid #eee;
    margin-bottom: 0;

    &:first-child {
      border-top: 1px solid #eee;
    }
  }

  .nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    font-size: 16px;
    text-align: left;

    &::after {
      border: 0;
      display: none;
    }
  }

  .submenu-arrow {
    display: inline-block;
  }

  .sub-menu {
    position: static;
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    overflow: hidden;
    transform: none;
    box-shadow: none;
    background: #f8f9fa;
    margin: 0;
    transition: all 0.3s ease;
    padding-left: 0;
    width: 100%;
    text-align: left;

    &.expanded {
      opacity: 1;
      visibility: visible;
      max-height: 500px;
    }

    .sub-link {
      display: block;
      padding: 12px 30px;
      font-size: 14px;
      color: #666;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: #e9ecef;
        color: #D80514;
        transform: translateX(10px);
      }
    }
  }

  // 移动端不需要hover效果，已在上面的媒体查询中处理

  .nav-toggle {
    display: flex;
  }
}
</style>
