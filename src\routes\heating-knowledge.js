const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取采暖知识列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    status = '',
    tag = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  if (tag) {
    whereConditions.push('JSON_CONTAINS(tags, ?)');
    queryParams.push(JSON.stringify(tag));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM heating_knowledge ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取采暖知识列表
  const knowledgeQuery = `
    SELECT id, title, summary, thumbnail, description, status, tags, sort_order, created_at, updated_at
    FROM heating_knowledge
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const knowledge = await query(knowledgeQuery, queryParams);

  // 处理tags字段
  const processedKnowledge = knowledge.map(row => {
    let tags = [];
    try {
      if (row.tags && typeof row.tags === 'string') {
        tags = JSON.parse(row.tags);
      } else if (Array.isArray(row.tags)) {
        tags = row.tags;
      }
    } catch (error) {
      console.error('解析tags字段失败:', error, 'tags值:', row.tags);
      tags = [];
    }
    return {
      ...row,
      tags
    };
  });

  res.json({
    success: true,
    message: '获取采暖知识列表成功',
    data: {
      knowledge: processedKnowledge,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取采暖知识详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query(
    'SELECT * FROM heating_knowledge WHERE id = ?',
    [id]
  );

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '采暖知识记录不存在'
    });
  }

  let tags = [];
  try {
    if (rows[0].tags && typeof rows[0].tags === 'string') {
      tags = JSON.parse(rows[0].tags);
    } else if (Array.isArray(rows[0].tags)) {
      tags = rows[0].tags;
    }
  } catch (error) {
    console.error('解析tags字段失败:', error);
    tags = [];
  }

  const knowledge = {
    ...rows[0],
    tags
  };

  res.json({
    success: true,
    message: '获取采暖知识详情成功',
    data: knowledge
  });
}));

// 创建采暖知识
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    title,
    summary,
    thumbnail,
    description,
    status = 1,
    tags = [],
    sort_order = 0
  } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '标题不能为空'
    });
  }

  const result = await query(
    'INSERT INTO heating_knowledge (title, summary, thumbnail, description, status, tags, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [title, summary, thumbnail, description, parseInt(status), JSON.stringify(tags), parseInt(sort_order)]
  );

  res.status(201).json({
    success: true,
    message: '创建采暖知识成功',
    data: {
      id: result.insertId,
      title,
      summary,
      thumbnail,
      description,
      status: parseInt(status),
      tags,
      sort_order: parseInt(sort_order)
    }
  });
}));

// 更新采暖知识
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { title, summary, thumbnail, description, status, tags, sort_order } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '标题不能为空'
    });
  }

  // 检查记录是否存在
  const existingRows = await query(
    'SELECT id FROM heating_knowledge WHERE id = ?',
    [id]
  );

  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '采暖知识记录不存在'
    });
  }

  await query(
    'UPDATE heating_knowledge SET title = ?, summary = ?, thumbnail = ?, description = ?, status = ?, tags = ?, sort_order = ? WHERE id = ?',
    [title, summary, thumbnail, description, parseInt(status), JSON.stringify(tags || []), parseInt(sort_order || 0), id]
  );

  res.json({
    success: true,
    message: '更新采暖知识成功'
  });
}));

// 删除采暖知识
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query(
    'SELECT id FROM heating_knowledge WHERE id = ?',
    [id]
  );

  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '采暖知识记录不存在'
    });
  }

  await query('DELETE FROM heating_knowledge WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除采暖知识成功'
  });
}));

module.exports = router;
