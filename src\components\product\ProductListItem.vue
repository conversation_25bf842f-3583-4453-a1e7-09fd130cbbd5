<template>
  <div class="product-list-item">
    <div class="product-image">
      <img :src="product.main_image || '/images/placeholder-product.jpg'" :alt="product.name"
        @error="handleImageError" />
    </div>

    <div class="product-info">
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-summary">{{ product.summary }}</p>

      <div class="product-meta">
        <span v-if="product.category_name" class="category">
          分类：{{ product.category_name }}
        </span>
        <span v-if="product.view_count" class="view-count">
          浏览：{{ product.view_count }}次
        </span>
      </div>

      <div class="product-actions">
        <button class="btn btn-primary" @click="$emit('view-detail', product)">
          查看详情
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductListItem',
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  methods: {
    handleImageError(event) {
      event.target.src = '/images/placeholder-product.jpg'
    }
  }
}
</script>

<style lang="less" scoped>
.product-list-item {
  display: flex;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .product-image {
    width: 100%;
    height: 220px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 220px;
      object-fit: cover;
    }
  }

  .product-info {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .product-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 10px 0;
      line-height: 1.4;
    }

    .product-summary {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      margin: 0 0 15px 0;
      flex: 1;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .product-meta {
      display: flex;
      gap: 20px;
      margin-bottom: 15px;

      span {
        font-size: 12px;
        color: #999;
      }
    }

    .product-actions {
      .btn {
        padding: 8px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;

        &.btn-primary {
          background: #D80514;
          color: white;

          &:hover {
            background: #b8040f;
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .product-list-item {
    flex-direction: column;

    .product-image {
      width: 100%;
      height: 180px;
    }

    .product-info {
      padding: 15px;

      .product-name {
        font-size: 16px;
      }

      .product-meta {
        flex-direction: row;
        gap: 5px;
      }
    }
  }
}
</style>
