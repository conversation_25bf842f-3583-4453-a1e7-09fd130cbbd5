// 菜单缓存管理器
class MenuCache {
  constructor() {
    this.cacheKey = 'airfit_menu_cache'
    this.timestampKey = 'airfit_menu_timestamp'
    this.cacheExpiry = 30 * 60 * 1000 // 30分钟缓存过期时间
  }

  // 获取缓存的菜单数据
  getCache() {
    try {
      const timestamp = localStorage.getItem(this.timestampKey)
      const cacheData = localStorage.getItem(this.cacheKey)
      
      if (!timestamp || !cacheData) {
        return null
      }
      
      const now = Date.now()
      const cacheTime = parseInt(timestamp)
      
      // 检查缓存是否过期
      if (now - cacheTime > this.cacheExpiry) {
        this.clearCache()
        return null
      }
      
      return JSON.parse(cacheData)
    } catch (error) {
      console.error('读取菜单缓存失败:', error)
      this.clearCache()
      return null
    }
  }

  // 设置菜单缓存
  setCache(menuData) {
    try {
      const timestamp = Date.now().toString()
      localStorage.setItem(this.cacheKey, JSON.stringify(menuData))
      localStorage.setItem(this.timestampKey, timestamp)
    } catch (error) {
      console.error('设置菜单缓存失败:', error)
    }
  }

  // 清除缓存
  clearCache() {
    try {
      localStorage.removeItem(this.cacheKey)
      localStorage.removeItem(this.timestampKey)
    } catch (error) {
      console.error('清除菜单缓存失败:', error)
    }
  }

  // 检查缓存是否有效
  isValid() {
    const timestamp = localStorage.getItem(this.timestampKey)
    if (!timestamp) {
      return false
    }
    
    const now = Date.now()
    const cacheTime = parseInt(timestamp)
    
    return (now - cacheTime) <= this.cacheExpiry
  }
}

// 创建单例实例
const menuCache = new MenuCache()

export default menuCache
