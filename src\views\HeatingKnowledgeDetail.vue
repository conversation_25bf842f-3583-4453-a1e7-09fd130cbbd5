<template>
  <div class="knowledge-detail">

    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="fetchKnowledgeDetail">重试</button>
      </div>
    </div>

    <!-- 知识详情内容 -->
    <div v-else-if="knowledgeData" class="detail-content">
      <div class="container">
        <!-- 文章头部 -->
        <div class="article-header">
          <h1 class="article-title">{{ knowledgeData.title }}</h1>
          <div class="article-meta">
            <span class="publish-time">{{ formatDate(knowledgeData.created_at) }}</span>
            <span v-if="knowledgeData.view_count" class="view-count">
              <i class="fas fa-eye"></i>
              {{ knowledgeData.view_count }} 次阅读
            </span>
          </div>

          <!-- 标签 -->
          <!-- <div v-if="knowledgeData.tags && knowledgeData.tags.length > 0" class="article-tags">
            <span v-for="tag in knowledgeData.tags" :key="tag" class="tag">
              {{ tag }}
            </span>
          </div> -->


        </div>

        <!-- 文章内容 -->
        <div class="article-body">
          <div v-if="knowledgeData.description" class="rich-content" v-html="knowledgeData.description"></div>
          <div v-else class="no-content">
            <p>暂无详细内容</p>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'
import api from '@/api'
import { formatDateTime } from '@/utils/dateFormat'

export default {
  name: 'HeatingKnowledgeDetail',
  components: {
    PageHeader,
    Breadcrumb
  },
  data() {
    return {
      knowledgeData: null,
      relatedKnowledge: [],
      loading: false,
      error: null
    }
  },
  computed: {
    breadcrumbItems() {
      const items = [
        { name: '服务支持', path: '/service-support' },
        { name: '采暖知识', path: '/heating-knowledge' }
      ]

      if (this.knowledgeData) {
        items.push({ name: this.knowledgeData.title, path: '' })
      }

      return items
    }
  },
  mounted() {
    this.fetchKnowledgeDetail()
  },
  watch: {
    '$route'() {
      this.fetchKnowledgeDetail()
    }
  },
  methods: {
    async fetchKnowledgeDetail() {
      const id = this.$route.params.id
      if (!id) {
        this.error = '知识ID不存在'
        return
      }

      this.loading = true
      this.error = null

      try {
        const response = await api.get(`/api/front/heating-knowledge/${id}`)

        if (response.success) {
          this.knowledgeData = response.data.knowledge
          this.relatedKnowledge = response.data.related_knowledge || []
          // 设置页面标题
          if (this.knowledgeData.title) {
            document.title = `${this.knowledgeData.title} - 采暖知识 - 雅克菲`
          }
        } else {
          this.error = response.message || '获取知识详情失败'
        }
      } catch (error) {
        console.error('获取知识详情失败:', error)
        if (error.response && error.response.status === 404) {
          this.error = '知识不存在或已下架'
        } else {
          this.error = '网络错误，请稍后重试'
        }
      } finally {
        this.loading = false
      }
    },

    goToKnowledge(id) {
      this.$router.push(`/heating-knowledge/${id}`)
    },

    formatDate(dateString) {
      return formatDateTime(dateString, 'YYYY-MM-DD HH:mm:ss')
    },

    handleImageError(event) {
      // 图片加载失败时隐藏图片
      event.target.style.display = 'none'
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.knowledge-detail {
  background: #f8f9fa;
  min-height: 100vh;

  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .loading-spinner,
    .error-content {
      text-align: center;

      i {
        font-size: 48px;
        color: @text-lighter;
        margin-bottom: 16px;
      }

      h3 {
        margin: 16px 0 8px;
        color: @text-color;
      }

      p {
        color: @text-light;
        margin-bottom: 16px;
      }

      .btn {
        background: @primary-color;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: @border-radius-small;
        cursor: pointer;
        transition: @transition;

        &:hover {
          background: @primary-hover;
        }
      }
    }
  }

  .detail-content {
    padding: @spacing-xxl 0;

    .container {
      max-width: @container-max-width;
      margin: 0 auto;
      padding: 0 @spacing-lg;
    }

    .article-header {
      text-align: center;
      margin-bottom: @spacing-xxl;
      padding-bottom: @spacing-xl;
      border-bottom: 1px solid @border-color;

      .article-title {
        font-size: 2.5rem;
        font-weight: @font-weight-bold;
        color: @text-color;
        margin-bottom: @spacing-lg;
        line-height: 1.3;

        @media (max-width: @screen-sm) {
          font-size: 2rem;
        }
      }

      .article-meta {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: @spacing-lg;
        margin-bottom: @spacing-lg;
        color: @text-light;
        font-size: @font-size-sm;

        @media (max-width: @screen-sm) {
          flex-direction: column;
          gap: @spacing-sm;
        }

        .publish-time {
          display: flex;
          align-items: center;
          gap: @spacing-xs;
        }

        .view-count {
          display: flex;
          align-items: center;
          gap: @spacing-xs;

          i {
            color: @primary-color;
          }
        }
      }

      .article-tags {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: @spacing-sm;

        .tag {
          background: @primary-light;
          color: @primary-color;
          padding: 6px 12px;
          border-radius: 16px;
          font-size: @font-size-xs;
          font-weight: @font-weight-medium;
        }
      }

      .article-thumbnail {
        margin-top: @spacing-xl;
        text-align: center;

        img {
          max-width: 100%;
          max-height: 400px;
          border-radius: @border-radius;
          box-shadow: @shadow;

          @media (max-width: @screen-sm) {
            max-height: 250px;
          }
        }
      }
    }

    .article-body {
      .rich-content {
        line-height: 1.8;
        color: @text-color;
        font-size: @font-size-base;



        :deep(p) {
          margin-bottom: @spacing-lg;
          line-height: 1.8;
        }



        :deep(img) {
          width: 100%;
          height: auto;
          border-radius: @border-radius;
          margin: @spacing-lg 0;
          box-shadow: @shadow;
        }


      }

      .no-content {
        text-align: center;
        padding: @spacing-xxl 0;
        color: @text-light;
      }
    }

    .related-knowledge {
      margin-top: @spacing-xxl * 2;
      padding-top: @spacing-xxl;
      border-top: 1px solid @border-color;

      h3 {
        font-size: 1.5rem;
        font-weight: @font-weight-bold;
        color: @text-color;
        margin-bottom: @spacing-xl;
        text-align: center;
      }

      .related-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: @spacing-lg;

        .related-item {
          background: white;
          border: 1px solid @border-color;
          border-radius: @border-radius;
          padding: @spacing-lg;
          cursor: pointer;
          transition: @transition;

          &:hover {
            border-color: @primary-color;
            transform: translateY(-2px);
            box-shadow: @shadow-hover;
          }

          h4 {
            font-size: @font-size-lg;
            font-weight: @font-weight-medium;
            color: @text-color;
            margin-bottom: @spacing-md;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .related-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: @text-light;
            font-size: @font-size-sm;

            .view-count {
              display: flex;
              align-items: center;
              gap: @spacing-xs;

              i {
                color: @primary-color;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: @screen-sm) {
  .knowledge-detail {
    background: #f8f9fa;

    .detail-content {
      padding: @spacing-xl 0;

      .container {
        padding: 0 @spacing-md;
      }

      .article-header {
        margin-bottom: @spacing-xl;
        padding-bottom: @spacing-lg;

        .article-title {
          font-size: 1.8rem;
        }
      }

      .related-knowledge {
        margin-top: @spacing-xl;
        padding-top: @spacing-lg;

        .related-list {
          grid-template-columns: 1fr;
          gap: @spacing-md;

          .related-item {
            padding: @spacing-md;
          }
        }
      }
    }
  }
}
</style>
