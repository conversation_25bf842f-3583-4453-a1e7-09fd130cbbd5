const express = require('express');
const { query, transaction } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取菜单列表（树形结构）
router.get('/', asyncHandler(async (req, res) => {
  const { status } = req.query;
  
  let sql = 'SELECT * FROM menus';
  let params = [];
  
  if (status !== undefined) {
    sql += ' WHERE status = ?';
    params.push(parseInt(status));
  }
  
  sql += ' ORDER BY parent_id ASC, sort_order ASC';
  
  const menus = await query(sql, params);
  
  // 构建树形结构
  const menuTree = buildMenuTree(menus);
  
  res.json({
    success: true,
    message: '获取菜单列表成功',
    data: {
      menus: menuTree,
      total: menus.length
    }
  });
}));

// 获取单个菜单详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const menus = await query('SELECT * FROM menus WHERE id = ?', [id]);
  
  if (menus.length === 0) {
    throw new CustomError('菜单不存在', 404);
  }
  
  res.json({
    success: true,
    message: '获取菜单详情成功',
    data: {
      menu: menus[0]
    }
  });
}));

// 创建菜单
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { name, path, url, parent_id = 0, sort_order = 0, status = 1, description } = req.body;

  // 参数验证
  if (!name || !path) {
    throw new CustomError('菜单名称和路径不能为空', 400);
  }

  // 检查路径是否已存在
  const existingMenus = await query('SELECT id FROM menus WHERE path = ?', [path]);
  if (existingMenus.length > 0) {
    throw new CustomError('菜单路径已存在', 400);
  }

  // 如果有父级菜单，检查父级菜单是否存在
  if (parent_id > 0) {
    const parentMenus = await query('SELECT id FROM menus WHERE id = ?', [parent_id]);
    if (parentMenus.length === 0) {
      throw new CustomError('父级菜单不存在', 400);
    }

    // 检查是否超过二级菜单限制
    const parentLevel = await query('SELECT parent_id FROM menus WHERE id = ?', [parent_id]);
    if (parentLevel[0].parent_id > 0) {
      throw new CustomError('最多只支持二级菜单', 400);
    }
  }

  const result = await query(
    'INSERT INTO menus (name, path, url, parent_id, sort_order, status, description) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [name, path, url || path, parent_id, sort_order, status, description || null]
  );

  res.json({
    success: true,
    message: '创建菜单成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新菜单
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, path, url, parent_id, sort_order, status, description } = req.body;
  
  // 检查菜单是否存在
  const existingMenus = await query('SELECT * FROM menus WHERE id = ?', [id]);
  if (existingMenus.length === 0) {
    throw new CustomError('菜单不存在', 404);
  }
  
  // 参数验证
  if (!name || !path) {
    throw new CustomError('菜单名称和路径不能为空', 400);
  }
  
  // 检查路径是否已被其他菜单使用
  const duplicateMenus = await query('SELECT id FROM menus WHERE path = ? AND id != ?', [path, id]);
  if (duplicateMenus.length > 0) {
    throw new CustomError('菜单路径已存在', 400);
  }
  
  // 如果修改了父级菜单，进行相关检查
  if (parent_id !== undefined) {
    // 不能将菜单设置为自己的子菜单
    if (parent_id == id) {
      throw new CustomError('不能将菜单设置为自己的子菜单', 400);
    }
    
    // 如果有父级菜单，检查父级菜单是否存在
    if (parent_id > 0) {
      const parentMenus = await query('SELECT id, parent_id FROM menus WHERE id = ?', [parent_id]);
      if (parentMenus.length === 0) {
        throw new CustomError('父级菜单不存在', 400);
      }
      
      // 检查是否超过二级菜单限制
      if (parentMenus[0].parent_id > 0) {
        throw new CustomError('最多只支持二级菜单', 400);
      }
    }
    
    // 检查是否有子菜单，如果有则不能设置父级菜单
    const childMenus = await query('SELECT id FROM menus WHERE parent_id = ?', [id]);
    if (childMenus.length > 0 && parent_id > 0) {
      throw new CustomError('该菜单有子菜单，不能设置父级菜单', 400);
    }
  }
  
  const currentMenu = existingMenus[0];
  
  await query(
    'UPDATE menus SET name = ?, path = ?, url = ?, parent_id = ?, sort_order = ?, status = ?, description = ?, updated_at = NOW() WHERE id = ?',
    [
      name,
      path,
      url !== undefined ? url : currentMenu.url,
      parent_id !== undefined ? parent_id : currentMenu.parent_id,
      sort_order !== undefined ? sort_order : currentMenu.sort_order,
      status !== undefined ? status : currentMenu.status,
      description !== undefined ? description : currentMenu.description,
      id
    ]
  );
  
  res.json({
    success: true,
    message: '更新菜单成功'
  });
}));

// 删除菜单
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查菜单是否存在
  const existingMenus = await query('SELECT * FROM menus WHERE id = ?', [id]);
  if (existingMenus.length === 0) {
    throw new CustomError('菜单不存在', 404);
  }
  
  // 检查是否有子菜单
  const childMenus = await query('SELECT id FROM menus WHERE parent_id = ?', [id]);
  if (childMenus.length > 0) {
    throw new CustomError('该菜单有子菜单，请先删除子菜单', 400);
  }
  
  await query('DELETE FROM menus WHERE id = ?', [id]);
  
  res.json({
    success: true,
    message: '删除菜单成功'
  });
}));

// 批量更新菜单排序
router.post('/sort', authenticateToken, asyncHandler(async (req, res) => {
  const { menus } = req.body;
  
  if (!Array.isArray(menus) || menus.length === 0) {
    throw new CustomError('菜单数据格式错误', 400);
  }
  
  await transaction(async (connection) => {
    for (const menu of menus) {
      if (!menu.id || menu.sort_order === undefined) {
        throw new CustomError('菜单ID和排序不能为空', 400);
      }
      
      await connection.execute(
        'UPDATE menus SET sort_order = ?, updated_at = NOW() WHERE id = ?',
        [menu.sort_order, menu.id]
      );
    }
  });
  
  res.json({
    success: true,
    message: '菜单排序更新成功'
  });
}));

// 构建菜单树形结构
function buildMenuTree(menus) {
  const menuMap = new Map();
  const rootMenus = [];
  
  // 创建菜单映射
  menus.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] });
  });
  
  // 构建树形结构
  menus.forEach(menu => {
    const menuItem = menuMap.get(menu.id);
    if (menu.parent_id === 0) {
      rootMenus.push(menuItem);
    } else {
      const parent = menuMap.get(menu.parent_id);
      if (parent) {
        parent.children.push(menuItem);
      }
    }
  });
  
  return rootMenus;
}

module.exports = router;
