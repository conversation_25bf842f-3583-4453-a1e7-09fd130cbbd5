<template>
  <section class="page-header">
    <div class="page-header-bg">
      <img :src="isMobile ? bannerData.mobile_image || bannerData.image : bannerData.image" :alt="bannerData.title">
    </div>
    <div class="page-header-content">
      <div class="container">
        <h1>{{ bannerData.title }}</h1>
        <p v-if="bannerData.subtitle">{{ bannerData.subtitle }}</p>
        <slot name="content"></slot>
      </div>
    </div>
  </section>
</template>

<script>
import { isMobile } from '@/utils/deviceDetect'
export default {
  name: 'PageHeader',
  data() {
    return {
      isMobile: isMobile(),
      bannerData: {
        image: '',
        title: '',
        subtitle: ''
      }
    }
  },
  props: {
    tag: {
      type: String,
      required: true
    },
  },
  methods: {
    // 获取首页banner数据
    getBannerData() {
      const bannerData = localStorage.getItem('bannerData')
      if (bannerData) {
        this.bannerData = JSON.parse(bannerData).filter(item => item.tags.includes(this.tag))[0]
      }
    },
  },
  mounted() {
    console.log(this.tag)
    this.getBannerData()
  }
}
</script>

<style lang="less" scoped>
.page-header-content {
  display: none;
}

.page-header-bg {
  img {
    background-color: #fafafa;
  }
}
</style>
