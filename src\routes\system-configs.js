const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler, CustomError } = require('../middleware/errorHandler');

const router = express.Router();

// 获取系统配置列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, configType, configKey } = req.query;
  const offset = (page - 1) * limit;
  
  let whereConditions = [];
  let params = [];
  
  // 配置类型筛选
  if (configType && configType.trim() !== '') {
    whereConditions.push('config_type = ?');
    params.push(configType);
  }
  
  // 配置键名筛选
  if (configKey && configKey.trim() !== '') {
    whereConditions.push('config_key LIKE ?');
    params.push(`%${configKey}%`);
  }
  
  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
  
  // 查询配置列表
  const configs = await query(`
    SELECT 
      id,
      config_key,
      config_name,
      config_desc,
      config_value,
      config_type,
      status,
      created_at,
      updated_at
    FROM system_configs 
    ${whereClause}
    ORDER BY config_type ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `, params);
  
  // 查询总数
  const countResult = await query(`
    SELECT COUNT(*) as total 
    FROM system_configs 
    ${whereClause}
  `, params);
  
  const total = countResult[0].total;
  const pages = Math.ceil(total / limit);
  
  // 处理配置值，添加统计信息
  const processedConfigs = configs.map(config => ({
    ...config,
    config_value: JSON.parse(config.config_value),
    item_count: JSON.parse(config.config_value).length
  }));
  
  res.json({
    success: true,
    message: '获取配置列表成功',
    data: {
      configs: processedConfigs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages
      }
    }
  });
}));

// 获取配置详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const configs = await query(`
    SELECT * FROM system_configs WHERE id = ?
  `, [id]);
  
  if (configs.length === 0) {
    throw new CustomError('配置不存在', 404);
  }
  
  const config = configs[0];
  config.config_value = JSON.parse(config.config_value);
  
  res.json({
    success: true,
    message: '获取配置详情成功',
    data: {
      config
    }
  });
}));

// 根据配置键获取配置（前端使用）
router.get('/key/:configKey', asyncHandler(async (req, res) => {
  const { configKey } = req.params;
  
  const configs = await query(`
    SELECT config_value FROM system_configs 
    WHERE config_key = ? AND status = 1
  `, [configKey]);
  
  if (configs.length === 0) {
    throw new CustomError('配置不存在', 404);
  }
  
  const configValue = JSON.parse(configs[0].config_value);
  // 只返回启用的配置项
  const activeItems = configValue.filter(item => item.status === 1);
  
  res.json({
    success: true,
    message: '获取配置成功',
    data: activeItems
  });
}));

// 创建配置
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    config_key,
    config_name,
    config_desc = '',
    config_value,
    config_type = 'category'
  } = req.body;
  
  // 参数验证
  if (!config_key || !config_name || !config_value) {
    throw new CustomError('配置键名、配置名称和配置值不能为空', 400);
  }
  
  // 检查配置键是否已存在
  const existingConfigs = await query(`
    SELECT id FROM system_configs WHERE config_key = ?
  `, [config_key]);
  
  if (existingConfigs.length > 0) {
    throw new CustomError('配置键名已存在', 400);
  }
  
  // 验证配置值是否为有效JSON
  let parsedValue;
  try {
    parsedValue = typeof config_value === 'string' ? JSON.parse(config_value) : config_value;
  } catch (error) {
    throw new CustomError('配置值必须是有效的JSON格式', 400);
  }
  
  const result = await query(`
    INSERT INTO system_configs 
    (config_key, config_name, config_desc, config_value, config_type) 
    VALUES (?, ?, ?, ?, ?)
  `, [config_key, config_name, config_desc, JSON.stringify(parsedValue), config_type]);
  
  res.json({
    success: true,
    message: '创建配置成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新配置
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    config_key,
    config_name,
    config_desc,
    config_value,
    config_type,
    status
  } = req.body;
  
  // 检查配置是否存在
  const existingConfigs = await query(`
    SELECT * FROM system_configs WHERE id = ?
  `, [id]);
  
  if (existingConfigs.length === 0) {
    throw new CustomError('配置不存在', 404);
  }
  
  // 参数验证
  if (!config_key || !config_name || !config_value) {
    throw new CustomError('配置键名、配置名称和配置值不能为空', 400);
  }
  
  // 检查配置键是否与其他记录冲突
  const duplicateConfigs = await query(`
    SELECT id FROM system_configs 
    WHERE config_key = ? AND id != ?
  `, [config_key, id]);
  
  if (duplicateConfigs.length > 0) {
    throw new CustomError('配置键名已存在', 400);
  }
  
  // 验证配置值是否为有效JSON
  let parsedValue;
  try {
    parsedValue = typeof config_value === 'string' ? JSON.parse(config_value) : config_value;
  } catch (error) {
    throw new CustomError('配置值必须是有效的JSON格式', 400);
  }
  
  await query(`
    UPDATE system_configs 
    SET config_key = ?, config_name = ?, config_desc = ?, 
        config_value = ?, config_type = ?, status = ?
    WHERE id = ?
  `, [config_key, config_name, config_desc || '', JSON.stringify(parsedValue), 
      config_type || 'category', status !== undefined ? status : 1, id]);
  
  res.json({
    success: true,
    message: '更新配置成功'
  });
}));

// 删除配置
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查配置是否存在
  const existingConfigs = await query(`
    SELECT * FROM system_configs WHERE id = ?
  `, [id]);
  
  if (existingConfigs.length === 0) {
    throw new CustomError('配置不存在', 404);
  }
  
  await query('DELETE FROM system_configs WHERE id = ?', [id]);
  
  res.json({
    success: true,
    message: '删除配置成功'
  });
}));

module.exports = router;
