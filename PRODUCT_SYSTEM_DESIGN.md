# 产品分类系统设计方案

## 1. 前端展示设计

### 1.1 多级分类导航方案

#### 方案A：侧边栏树形导航 + 主内容区展示
```
┌─────────────────┬─────────────────────────────────┐
│   产品分类      │         产品展示区域              │
│                 │                                 │
│ ▼ 壁挂炉        │  ┌─────┐ ┌─────┐ ┌─────┐        │
│   ├─ 雅图       │  │产品1│ │产品2│ │产品3│        │
│   └─ 雅坛       │  └─────┘ └─────┘ └─────┘        │
│                 │                                 │
│ ▼ 控制系统      │  ┌─────┐ ┌─────┐ ┌─────┐        │
│   ├─ 中控       │  │产品4│ │产品5│ │产品6│        │
│   ├─ 分集水器   │  └─────┘ └─────┘ └─────┘        │
│   ├─ 温控面板   │                                 │
│   ├─ 电热执行器 │                                 │
│   └─ 温控阀     │                                 │
│                 │                                 │
│ ▼ 末端          │                                 │
│   ├─ 管道       │                                 │
│   │  ├─ pert    │                                 │
│   │  ├─ pexb    │                                 │
│   │  └─ 铝塑管  │                                 │
│   └─ 散热器     │                                 │
│      ├─ 钢制板式│                                 │
│      └─ 毛巾架  │                                 │
└─────────────────┴─────────────────────────────────┘
```

#### 方案B：面包屑导航 + 卡片式分类
```
首页 > 产品中心 > 控制系统 > 中控

┌─────────────────────────────────────────────────────┐
│                   产品分类                          │
│  ┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐                │
│  │壁挂炉│  │控制系│  │ 末端 │  │其他产│                │
│  │     │  │ 统  │  │     │  │ 品  │                │
│  └─────┘  └─────┘  └─────┘  └─────┘                │
│                                                     │
│              控制系统子分类                         │
│  ┌─────┐  ┌─────┐  ┌─────┐  ┌─────┐                │
│  │ 中控 │  │分集水│  │温控面│  │电热执│                │
│  │     │  │ 器  │  │ 板  │  │行器 │                │
│  └─────┘  └─────┘  └─────┘  └─────┘                │
│                                                     │
│                  产品展示                           │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐               │
│  │  产品1  │ │  产品2  │ │  产品3  │               │
│  │         │ │         │ │         │               │
│  └─────────┘ └─────────┘ └─────────┘               │
└─────────────────────────────────────────────────────┘
```

### 1.2 移动端适配方案

#### 移动端导航设计
```
┌─────────────────────┐
│  ☰ 产品分类  🔍     │  <- 顶部导航栏
├─────────────────────┤
│                     │
│   当前分类：控制系统  │  <- 当前位置指示
│                     │
│  ┌─────┐ ┌─────┐    │  <- 子分类快速选择
│  │中控 │ │分集水│    │
│  └─────┘ └─────┘    │
│  ┌─────┐ ┌─────┐    │
│  │温控面│ │电热执│    │
│  └─────┘ └─────┘    │
│                     │
│  ┌─────────────────┐│  <- 产品列表（垂直布局）
│  │     产品1       ││
│  │   [图片]        ││
│  │   产品名称      ││
│  │   简短描述      ││
│  └─────────────────┘│
│                     │
│  ┌─────────────────┐│
│  │     产品2       ││
│  └─────────────────┘│
└─────────────────────┘
```

## 2. 数据库设计

### 2.1 产品分类表 (product_categories)
```sql
CREATE TABLE product_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  slug VARCHAR(100) NOT NULL COMMENT 'URL友好的标识符',
  parent_id INT DEFAULT 0 COMMENT '父级分类ID',
  level TINYINT DEFAULT 1 COMMENT '分类层级 1-4',
  sort_order INT DEFAULT 0 COMMENT '排序序号',
  status TINYINT DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
  description TEXT COMMENT '分类描述',
  image VARCHAR(255) COMMENT '分类图片',
  seo_title VARCHAR(200) COMMENT 'SEO标题',
  seo_description TEXT COMMENT 'SEO描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_parent_id (parent_id),
  INDEX idx_level (level),
  INDEX idx_sort_order (sort_order),
  INDEX idx_status (status),
  UNIQUE KEY uk_slug (slug)
);
```

### 2.2 产品表 (products)
```sql
CREATE TABLE products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(200) NOT NULL COMMENT '产品名称',
  slug VARCHAR(200) NOT NULL COMMENT 'URL友好标识符',
  category_id INT NOT NULL COMMENT '所属分类ID',
  model VARCHAR(100) COMMENT '产品型号',
  brand VARCHAR(100) COMMENT '品牌',
  price DECIMAL(10,2) COMMENT '价格',
  market_price DECIMAL(10,2) COMMENT '市场价',
  summary TEXT COMMENT '产品简介',
  description LONGTEXT COMMENT '详细描述',
  specifications JSON COMMENT '产品规格参数',
  features JSON COMMENT '产品特点',
  images JSON COMMENT '产品图片集',
  main_image VARCHAR(255) COMMENT '主图',
  video_url VARCHAR(255) COMMENT '产品视频',
  downloads JSON COMMENT '下载资料',
  status TINYINT DEFAULT 1 COMMENT '状态: 1-上架, 0-下架',
  is_featured TINYINT DEFAULT 0 COMMENT '是否推荐',
  is_new TINYINT DEFAULT 0 COMMENT '是否新品',
  sort_order INT DEFAULT 0 COMMENT '排序',
  view_count INT DEFAULT 0 COMMENT '浏览次数',
  seo_title VARCHAR(200) COMMENT 'SEO标题',
  seo_description TEXT COMMENT 'SEO描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category_id (category_id),
  INDEX idx_status (status),
  INDEX idx_featured (is_featured),
  INDEX idx_sort_order (sort_order),
  UNIQUE KEY uk_slug (slug),
  FOREIGN KEY (category_id) REFERENCES product_categories(id)
);
```

### 2.3 产品标签关联表 (product_tags)
```sql
CREATE TABLE product_tags (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT NOT NULL,
  tag_name VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_product_id (product_id),
  INDEX idx_tag_name (tag_name),
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

## 3. 管理后台功能设计

### 3.1 分类管理功能
- 树形结构展示所有分类
- 支持拖拽排序
- 支持批量操作（启用/禁用/删除）
- 分类层级限制（最多4级）
- 分类路径自动生成

### 3.2 产品管理功能
- 产品列表（支持分类筛选、状态筛选、搜索）
- 产品编辑器（富文本编辑器支持）
- 图片批量上传和管理
- 产品规格参数动态配置
- 产品关联推荐
- SEO优化设置

### 3.3 数据统计功能
- 分类产品数量统计
- 产品浏览量统计
- 热门产品排行
- 分类访问热度分析

## 4. API接口设计

### 4.1 分类相关接口
- GET /api/categories - 获取分类树
- GET /api/categories/:id/products - 获取分类下的产品
- GET /api/categories/:id/children - 获取子分类

### 4.2 产品相关接口
- GET /api/products - 产品列表（支持分页、筛选、排序）
- GET /api/products/:id - 产品详情
- GET /api/products/featured - 推荐产品
- GET /api/products/search - 产品搜索

## 5. 前端组件设计

### 5.1 分类导航组件
- CategoryTree.vue - 树形分类导航
- CategoryBreadcrumb.vue - 面包屑导航
- CategoryFilter.vue - 分类筛选器

### 5.2 产品展示组件
- ProductGrid.vue - 产品网格布局
- ProductCard.vue - 产品卡片
- ProductDetail.vue - 产品详情
- ProductSearch.vue - 产品搜索

### 5.3 移动端组件
- MobileCategoryNav.vue - 移动端分类导航
- MobileProductList.vue - 移动端产品列表
- MobileProductFilter.vue - 移动端筛选器
