<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>公司荣誉</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加荣誉
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="荣誉标题">
            <el-input v-model="searchForm.title" placeholder="请输入荣誉标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 荣誉表格 -->
      <el-table v-loading="loading" :data="honorsList" border class="honors-table">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="荣誉图片" width="120">
          <template slot-scope="scope">
            <img v-if="scope.row.image_url" :src="scope.row.image_url" alt="荣誉图片" class="honor-image"
              @click="previewImage(scope.row.image_url)" />
            <span v-else class="no-image">无图片</span>
          </template>
        </el-table-column>

        <el-table-column prop="title" label="荣誉标题" min-width="150" />

        <el-table-column prop="summary" label="荣誉摘要" min-width="200" show-overflow-tooltip />

        <el-table-column label="获取时间" width="120">
          <template slot-scope="scope">
            {{ scope.row.award_date || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="description" label="详细描述" min-width="200" show-overflow-tooltip />

        <el-table-column prop="sort_order" label="排序" width="80" />

        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="left">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current_page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.per_page"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total_items" />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="handleDialogClose">
      <el-form ref="honorForm" :model="honorForm" :rules="formRules" label-width="100px">
        <el-form-item label="荣誉标题" prop="title">
          <el-input v-model="honorForm.title" placeholder="请输入荣誉标题" maxlength="200" show-word-limit />
        </el-form-item>

        <el-form-item label="荣誉摘要" prop="summary">
          <el-input v-model="honorForm.summary" type="textarea" :rows="3" placeholder="请输入荣誉摘要" maxlength="500"
            show-word-limit />
        </el-form-item>

        <el-form-item label="获取时间" prop="award_date">
          <el-date-picker v-model="honorForm.award_date" type="date" placeholder="请选择获取时间" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" style="width: 100%;" />
        </el-form-item>

        <el-form-item label="荣誉图片" prop="image_url">
          <ImageUploader v-model="honorForm.image_url" size="150x200" tip="建议尺寸：300x400px（竖图），支持jpg、png格式"
            :max-size="5" />
        </el-form-item>

        <el-form-item label="详细描述" prop="description">
          <el-input v-model="honorForm.description" type="textarea" :rows="3" placeholder="请输入详细描述" maxlength="500"
            show-word-limit />
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="honorForm.sort_order" :min="0" :max="9999" placeholder="数字越小排序越靠前" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="honorForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="previewVisible" width="60%" center>
      <div class="image-preview">
        <img :src="previewImageUrl" alt="预览图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { companyHonors } from '@/api'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  name: 'CompanyHonors',
  components: {
    ImageUploader
  },
  data() {
    return {
      loading: false,
      submitting: false,
      honorsList: [],
      searchForm: {
        title: '',
        status: ''
      },
      pagination: {
        current_page: 1,
        per_page: 10,
        total_items: 0,
        total_pages: 0
      },
      dialogVisible: false,
      isEdit: false,
      honorForm: {
        title: '',
        summary: '',
        award_date: '',
        image_url: '',
        description: '',
        sort_order: 0,
        status: 1
      },
      formRules: {
        title: [
          { required: true, message: '请输入荣誉标题', trigger: 'blur' },
          { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
        ],
        summary: [
          { required: true, message: '请输入荣誉摘要', trigger: 'blur' },
          { min: 10, max: 500, message: '摘要长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        award_date: [
          { required: true, message: '请选择获取时间', trigger: 'change' }
        ],
        image_url: [
          { required: true, message: '请上传荣誉图片', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入详细描述', trigger: 'blur' }
        ]
      },
      previewVisible: false,
      previewImageUrl: ''
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑荣誉' : '添加荣誉'
    }
  },
  created() {
    this.fetchHonorsList()
  },
  methods: {
    // 获取荣誉列表
    async fetchHonorsList() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.current_page,
          limit: this.pagination.per_page,
          title: this.searchForm.title,
          status: this.searchForm.status
        }

        const response = await companyHonors.getList(params)
        this.honorsList = response.data.honors || []
        this.pagination = response.data.pagination || {}
      } catch (error) {
        console.error('获取荣誉列表失败:', error)
        this.$message.error('获取荣誉列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current_page = 1
      this.fetchHonorsList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        status: ''
      }
      this.pagination.current_page = 1
      this.fetchHonorsList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.per_page = val
      this.pagination.current_page = 1
      this.fetchHonorsList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current_page = val
      this.fetchHonorsList()
    },

    // 添加
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
      this.honorForm = {
        image_url: '',
        description: '',
        sort_order: 0,
        status: 1
      }
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.dialogVisible = true
      this.honorForm = {
        id: row.id,
        title: row.title || '',
        summary: row.summary || '',
        award_date: row.award_date || '',
        image_url: row.image_url,
        description: row.description,
        sort_order: row.sort_order,
        status: row.status
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除荣誉 "${row.description}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await companyHonors.delete(row.id)
          this.$message.success('删除成功')
          this.fetchHonorsList()
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.honorForm.validate()
        this.submitting = true

        if (this.isEdit) {
          await companyHonors.update(this.honorForm.id, this.honorForm)
          this.$message.success('更新成功')
        } else {
          await companyHonors.create(this.honorForm)
          this.$message.success('创建成功')
        }

        this.dialogVisible = false
        this.fetchHonorsList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.honorForm.resetFields()
      this.honorForm = {
        title: '',
        summary: '',
        award_date: '',
        image_url: '',
        description: '',
        sort_order: 0,
        status: 1
      }
      this.isEdit = false
    },

    // 预览图片
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl
      this.previewVisible = true
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.honors-table {
  margin-bottom: 20px;
}

.honor-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.honor-image:hover {
  transform: scale(1.1);
}

.no-image {
  color: #999;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409EFF;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 500px;
}
</style>
