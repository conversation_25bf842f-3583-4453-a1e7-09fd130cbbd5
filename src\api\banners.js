import { request } from './http'

// banner管理API服务
export const banners = {
  // 获取banner列表
  getList(params = {}) {
    return request.get('/api/banners', params)
  },

  // 获取banner详情
  getDetail(id) {
    return request.get(`/api/banners/${id}`)
  },

  // 创建banner
  create(data) {
    return request.post('/api/banners', data)
  },

  // 更新banner
  update(id, data) {
    return request.put(`/api/banners/${id}`, data)
  },

  // 删除banner
  delete(id) {
    return request.delete(`/api/banners/${id}`)
  },

  // 切换banner状态
  toggleStatus(id, status) {
    return request.patch(`/api/banners/${id}/status`, { status })
  }
}

export default banners 