import { request } from './http'

// 新闻资讯API服务
export const news = {
  // 获取新闻列表
  getList(params = {}) {
    return request.get('/api/news', params)
  },

  // 获取新闻详情
  getDetail(id) {
    return request.get(`/api/news/${id}`)
  },

  // 创建新闻
  create(data) {
    return request.post('/api/news', data)
  },

  // 更新新闻
  update(id, data) {
    return request.put(`/api/news/${id}`, data)
  },

  // 删除新闻
  delete(id) {
    return request.delete(`/api/news/${id}`)
  },

  // 切换新闻状态
  toggleStatus(id, status) {
    return request.patch(`/api/news/${id}/status`, { status })
  }
}

export default news
