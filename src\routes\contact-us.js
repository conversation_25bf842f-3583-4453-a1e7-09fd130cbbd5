const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler, CustomError } = require('../middleware/errorHandler');

// 获取联系我们内容
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const contacts = await query(`
    SELECT 
      id,
      title,
      summary,
      content,
      created_at,
      updated_at
    FROM contact_us
    ORDER BY id DESC
    LIMIT 1
  `);

  const contact = contacts.length > 0 ? contacts[0] : null;

  res.json({
    success: true,
    message: '获取联系我们内容成功',
    data: {
      contact
    }
  });
}));

// 创建或更新联系我们内容
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    title,
    summary,
    content
  } = req.body;

  // 参数验证
  if (!title) {
    throw new CustomError('标题不能为空', 400);
  }

  // 检查是否已存在记录
  const existingContacts = await query(`
    SELECT id FROM contact_us LIMIT 1
  `);

  let result;
  if (existingContacts.length > 0) {
    // 更新现有记录
    result = await query(`
      UPDATE contact_us 
      SET title = ?, summary = ?, content = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [title, summary || null, content || null, existingContacts[0].id]);
    
    res.json({
      success: true,
      message: '更新联系我们内容成功',
      data: {
        id: existingContacts[0].id
      }
    });
  } else {
    // 创建新记录
    result = await query(`
      INSERT INTO contact_us (title, summary, content)
      VALUES (?, ?, ?)
    `, [title, summary || null, content || null]);

    res.json({
      success: true,
      message: '创建联系我们内容成功',
      data: {
        id: result.insertId
      }
    });
  }
}));

module.exports = router;
