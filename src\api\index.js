import axios from 'axios'
// import menuCache from '@/utils/menuCache'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? 'http://**************:3333'
    : 'http://localhost:3333',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 菜单相关API
export const API = {
  // 获取前端官网菜单列表
  async getMenuList() {
    return await api.get('/api/frontend-menus')
  },
  // 获取banner数据
  async getBannerData() {
    return await api.get('api/front/banners')
  },

  // 获取页面内容块
  async getPageContentBlock(pageKey, blockKey) {
    return await api.get(`/api/front/page-content-blocks/${pageKey}/${blockKey}`)
  },

  // 获取系统配置（分类等）
  async getSystemConfig(configKey) {
    return await api.get(`/api/system-configs/key/${configKey}`)
  },
  // 获取标签类型图片
  async getImageByTag(tag) {
    return await api.get(`/api/front/images?tag=${tag}`)
  }

}

export default api
