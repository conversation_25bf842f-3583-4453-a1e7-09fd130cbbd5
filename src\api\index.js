import { request } from './http'
import { energyCases } from './energyCases'
import { dealers } from './dealers'
import { news } from './news'
import { resourceLibrary } from './resource-library'
import { videoTutorials } from './video-tutorials'
import { heatingKnowledge } from './heating-knowledge'
import aboutUsAPI from './about-us'
import { contactUs } from './contact-us'
import { pageContentBlocks } from './page-content-blocks'
import { systemConfigs } from './system-configs'

// 认证相关API
export const auth = {
  // 登录
  login(data) {
    return request.post('/api/auth/login', data)
  },

  // 获取用户信息
  getUserInfo() {
    return request.get('/api/auth/me')
  },

  // 修改密码
  changePassword(data) {
    return request.post('/api/auth/change-password', data)
  }
}

// 菜单相关API
export const menu = {
  // 获取菜单列表
  getList() {
    return request.get('/api/menus')
  },

  // 创建菜单
  create(data) {
    return request.post('/api/menus', data)
  },

  // 更新菜单
  update(id, data) {
    return request.put(`/api/menus/${id}`, data)
  },

  // 删除菜单
  delete(id) {
    return request.delete(`/api/menus/${id}`)
  }
}

// 产品分类相关API
export const productCategory = {
  // 获取分类列表
  getList() {
    return request.get('/api/product-categories')
  },

  // 获取分类详情
  getDetail(id) {
    return request.get(`/api/product-categories/${id}`)
  },

  // 创建分类
  create(data) {
    return request.post('/api/product-categories', data)
  },

  // 更新分类
  update(id, data) {
    return request.put(`/api/product-categories/${id}`, data)
  },

  // 删除分类
  delete(id) {
    return request.delete(`/api/product-categories/${id}`)
  }
}

// 产品相关API
export const product = {
  // 获取产品列表
  getList(params) {
    return request.get('/api/products', params)
  },

  // 获取产品详情
  getDetail(id) {
    return request.get(`/api/products/${id}`)
  },

  // 创建产品
  create(data) {
    return request.post('/api/products', data)
  },

  // 更新产品
  update(id, data) {
    return request.put(`/api/products/${id}`, data)
  },

  // 删除产品
  delete(id) {
    return request.delete(`/api/products/${id}`)
  },

  // 批量更新状态
  batchUpdateStatus(data) {
    return request.post('/api/products/batch-status', data)
  }
}

// 文件上传API
export const upload = {
  // 上传图片
  uploadImage(file) {
    const formData = new FormData()
    formData.append('file', file)

    return request.post('/api/upload?type=image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 上传文档
  uploadDocument(file) {
    const formData = new FormData()
    formData.append('file', file)

    return request.post('/api/upload?type=document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 上传任意文件（用于资料库等）
  uploadFile(file) {
    const formData = new FormData()
    formData.append('file', file)

    return request.post('/api/upload?type=any', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 导出关于我们相关API（命名导出）
export const companyProfile = aboutUsAPI.companyProfile
export const companyHonors = aboutUsAPI.companyHonors
export const developmentHistory = aboutUsAPI.developmentHistory
export const qrCodes = aboutUsAPI.qrCodes
export const contactInformation = aboutUsAPI.contactInformation

// 导出联系我们API（命名导出）
export { contactUs }

// 导出默认API实例和所有API模块
export default {
  auth,
  menu,
  productCategory,
  product,
  energyCases,
  dealers,
  news,
  resourceLibrary,
  videoTutorials,
  heatingKnowledge,
  upload,
  contactUs,
  pageContentBlocks,
  systemConfigs,
  // 关于我们相关API
  ...aboutUsAPI
}
