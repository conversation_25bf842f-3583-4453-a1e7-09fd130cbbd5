<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>公司简介</h2>
        <el-button type="primary" icon="el-icon-edit" @click="handleEdit" :loading="loading">
          编辑简介
        </el-button>
      </div>

      <!-- 公司简介展示 -->
      <div v-if="!isEditing" class="profile-display">
        <!-- <div class="image-section">
          <div class="main-image" v-if="profile.main_image">
            <el-image style="width: 200px; height: 200px;" fit="cover" :src="profile.main_image" alt="公司主图"
              :preview-src-list="[profile.main_image]" />
          </div>
          <div class="sub-image" v-if="profile.sub_image">
            <el-image style="width: 200px; height: 200px;" fit="cover" :src="profile.sub_image" alt="公司副图"
              :preview-src-list="[profile.sub_image]" />
          </div>
        </div> -->

        <div class="content-section">
          <div class="summary-section">
            <h3>公司摘要</h3>
            <p>{{ profile.summary || '暂无摘要' }}</p>
          </div>

          <div class="detail-section">
            <h3>详细介绍</h3>
            <div class="content" v-html="profile.content || '暂无详细介绍'"></div>
          </div>

          <div class="culture-section">
            <h3>公司文化</h3>
            <div class="content" v-html="profile.culture || '暂无公司文化'"></div>
          </div>
        </div>
      </div>

      <!-- 编辑表单 -->
      <div v-if="isEditing" class="edit-form">
        <el-form ref="profileForm" :model="editForm" :rules="formRules" label-width="120px">
          <!-- <el-form-item label="主图" prop="main_image">
            <ImageUploader v-model="editForm.main_image" size="300x225" tip="建议尺寸：800x600px，支持jpg、png格式"
              :max-size="5" />
          </el-form-item>

          <el-form-item label="副图" prop="sub_image">
            <ImageUploader v-model="editForm.sub_image" size="200x150" tip="建议尺寸：400x300px，支持jpg、png格式" :max-size="5" />
          </el-form-item> -->

          <el-form-item label="公司摘要" prop="summary">
            <el-input v-model="editForm.summary" type="textarea" :rows="4" placeholder="请输入公司简介摘要" maxlength="500"
              show-word-limit />
          </el-form-item>

          <el-form-item label="详细介绍" prop="content">
            <RichTextEditor v-model="editForm.content" placeholder="请输入公司详细介绍" height="400px" />
          </el-form-item>

          <el-form-item label="公司文化" prop="culture">
            <RichTextEditor v-model="editForm.culture" placeholder="请输入公司文化" height="400px" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
            <el-button @click="handleCancel">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { companyProfile } from '@/api'
import RichTextEditor from '@/components/RichTextEditor.vue'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  name: 'CompanyProfile',
  components: {
    RichTextEditor,
    ImageUploader
  },
  data() {
    return {
      loading: false,
      saving: false,
      isEditing: false,
      profile: {},
      editForm: {
        main_image: '',
        sub_image: '',
        summary: '',
        content: '',
        culture: ''
      },
      formRules: {
        summary: [
          { required: true, message: '请输入公司摘要', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入详细介绍', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchProfile()
  },
  methods: {
    // 获取公司简介
    async fetchProfile() {
      try {
        this.loading = true
        const response = await companyProfile.get()
        this.profile = response.data || {}
      } catch (error) {
        console.error('获取公司简介失败:', error)
        this.$message.error('获取公司简介失败')
      } finally {
        this.loading = false
      }
    },

    // 编辑
    handleEdit() {
      this.isEditing = true
      this.editForm = {
        main_image: this.profile.main_image || '',
        sub_image: this.profile.sub_image || '',
        summary: this.profile.summary || '',
        content: this.profile.content || '',
        culture: this.profile.culture || ''
      }
    },

    // 取消编辑
    handleCancel() {
      this.isEditing = false
      this.editForm = {
        main_image: '',
        sub_image: '',
        summary: '',
        content: '',
        culture: ''
      }
    },

    // 保存
    async handleSave() {
      try {
        await this.$refs.profileForm.validate()
        this.saving = true

        await companyProfile.update(this.editForm)

        this.$message.success('保存成功')
        this.isEditing = false
        this.fetchProfile()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.profile-display {
  max-width: 1000px;
}

.image-section {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.main-image,
.sub-image {
  border-radius: 8px;
  overflow: hidden;
}

/* .main-image {
  flex: 2;
}

.sub-image {
  flex: 1;
} */

.main-image .el-image img,
.sub-image .el-image img {
  width: 200px;
  height: 200px;
  display: block;
  object-fit: cover;
}

.content-section {
  line-height: 1.6;
}

.summary-section,
.detail-section,
.culture-section {
  margin-bottom: 30px;
}

.summary-section h3,
.detail-section h3,
.culture-section h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 16px;
  font-weight: 600;
}

.summary-section p {
  color: #666;
  font-size: 16px;
  line-height: 1.8;
}

.content {
  color: #666;
  font-size: 14px;
}

.edit-form {
  width: 100%;
}
</style>
