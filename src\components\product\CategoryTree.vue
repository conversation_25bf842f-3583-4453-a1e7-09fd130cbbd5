<template>
  <div class="category-tree">
    <!-- 桌面端侧边栏导航 -->
    <div class="desktop-nav" v-if="!isMobile">
      <div class="category-header">
        <h3>产品分类</h3>
      </div>
      <div class="category-list">
        <div v-for="category in categories" :key="category.id" class="category-item" :class="{
          active: activeCategory === category.id,
          expanded: expandedCategories.includes(category.id)
        }">
          <div class="category-title" @click="toggleCategory(category)">
            <span class="category-name">{{ category.name }}</span>
            <i v-if="category.children && category.children.length > 0" class="fas fa-chevron-down expand-icon"
              :class="{ rotated: expandedCategories.includes(category.id) }"></i>
          </div>

          <!-- 子分类 -->
          <div v-if="category.children && category.children.length > 0" class="subcategory-list"
            :class="{ expanded: expandedCategories.includes(category.id) }">
            <div v-for="subCategory in category.children" :key="subCategory.id" class="subcategory-item" :class="{
              active: activeCategory === subCategory.id,
              expanded: expandedCategories.includes(subCategory.id)
            }">
              <div class="subcategory-title" @click="toggleCategory(subCategory)">
                <span class="subcategory-name">{{ subCategory.name }}</span>
                <i v-if="subCategory.children && subCategory.children.length > 0"
                  class="fas fa-chevron-down expand-icon"
                  :class="{ rotated: expandedCategories.includes(subCategory.id) }"></i>
              </div>

              <!-- 三级分类 -->
              <div v-if="subCategory.children && subCategory.children.length > 0" class="third-category-list"
                :class="{ expanded: expandedCategories.includes(subCategory.id) }">
                <div v-for="thirdCategory in subCategory.children" :key="thirdCategory.id" class="third-category-item"
                  :class="{
                    active: activeCategory === thirdCategory.id,
                    expanded: expandedCategories.includes(thirdCategory.id)
                  }">
                  <div class="third-category-title" @click="toggleCategory(thirdCategory)">
                    <span class="third-category-name">{{ thirdCategory.name }}</span>
                    <i v-if="thirdCategory.children && thirdCategory.children.length > 0"
                      class="fas fa-chevron-down expand-icon"
                      :class="{ rotated: expandedCategories.includes(thirdCategory.id) }"></i>
                  </div>

                  <!-- 四级分类 -->
                  <div v-if="thirdCategory.children && thirdCategory.children.length > 0" class="fourth-category-list"
                    :class="{ expanded: expandedCategories.includes(thirdCategory.id) }">
                    <div v-for="fourthCategory in thirdCategory.children" :key="fourthCategory.id"
                      class="fourth-category-item" :class="{ active: activeCategory === fourthCategory.id }"
                      @click="selectCategory(fourthCategory)">
                      <span class="fourth-category-name">{{ fourthCategory.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端下拉导航 -->
    <div class="mobile-nav" v-else>
      <div class="mobile-category-selector">
        <button class="category-dropdown-btn" @click="showMobileNav = !showMobileNav">
          <span>{{ currentCategoryName }}</span>
          <i class="fas fa-chevron-down" :class="{ rotated: showMobileNav }"></i>
        </button>

        <div class="mobile-category-dropdown" :class="{ show: showMobileNav }">
          <div class="mobile-category-list">
            <div v-for="category in flatCategories" :key="category.id" class="mobile-category-item"
              :class="{ active: activeCategory === category.id }" @click="selectMobileCategory(category)">
              <span class="level-indicator">{{ '—'.repeat(category.level - 1) }}</span>
              <span class="category-name">{{ category.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategoryTree',
  props: {
    categories: {
      type: Array,
      default: () => []
    },
    activeCategory: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      expandedCategories: [],
      showMobileNav: false,
      isMobile: false
    }
  },
  computed: {
    currentCategoryName() {
      if (!this.activeCategory) return '全部产品'
      const category = this.findCategoryById(this.activeCategory)
      return category ? category.name : '全部产品'
    },
    flatCategories() {
      const flatten = (categories, level = 1) => {
        let result = []
        categories.forEach(category => {
          result.push({ ...category, level })
          if (category.children && category.children.length > 0) {
            result = result.concat(flatten(category.children, level + 1))
          }
        })
        return result
      }
      return [{ id: null, name: '全部产品', level: 1 }, ...flatten(this.categories)]
    }
  },
  methods: {
    toggleCategory(category) {
      if (category.children && category.children.length > 0) {
        const index = this.expandedCategories.indexOf(category.id)
        if (index > -1) {
          this.expandedCategories.splice(index, 1)
        } else {
          this.expandedCategories.push(category.id)
        }
      } else {
        this.selectCategory(category)
      }
    },
    selectCategory(category) {
      this.$emit('category-change', category.id)
    },
    selectMobileCategory(category) {
      this.selectCategory(category)
      this.showMobileNav = false
    },
    findCategoryById(id, categories = this.categories) {
      for (const category of categories) {
        if (category.id === id) {
          return category
        }
        if (category.children && category.children.length > 0) {
          const found = this.findCategoryById(id, category.children)
          if (found) return found
        }
      }
      return null
    },
    checkMobile() {
      this.isMobile = window.innerWidth <= 768
    },
    handleResize() {
      this.checkMobile()
    }
  },
  mounted() {
    this.checkMobile()
    window.addEventListener('resize', this.handleResize)

    // 点击外部关闭移动端导航
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.showMobileNav = false
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style lang="less" scoped>
.category-tree {
  .desktop-nav {
    width: 280px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .category-header {
      background: #D80514;
      color: white;
      padding: 15px 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #fff;
      }
    }

    .category-list {
      max-height: 600px;
      overflow-y: auto;
    }

    .category-item {
      border-bottom: 1px solid #f0f0f0;

      .category-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 20px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #f8f9fa;
        }

        .category-name {
          font-weight: 500;
          color: #333;
        }

        .expand-icon {
          font-size: 12px;
          color: #999;
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(180deg);
          }
        }
      }

      &.active>.category-title {
        background: #D80514;
        color: white;

        .expand-icon {
          color: white;
        }
      }

      .subcategory-list {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: #f8f9fa;

        &.expanded {
          max-height: 500px;
        }

        .subcategory-item {
          .subcategory-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 40px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: #e9ecef;
            }

            .subcategory-name {
              font-size: 14px;
              color: #555;
            }

            .expand-icon {
              font-size: 10px;
              color: #999;
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(180deg);
              }
            }
          }

          &.active>.subcategory-title {
            background: #D80514;
            color: white;

            .subcategory-name {
              color: white;
            }

            .expand-icon {
              color: white;
            }
          }

          .third-category-list {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #e9ecef;

            &.expanded {
              max-height: 300px;
            }

            .third-category-item {
              .third-category-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 60px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                  background: #dee2e6;
                }

                .third-category-name {
                  font-size: 13px;
                  color: #666;
                }

                .expand-icon {
                  font-size: 10px;
                  color: #999;
                  transition: transform 0.3s ease;

                  &.rotated {
                    transform: rotate(180deg);
                  }
                }
              }

              &.active>.third-category-title {
                background: #D80514;
                color: white;

                .third-category-name {
                  color: white;
                }

                .expand-icon {
                  color: white;
                }
              }

              .fourth-category-list {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
                background: #dee2e6;

                &.expanded {
                  max-height: 200px;
                }

                .fourth-category-item {
                  padding: 6px 80px;
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    background: #ced4da;
                  }

                  &.active {
                    background: #D80514;
                    color: white;
                  }

                  .fourth-category-name {
                    font-size: 12px;
                    color: #777;
                  }

                  &.active .fourth-category-name {
                    color: white;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .mobile-nav {
    .mobile-category-selector {
      position: relative;

      .category-dropdown-btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;

        .fas {
          transition: transform 0.3s ease;

          &.rotated {
            transform: rotate(180deg);
          }
        }
      }

      .mobile-category-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        border-radius: 0 0 8px 8px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        z-index: 1000;

        &.show {
          max-height: 400px;
        }

        .mobile-category-list {
          max-height: 400px;
          overflow-y: auto;

          .mobile-category-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            cursor: pointer;
            transition: background 0.3s ease;

            &:hover {
              background: #f8f9fa;
            }

            &.active {
              background: #D80514;
              color: white;
            }

            .level-indicator {
              margin-right: 8px;
              color: #999;
              font-size: 12px;
            }

            .category-name {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .category-tree {
    .desktop-nav {
      display: none;
    }
  }
}

@media (min-width: 769px) {
  .category-tree {
    .mobile-nav {
      display: none;
    }
  }
}
</style>
