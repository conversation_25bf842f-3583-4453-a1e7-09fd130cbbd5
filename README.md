# 雅克菲采暖管理后台

## 项目简介

雅克菲采暖官网管理后台系统，基于Vue 2 + Element UI构建，为管理员提供官网内容管理功能。

## 技术栈

- **Vue.js** 2.6.14 - 渐进式JavaScript框架
- **Element UI** 2.15.14 - 基于Vue的组件库
- **Vue Router** 3.5.4 - 官方路由管理器
- **Vuex** 3.6.2 - 状态管理模式
- **Axios** 1.6.2 - HTTP客户端
- **Less** 4.1.2 - CSS预处理器
- **Webpack** 5 - 模块打包器
- **Babel** - JavaScript编译器

## 项目结构

```
admin/
├── src/
│   ├── main.js               # 应用入口文件
│   ├── App.vue               # 根组件
│   ├── router/
│   │   └── index.js          # 路由配置
│   ├── store/
│   │   └── index.js          # Vuex状态管理
│   ├── api/
│   │   └── index.js          # API接口封装
│   ├── layout/
│   │   └── index.vue         # 主布局组件
│   ├── views/
│   │   ├── Login.vue         # 登录页面
│   │   ├── Dashboard.vue     # 仪表盘
│   │   └── MenuManagement.vue # 菜单管理
│   └── assets/
│       └── styles/
│           └── main.less     # 全局样式
├── public/
│   ├── index.html            # HTML模板
│   └── favicon.ico           # 网站图标
├── webpack.config.js         # Webpack配置
├── package.json              # 项目依赖配置
└── README.md                 # 项目说明文档
```

## 快速开始

### 1. 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- 后端API服务正在运行 (端口3333)

### 2. 安装依赖

```bash
npm install
```

### 3. 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:8084` 启动

### 4. 构建生产版本

```bash
npm run build
```

构建文件将输出到 `dist` 目录

## 功能特性

### 🔐 用户认证
- 管理员登录/登出
- JWT Token认证
- 路由守卫保护
- 自动token刷新

### 📊 仪表盘
- 系统概况展示
- 统计数据可视化
- 快速操作入口
- 系统信息展示

### 🗂️ 菜单管理
- 菜单增删改查
- 树形结构展示
- 支持二级菜单
- 拖拽排序功能
- 状态管理

### 🎨 界面设计
- 响应式布局
- 现代化UI设计
- 暗色侧边栏
- 面包屑导航

## 默认账号

- **用户名**: admin
- **密码**: airfit2025

## 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建Vue组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在布局组件中添加导航菜单

### API调用

使用封装的API服务：

```javascript
import api from '@/api';

// 获取菜单列表
const response = await api.menu.getList();

// 创建菜单
const result = await api.menu.create(menuData);
```

### 状态管理

使用Vuex管理全局状态：

```javascript
import { mapGetters, mapActions } from 'vuex';

export default {
  computed: {
    ...mapGetters(['userInfo', 'token'])
  },
  methods: {
    ...mapActions(['login', 'logout'])
  }
}
```

### 样式开发

使用Less预处理器：

```less
// 使用全局变量
@import '@/assets/styles/variables.less';

.my-component {
  color: @primary-color;
  
  &:hover {
    background: @background-color-light;
  }
}
```

## 项目配置

### Webpack配置

- **开发服务器**: 端口8084，支持热重载
- **API代理**: `/api/*` 请求代理到后端服务器
- **代码分割**: 自动分离第三方库
- **资源优化**: 图片压缩、CSS提取

### 环境变量

通过webpack.DefinePlugin注入：

```javascript
process.env.NODE_ENV // 'development' | 'production'
process.env.BASE_URL // '/'
```

### 路由配置

- **模式**: History模式
- **懒加载**: 组件按需加载
- **路由守卫**: 自动检查登录状态
- **重定向**: 未匹配路由重定向到首页

## 部署说明

### 生产环境构建

```bash
npm run build
```

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name admin.airfit.com;
    root /var/www/airfit-admin/dist;
    index index.html;
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3333/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Docker部署

```dockerfile
# 构建阶段
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 开发规范

### 代码风格

- 使用ES6+语法
- 组件名使用PascalCase
- 文件名使用kebab-case
- 样式使用BEM命名规范

### 组件开发

- 单文件组件(.vue)
- Props类型验证
- 事件命名使用kebab-case
- 合理使用插槽(slot)

### 提交规范

```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 常见问题

### Q: 登录后页面空白
A: 检查后端API服务是否正常运行，确认网络连接

### Q: 开发服务器启动失败
A: 检查端口8084是否被占用，或修改webpack配置中的端口

### Q: API请求失败
A: 确认后端服务运行在3333端口，检查代理配置

### Q: 样式不生效
A: 检查Less语法，确认样式文件正确导入

## 浏览器支持

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 许可证

MIT License

## 联系方式

- 项目地址: https://github.com/yuanyang749/airfit-admin
- 邮箱: <EMAIL>
