# 雅克菲采暖后端API服务

## 项目简介

雅克菲采暖官网后端API服务，提供管理后台所需的数据接口和用户认证功能。

## 技术栈

- **Node.js** - JavaScript运行环境
- **Express.js** - Web应用框架
- **MySQL** - 关系型数据库
- **JWT** - 用户认证
- **bcryptjs** - 密码加密
- **CORS** - 跨域资源共享
- **Helmet** - 安全中间件
- **Express Rate Limit** - 请求限流

## 项目结构

```
backend/
├── src/
│   ├── app.js                 # 应用入口文件
│   ├── config/
│   │   └── database.js        # 数据库配置
│   ├── middleware/
│   │   ├── auth.js           # JWT认证中间件
│   │   └── errorHandler.js   # 错误处理中间件
│   └── routes/
│       ├── auth.js           # 认证相关路由
│       └── menus.js          # 菜单管理路由
├── scripts/
│   └── init-database.js      # 数据库初始化脚本
├── .env                      # 环境变量配置
├── package.json              # 项目依赖配置
└── README.md                 # 项目说明文档
```

## 环境要求

- Node.js >= 14.0.0
- MySQL >= 5.7
- npm >= 6.0.0

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制 `.env` 文件并配置数据库连接信息：

```bash
# 服务器配置
PORT=3333
NODE_ENV=development

# 前端地址
FRONTEND_URL=http://localhost:8082
ADMIN_URL=http://localhost:8084

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=airfit

# JWT配置
JWT_SECRET=airfit_jwt_secret_key_2025
JWT_EXPIRES_IN=24h

# 加密配置
BCRYPT_ROUNDS=12
```

### 3. 初始化数据库

```bash
npm run init-db
```

### 4. 启动服务

```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

服务将在 `http://localhost:3333` 启动

## API接口文档

### 认证接口

#### 登录
- **POST** `/api/auth/login`
- **请求体**:
  ```json
  {
    "username": "admin",
    "password": "airfit2025"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "data": {
      "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "real_name": "系统管理员"
      },
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": "24h"
    }
  }
  ```

#### 获取用户信息
- **GET** `/api/auth/profile`
- **请求头**: `Authorization: Bearer <token>`
- **响应**:
  ```json
  {
    "success": true,
    "message": "获取用户信息成功",
    "data": {
      "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "real_name": "系统管理员"
      }
    }
  }
  ```

#### 登出
- **POST** `/api/auth/logout`
- **请求头**: `Authorization: Bearer <token>`

#### 修改密码
- **POST** `/api/auth/change-password`
- **请求头**: `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "oldPassword": "old_password",
    "newPassword": "new_password"
  }
  ```

### 菜单管理接口

#### 获取菜单列表
- **GET** `/api/menus`
- **查询参数**: `status` (可选，0或1)
- **响应**: 返回树形结构的菜单列表

#### 获取菜单详情
- **GET** `/api/menus/:id`
- **请求头**: `Authorization: Bearer <token>`

#### 创建菜单
- **POST** `/api/menus`
- **请求头**: `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "name": "菜单名称",
    "path": "/menu-path",
    "url": "/menu-url",
    "parent_id": 0,
    "sort_order": 1,
    "status": 1,
    "icon": "fas fa-home",
    "description": "菜单描述"
  }
  ```

#### 更新菜单
- **PUT** `/api/menus/:id`
- **请求头**: `Authorization: Bearer <token>`

#### 删除菜单
- **DELETE** `/api/menus/:id`
- **请求头**: `Authorization: Bearer <token>`

#### 批量更新菜单排序
- **POST** `/api/menus/sort`
- **请求头**: `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "menus": [
      {"id": 1, "sort_order": 1},
      {"id": 2, "sort_order": 2}
    ]
  }
  ```

## 数据库结构

### 管理员表 (admins)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键，自增 |
| username | VARCHAR(50) | 用户名，唯一 |
| password | VARCHAR(255) | 密码(bcrypt加密) |
| email | VARCHAR(100) | 邮箱 |
| real_name | VARCHAR(50) | 真实姓名 |
| status | TINYINT | 状态: 1-启用, 0-禁用 |
| last_login_time | DATETIME | 最后登录时间 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 菜单表 (menus)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键，自增 |
| name | VARCHAR(50) | 菜单名称 |
| path | VARCHAR(100) | 路由路径 |
| url | VARCHAR(200) | 页面URL路径 |
| parent_id | INT | 父级菜单ID, 0表示顶级菜单 |
| sort_order | INT | 排序序号 |
| status | TINYINT | 状态: 1-启用, 0-禁用 |
| description | TEXT | 菜单描述 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 默认账号

- **用户名**: admin
- **密码**: airfit2025

## 安全特性

- JWT Token认证
- 密码bcrypt加密
- 请求限流保护
- CORS跨域配置
- Helmet安全头设置
- SQL注入防护

## 开发说明

### 添加新的API接口

1. 在 `src/routes/` 目录下创建或修改路由文件
2. 在 `src/app.js` 中注册新的路由
3. 如需认证，使用 `authenticateToken` 中间件

### 数据库操作

项目使用原生SQL语句，不使用ORM。数据库操作示例：

```javascript
const { query } = require('../config/database');

// 查询数据
const users = await query('SELECT * FROM admins WHERE status = ?', [1]);

// 插入数据
const result = await query(
  'INSERT INTO menus (name, path) VALUES (?, ?)',
  ['菜单名', '/path']
);
```

## 部署说明

### 生产环境配置

1. 修改 `.env` 文件中的生产环境配置
2. 设置强密码和安全的JWT密钥
3. 配置反向代理（如Nginx）
4. 启用HTTPS
5. 配置日志记录

### Docker部署

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3333
CMD ["npm", "start"]
```

## 许可证

MIT License

## 联系方式

如有问题，请联系开发团队。
