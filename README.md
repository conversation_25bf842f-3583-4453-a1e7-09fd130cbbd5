# 雅克菲采暖官方网站 - Vue 2版本

## 项目概述

这是雅克菲（上海）热能设备有限公司官方网站的Vue 2版本，从原生HTML/CSS/JavaScript技术栈迁移而来。项目采用现代化的前端技术栈，提供更好的开发体验和维护性。

## 技术栈

- **Vue 2.6.14** - 渐进式JavaScript框架
- **Vue Router 3.5.4** - 官方路由管理器
- **Less 4.1.2** - CSS预处理器
- **Webpack 5** - 模块打包工具
- **Babel** - JavaScript编译器

## 主要插件

- **vue-awesome-swiper** - 轮播图组件
- **vue-lazyload** - 图片懒加载
- **vue-count-to** - 数字动画
- **AOS** - 滚动动画库
- **Font Awesome** - 图标库

## 项目结构

```
demo4/
├── public/                 # 静态资源
│   └── index.html         # HTML模板
├── src/                   # 源代码
│   ├── assets/           # 资源文件
│   │   └── styles/       # 样式文件
│   │       ├── variables.less  # Less变量
│   │       ├── base.less      # 基础样式
│   │       └── main.less      # 主样式文件
│   ├── components/       # 组件
│   │   ├── common/       # 通用组件
│   │   └── layout/       # 布局组件
│   ├── views/           # 页面组件
│   ├── router/          # 路由配置
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── package.json         # 项目配置
├── webpack.config.js    # Webpack配置
└── .babelrc            # Babel配置
```

## 页面功能

### 1. 首页 (/)
- Hero Banner展示
- 核心服务介绍
- 智能控制系统展示
- 主要产品预览
- 新闻资讯

### 2. 产品中心 (/products)
- 产品分类导航
- 产品列表展示
- 产品详情查看
- 产品优势介绍

### 3. 采暖知识 (/heating-knowledge)
- 学习模块展示
- 热门课程推荐
- 技能大赛信息
- 专家专栏

### 4. 工程案例 (/cases)
- 精选案例展示
- 服务流程介绍
- 客户评价
- 项目统计数据

### 5. 门店查询 (/stores)
- 门店信息查询
- 地区筛选功能
- 联系方式展示

### 6. 关于我们 (/about)
- 公司简介
- 企业文化
- 技术创新
- 发展历程
- 公司数据统计

## 安装和运行

### 环境要求
- Node.js >= 12.0.0
- npm >= 6.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
项目将在 http://localhost:8080 启动

### 生产构建
```bash
npm run build
```
构建文件将生成在 `dist` 目录

### 代码检查
```bash
npm run lint
```

## 主要特性

### 响应式设计
- 完美适配PC、平板、手机等设备
- 移动端友好的导航菜单
- 灵活的网格布局系统

### 现代化交互
- 平滑的页面滚动动画
- 图片懒加载优化
- 数字滚动动效
- 悬停交互效果

### 组件化架构
- 可复用的Vue组件
- 统一的设计系统
- 模块化的样式管理

### 性能优化
- 代码分割和懒加载
- 图片优化和压缩
- CSS和JavaScript压缩
- 浏览器缓存策略

## 设计系统

### 颜色规范
- 主色调：#D80514 (企业红)
- 辅助色：#333 (深灰)
- 文本色：#666 (中灰)、#999 (浅灰)
- 背景色：#fff (白色)、#f8f9fa (浅灰背景)

### 字体规范
- 主字体：Noto Sans SC
- 备用字体：-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif

### 间距规范
- 基础间距：5px, 10px, 15px, 20px, 30px, 50px
- 容器最大宽度：1200px
- 导航栏高度：70px

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+ (需要polyfill)

## 部署说明

### 静态部署
构建完成后，将 `dist` 目录的内容部署到任何静态文件服务器即可。

### 服务器配置
由于使用了Vue Router的history模式，需要配置服务器支持单页应用：

#### Nginx配置示例
```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

#### Apache配置示例
```apache
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /
  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>
```

## 迁移说明

本项目从原生HTML/CSS/JavaScript成功迁移到Vue 2技术栈，主要改进包括：

1. **组件化架构**：将页面拆分为可复用的Vue组件
2. **状态管理**：使用Vue的响应式数据管理页面状态
3. **路由管理**：使用Vue Router实现单页应用导航
4. **样式优化**：将CSS转换为Less，提供更好的样式管理
5. **开发体验**：提供热重载、代码检查等现代化开发工具

## 维护和更新

- 定期更新依赖包版本
- 关注Vue 2的安全更新
- 优化图片资源和代码性能
- 根据用户反馈改进功能

## 联系方式

- 技术支持：<EMAIL>
- 客服热线：4008-206-306
- 官方网站：http://www.airfit.cn

---

© 2025 雅克菲（上海）热能设备有限公司 版权所有
