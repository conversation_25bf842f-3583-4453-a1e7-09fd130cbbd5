const express = require('express');
const router = express.Router();
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

// 获取联系我们内容
router.get('/', asyncHandler(async (req, res) => {
  const contacts = await query(`
    SELECT 
      id,
      title,
      summary,
      content,
      created_at,
      updated_at
    FROM contact_us
    ORDER BY id DESC
    LIMIT 1
  `);

  const contact = contacts.length > 0 ? contacts[0] : {
    title: '联系我们',
    summary: '雅克菲致力于为客户提供专业的采暖解决方案和优质的服务体验。',
    content: '<p>如需了解更多信息，请联系我们。</p>'
  };

  res.json({
    success: true,
    message: '获取联系我们内容成功',
    data: {
      contact
    }
  });
}));

module.exports = router;
