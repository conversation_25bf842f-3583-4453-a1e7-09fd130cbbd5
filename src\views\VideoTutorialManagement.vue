<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>视频教程管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加教程
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="教程标题">
            <el-input v-model="searchForm.title" placeholder="请输入教程标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="教程类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable style="width: 120px;">
              <el-option v-for="videoType in videoTypes" :key="videoType.key" :label="videoType.name"
                :value="videoType.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="标签">
            <el-input v-model="searchForm.tag" placeholder="请输入标签" clearable style="width: 120px;" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="已发布" :value="1" />
              <el-option label="草稿" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 教程表格 -->
      <el-table v-loading="loading" :data="tutorialList" border class="tutorial-table">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="title" label="教程标题" min-width="200" />

        <el-table-column prop="summary" label="摘要" min-width="200" show-overflow-tooltip />

        <el-table-column label="类型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getVideoTypeTagType(scope.row.type)" size="mini">
              {{ getVideoTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="标签" width="150">
          <template slot-scope="scope">
            <el-tag v-for="tag in scope.row.tags" :key="tag" size="mini" style="margin-right: 5px; margin-bottom: 2px;">
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" />
      </div>
    </div>

    <!-- 添加/编辑教程对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="900px" @close="resetForm">
      <el-form ref="tutorialForm" :model="tutorialForm" :rules="tutorialRules" label-width="100px">
        <el-form-item label="教程标题" prop="title">
          <el-input v-model="tutorialForm.title" placeholder="请输入教程标题" />
        </el-form-item>

        <el-form-item label="教程类型" prop="type">
          <el-radio-group v-model="tutorialForm.type">
            <el-radio v-for="videoType in videoTypes" :key="videoType.key" :label="videoType.key">
              {{ videoType.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="摘要">
          <el-input v-model="tutorialForm.summary" type="textarea" :rows="3" placeholder="请输入教程摘要" />
        </el-form-item>

        <el-form-item label="详细描述">
          <el-input v-model="tutorialForm.description" type="textarea" :rows="4" placeholder="请输入详细描述" />
        </el-form-item>

        <el-form-item label="视频代码">
          <el-input v-model="tutorialForm.video_code" type="textarea" :rows="4" placeholder="请输入视频嵌入代码或视频链接" />
          <div class="form-tip">
            支持视频嵌入代码（iframe）
            <span v-if="tutorialForm.video_code" style="color: #409EFF; cursor: pointer;" @click="previewVideo">
              预览
            </span>
          </div>
        </el-form-item>

        <el-form-item label="标签">
          <el-tag v-for="tag in tutorialForm.tags" :key="tag" closable @close="removeTag(tag)"
            style="margin-right: 10px;">
            {{ tag }}
          </el-tag>
          <el-input v-if="inputVisible" ref="saveTagInput" v-model="inputValue" size="small" style="width: 100px;"
            @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" />
          <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="tutorialForm.status">
                <el-radio :label="1">已发布</el-radio>
                <el-radio :label="0">草稿</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序序号">
              <el-input-number v-model="tutorialForm.sort_order" :min="0" :max="9999" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-dialog v-if="previewVisible" title="视频预览" :visible="true" width="600px" :append-to-body="true"
        :close-on-click-modal="false" :before-close="handleClosePreview">
        <div class="video-preview" v-html="tutorialForm.video_code" />
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { videoTutorials } from '@/api/video-tutorials'
import { systemConfigs } from '@/api/system-configs'

export default {
  name: 'VideoTutorialManagement',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      tutorialList: [],
      videoTypes: [], // 动态视频类型配置
      searchForm: {
        title: '',
        type: '',
        tag: '',
        status: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      tutorialForm: {
        title: '',
        summary: '',
        description: '',
        video_code: '',
        type: '',
        tags: [],
        status: 1,
        sort_order: 0
      },
      tutorialRules: {
        title: [
          { required: true, message: '请输入教程标题', trigger: 'blur' },
          { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择教程类型', trigger: 'change' }
        ]
      },
      inputVisible: false,
      inputValue: '',
      previewVisible: false
    }
  },

  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑教程' : '添加教程'
    }
  },

  mounted() {
    this.loadVideoTypes()
    this.loadTutorialList()
  },

  methods: {
    // 获取视频类型配置
    async loadVideoTypes() {
      try {
        const response = await systemConfigs.getByKey('video_types')
        if (response.success && response.data) {
          this.videoTypes = response.data
          // 设置默认类型为第一个类型
          if (this.videoTypes.length > 0 && !this.tutorialForm.type) {
            this.tutorialForm.type = this.videoTypes[0].key
          }
        }
      } catch (error) {
        console.error('获取视频类型配置失败:', error)
        this.$message.error('获取视频类型配置失败')
      }
    },

    // 根据key获取视频类型中文名称
    getVideoTypeName(typeKey) {
      const videoType = this.videoTypes.find(type => type.key === typeKey)
      return videoType ? videoType.name : typeKey
    },

    // 获取视频类型标签类型
    getVideoTypeTagType(typeKey) {
      // 根据类型索引分配不同的标签类型
      const index = this.videoTypes.findIndex(type => type.key === typeKey)
      const tagTypes = ['primary', 'success', 'warning', 'danger', 'info']
      return index >= 0 ? tagTypes[index % tagTypes.length] : 'primary'
    },

    // 加载教程列表
    async loadTutorialList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          title: this.searchForm.title,
          type: this.searchForm.type,
          tag: this.searchForm.tag,
          status: this.searchForm.status
        }

        const response = await videoTutorials.getList(params)
        if (response.success) {
          this.tutorialList = response.data.tutorials
          this.pagination.total = response.data.pagination.total_items
        }
      } catch (error) {
        console.error('加载教程列表失败:', error)
        this.$message.error('加载教程列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadTutorialList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        type: '',
        tag: '',
        status: ''
      }
      this.pagination.current = 1
      this.loadTutorialList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadTutorialList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadTutorialList()
    },

    // 添加教程
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.dialogVisible = true
    },

    // 编辑教程
    handleEdit(row) {
      this.isEdit = true
      this.editId = row.id
      this.tutorialForm = {
        title: row.title,
        summary: row.summary || '',
        description: row.description || '',
        video_code: row.video_code || '',
        type: row.type,
        tags: row.tags || [],
        status: row.status,
        sort_order: row.sort_order
      }
      this.dialogVisible = true
    },

    // 删除教程
    handleDelete(row) {
      this.$confirm(`确定要删除教程"${row.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await videoTutorials.delete(row.id)
          if (response.success) {
            this.$message.success('删除成功')
            this.loadTutorialList()
          }
        } catch (error) {
          console.error('删除教程失败:', error)
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        const response = await videoTutorials.update(row.id, {
          title: row.title,
          summary: row.summary,
          description: row.description,
          video_code: row.video_code,
          type: row.type,
          tags: row.tags,
          status: row.status,
          sort_order: row.sort_order
        })
        if (response.success) {
          this.$message.success('状态更新成功')
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 显示标签输入框
    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    // 确认添加标签
    handleInputConfirm() {
      const inputValue = this.inputValue
      if (inputValue && !this.tutorialForm.tags.includes(inputValue)) {
        this.tutorialForm.tags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    // 移除标签
    removeTag(tag) {
      const index = this.tutorialForm.tags.indexOf(tag)
      if (index > -1) {
        this.tutorialForm.tags.splice(index, 1)
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.tutorialForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const formData = { ...this.tutorialForm }

            let response
            if (this.isEdit) {
              response = await videoTutorials.update(this.editId, formData)
            } else {
              response = await videoTutorials.create(formData)
            }

            if (response.success) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功')
              this.dialogVisible = false
              this.loadTutorialList()
            }
          } catch (error) {
            console.error('提交失败:', error)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      this.tutorialForm = {
        title: '',
        summary: '',
        description: '',
        video_code: '',
        type: this.videoTypes.length > 0 ? this.videoTypes[0].key : '',
        tags: [],
        status: 1,
        sort_order: 0
      }
      this.inputVisible = false
      this.inputValue = ''
      if (this.$refs.tutorialForm) {
        this.$refs.tutorialForm.resetFields()
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 预览视频
    previewVideo() {
      this.previewVisible = true
    },

    // 关闭预览
    handleClosePreview() {
      this.previewVisible = false
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.tutorial-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 表格样式优化 */
.tutorial-table .el-table__header {
  background-color: #f5f7fa;
}

.tutorial-table .el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

/* 标签样式 */
.el-tag {
  margin-right: 5px;
  margin-bottom: 2px;
}
</style>
