const express = require('express');
const { query, transaction } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取经销商列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    company_name = '',
    province = '',
    city_name = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (company_name) {
    whereConditions.push('company_name LIKE ?');
    queryParams.push(`%${company_name}%`);
  }

  if (province) {
    whereConditions.push('province = ?');
    queryParams.push(province);
  }

  if (city_name) {
    whereConditions.push('city_name = ?');
    queryParams.push(city_name);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(status);
  }

  const whereClause = whereConditions.length > 0
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  // 获取总数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM dealers
    ${whereClause}
  `;

  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取经销商列表
  const dealersQuery = `
    SELECT *
    FROM dealers
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const dealers = await query(dealersQuery, queryParams);

  res.json({
    success: true,
    message: '获取经销商列表成功',
    data: {
      dealers,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取单个经销商详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const dealers = await query('SELECT * FROM dealers WHERE id = ?', [id]);

  if (dealers.length === 0) {
    throw new CustomError('经销商不存在', 404);
  }

  res.json({
    success: true,
    message: '获取经销商详情成功',
    data: {
      dealer: dealers[0]
    }
  });
}));

// 创建经销商
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    province,
    city_name,
    company_name,
    company_location,
    phone,
    status = 1,
    sort_order = 0
  } = req.body;

  // 参数验证
  if (!province || !city_name || !company_name) {
    throw new CustomError('省份、城市名称和公司名称不能为空', 400);
  }

  const result = await query(`
    INSERT INTO dealers (province, city_name, company_name, company_location, phone, status, sort_order)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `, [province, city_name, company_name, company_location || null, phone || null, status, sort_order]);

  res.json({
    success: true,
    message: '创建经销商成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新经销商
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    province,
    city_name,
    company_name,
    company_location,
    phone,
    status,
    sort_order
  } = req.body;

  // 检查经销商是否存在
  const existingDealers = await query('SELECT * FROM dealers WHERE id = ?', [id]);
  if (existingDealers.length === 0) {
    throw new CustomError('经销商不存在', 404);
  }

  // 构建更新字段和参数
  const updateFields = [];
  const updateParams = [];

  if (province !== undefined) {
    if (!province.trim()) {
      throw new CustomError('所属省份不能为空', 400);
    }
    updateFields.push('province = ?');
    updateParams.push(province);
  }

  if (city_name !== undefined) {
    if (!city_name.trim()) {
      throw new CustomError('城市名称不能为空', 400);
    }
    updateFields.push('city_name = ?');
    updateParams.push(city_name);
  }

  if (company_name !== undefined) {
    if (!company_name.trim()) {
      throw new CustomError('公司名称不能为空', 400);
    }
    updateFields.push('company_name = ?');
    updateParams.push(company_name);
  }

  if (company_location !== undefined) {
    updateFields.push('company_location = ?');
    updateParams.push(company_location);
  }

  if (phone !== undefined) {
    updateFields.push('phone = ?');
    updateParams.push(phone);
  }

  if (status !== undefined) {
    updateFields.push('status = ?');
    updateParams.push(status);
  }

  if (sort_order !== undefined) {
    updateFields.push('sort_order = ?');
    updateParams.push(sort_order);
  }

  // 如果没有要更新的字段，返回错误
  if (updateFields.length === 0) {
    throw new CustomError('没有要更新的字段', 400);
  }

  // 添加更新时间
  updateFields.push('updated_at = NOW()');
  updateParams.push(id);

  await query(`
    UPDATE dealers
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateParams);

  res.json({
    success: true,
    message: '更新经销商成功'
  });
}));

// 删除经销商
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查经销商是否存在
  const existingDealers = await query('SELECT * FROM dealers WHERE id = ?', [id]);
  if (existingDealers.length === 0) {
    throw new CustomError('经销商不存在', 404);
  }

  await query('DELETE FROM dealers WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除经销商成功'
  });
}));

// 批量更新经销商状态
router.post('/batch-status', authenticateToken, asyncHandler(async (req, res) => {
  const { ids, status } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new CustomError('经销商ID列表不能为空', 400);
  }

  if (![0, 1].includes(status)) {
    throw new CustomError('状态值无效', 400);
  }

  const placeholders = ids.map(() => '?').join(',');
  await query(`UPDATE dealers SET status = ? WHERE id IN (${placeholders})`, [status, ...ids]);

  res.json({
    success: true,
    message: '批量更新状态成功'
  });
}));

module.exports = router;
