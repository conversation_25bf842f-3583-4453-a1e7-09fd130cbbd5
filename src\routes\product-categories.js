const express = require('express');
const { query, transaction } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 构建分类树形结构的辅助函数
function buildCategoryTree(categories) {
  const categoryMap = new Map();
  const rootCategories = [];

  // 创建分类映射并计算层级
  categories.forEach(category => {
    categoryMap.set(category.id, { 
      ...category, 
      children: [],
      level: 1 // 默认层级，后面会重新计算
    });
  });

  // 构建树形结构并计算正确的层级
  const calculateLevel = (categoryId, level = 1) => {
    const category = categoryMap.get(categoryId);
    if (category) {
      category.level = level;
      categories
        .filter(cat => cat.parent_id === categoryId)
        .forEach(child => {
          category.children.push(categoryMap.get(child.id));
          calculateLevel(child.id, level + 1);
        });
    }
  };

  // 找出根分类并构建树
  categories.forEach(category => {
    if (category.parent_id === 0) {
      const rootCategory = categoryMap.get(category.id);
      rootCategories.push(rootCategory);
      calculateLevel(category.id, 1);
    }
  });

  return rootCategories;
}

// 获取分类列表（树形结构）
router.get('/', asyncHandler(async (req, res) => {
  const sql = 'SELECT * FROM product_categories ORDER BY parent_id ASC, sort_order ASC';

  const categories = await query(sql);

  // 构建树形结构
  const categoryTree = buildCategoryTree(categories);

  res.json({
    success: true,
    message: '获取分类列表成功',
    data: {
      categories: categoryTree,
      total: categories.length
    }
  });
}));

// 获取单个分类详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const categories = await query('SELECT * FROM product_categories WHERE id = ?', [id]);
  
  if (categories.length === 0) {
    throw new CustomError('分类不存在', 404);
  }
  
  res.json({
    success: true,
    message: '获取分类详情成功',
    data: {
      category: categories[0]
    }
  });
}));

// 创建分类
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    name,
    parent_id = 0,
    sort_order = 0,
    description
  } = req.body;

  // 参数验证
  if (!name) {
    throw new CustomError('分类名称不能为空', 400);
  }

  // 如果有父级分类，检查父级分类是否存在和层级限制
  let level = 1;
  if (parent_id > 0) {
    const parentCategories = await query('SELECT id, level FROM product_categories WHERE id = ?', [parent_id]);
    if (parentCategories.length === 0) {
      throw new CustomError('父级分类不存在', 400);
    }

    level = (parentCategories[0].level || 1) + 1;

    // 检查是否超过4级分类限制
    if (level > 4) {
      throw new CustomError('最多只支持4级分类', 400);
    }
  }

  const result = await query(
    `INSERT INTO product_categories (name, parent_id, level, sort_order, description)
     VALUES (?, ?, ?, ?, ?)`,
    [name, parent_id, level, sort_order, description || null]
  );

  res.json({
    success: true,
    message: '创建分类成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新分类
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name,
    sort_order,
    description
  } = req.body;
  
  // 检查分类是否存在
  const existingCategories = await query('SELECT * FROM product_categories WHERE id = ?', [id]);
  if (existingCategories.length === 0) {
    throw new CustomError('分类不存在', 404);
  }
  
  // 构建更新字段和参数（只允许更新名称、排序和描述）
  const updateFields = [];
  const updateParams = [];

  if (name !== undefined) {
    if (!name.trim()) {
      throw new CustomError('分类名称不能为空', 400);
    }
    updateFields.push('name = ?');
    updateParams.push(name);
  }

  if (sort_order !== undefined) {
    updateFields.push('sort_order = ?');
    updateParams.push(sort_order);
  }

  if (description !== undefined) {
    updateFields.push('description = ?');
    updateParams.push(description);
  }

  // 如果没有要更新的字段，返回错误
  if (updateFields.length === 0) {
    throw new CustomError('没有要更新的字段', 400);
  }
  
  // 注意：编辑分类时不允许修改层级关系（parent_id），只能修改名称、排序和描述
  
  // 添加更新时间
  updateFields.push('updated_at = NOW()');
  updateParams.push(id);

  await query(`
    UPDATE product_categories
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateParams);
  
  // 注意：编辑时不允许修改层级关系，因此不需要更新子分类层级
  
  res.json({
    success: true,
    message: '更新分类成功'
  });
}));

// 删除分类
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查分类是否存在
  const existingCategories = await query('SELECT * FROM product_categories WHERE id = ?', [id]);
  if (existingCategories.length === 0) {
    throw new CustomError('分类不存在', 404);
  }
  
  // 检查是否有子分类
  const childCategories = await query('SELECT id FROM product_categories WHERE parent_id = ?', [id]);
  if (childCategories.length > 0) {
    throw new CustomError('该分类有子分类，请先删除子分类', 400);
  }
  
  // 检查是否有关联的产品
  const products = await query('SELECT id FROM products WHERE category_id = ?', [id]);
  if (products.length > 0) {
    throw new CustomError('该分类下有产品，请先移动或删除相关产品', 400);
  }
  
  await query('DELETE FROM product_categories WHERE id = ?', [id]);
  
  res.json({
    success: true,
    message: '删除分类成功'
  });
}));

// 注意：由于编辑时不允许修改层级关系，已移除循环引用检查函数

// 注意：由于编辑时不允许修改层级关系，已移除更新子分类层级的函数

module.exports = router;
