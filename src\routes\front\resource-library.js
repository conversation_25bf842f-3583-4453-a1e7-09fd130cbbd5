const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取资源库列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 12,
    keyword,
    category
  } = req.query;

  const offset = (page - 1) * limit;
  let whereConditions = ['status = 1']; // 只返回已发布的资源
  let params = [];

  // 关键词搜索
  if (keyword) {
    whereConditions.push('(title LIKE ? OR summary LIKE ?)');
    params.push(`%${keyword}%`, `%${keyword}%`);
  }

  // 分类筛选
  if (category) {
    whereConditions.push('category = ?');
    params.push(category);
  }

  const whereClause = whereConditions.join(' AND ');

  // 查询资源列表
  let sql = `
    SELECT
      id,
      title,
      summary,
      category,
      files,
      download_count,
      created_at
    FROM resource_library
    WHERE ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const resources = await query(sql, params);

  // 处理JSON字段
  const processedResources = resources.map(resource => {
    let files = [];
    try {
      if (resource.files) {
        files = typeof resource.files === 'string' ? JSON.parse(resource.files) : resource.files;
      }
    } catch (error) {
      console.error('JSON解析错误:', error, 'files字段值:', resource.files);
      files = [];
    }

    return {
      ...resource,
      files
    };
  });

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM resource_library
    WHERE ${whereClause}
  `, params);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取资源库列表成功',
    data: {
      resources: processedResources,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取资源详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 查询资源详情
  const resources = await query(`
    SELECT *
    FROM resource_library
    WHERE id = ? AND status = 1
  `, [id]);

  if (resources.length === 0) {
    return res.status(404).json({
      success: false,
      message: '资源不存在或已下架'
    });
  }

  const resource = resources[0];

  // 处理JSON字段
  try {
    resource.files = resource.files ? (typeof resource.files === 'string' ? JSON.parse(resource.files) : resource.files) : [];
  } catch (error) {
    console.error('JSON解析错误:', error, 'files字段值:', resource.files);
    resource.files = [];
  }

  res.json({
    success: true,
    message: '获取资源详情成功',
    data: {
      resource
    }
  });
}));

// 记录资源下载
router.post('/:id/download', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查资源是否存在
  const resources = await query(`
    SELECT id, title
    FROM resource_library
    WHERE id = ? AND status = 1
  `, [id]);

  if (resources.length === 0) {
    return res.status(404).json({
      success: false,
      message: '资源不存在或已下架'
    });
  }

  // 增加下载次数
  await query('UPDATE resource_library SET download_count = download_count + 1 WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '下载记录成功',
    data: {
      resource_id: id,
      resource_title: resources[0].title
    }
  });
}));

// 获取热门资源
router.get('/hot/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;

  const resources = await query(`
    SELECT
      id,
      title,
      summary,
      created_at
    FROM resource_library
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取热门资源成功',
    data: {
      resources
    }
  });
}));

// 获取最新资源
router.get('/latest/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;

  const resources = await query(`
    SELECT
      id,
      title,
      summary,
      created_at
    FROM resource_library
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取最新资源成功',
    data: {
      resources
    }
  });
}));

// 按标签获取资源
router.get('/tags/:tag', asyncHandler(async (req, res) => {
  const { tag } = req.params;
  const { page = 1, limit = 12 } = req.query;
  const offset = (page - 1) * limit;

  const resources = await query(`
    SELECT 
      id,
      title,
      description,
      tags,
      download_count,
      created_at
    FROM resource_library
    WHERE status = 1 AND tags LIKE ?
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ? OFFSET ?
  `, [`%${tag}%`, parseInt(limit), parseInt(offset)]);

  // 处理tags字段
  const processedResources = resources.map(resource => {
    let tags = [];
    try {
      if (resource.tags) {
        tags = typeof resource.tags === 'string' ? JSON.parse(resource.tags) : resource.tags;
      }
    } catch (error) {
      console.error('JSON解析错误:', error, 'tags字段值:', resource.tags);
      tags = [];
    }

    return {
      ...resource,
      tags
    };
  });

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM resource_library
    WHERE status = 1 AND tags LIKE ?
  `, [`%${tag}%`]);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: `获取标签"${tag}"的资源成功`,
    data: {
      resources: processedResources,
      tag,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

module.exports = router;
