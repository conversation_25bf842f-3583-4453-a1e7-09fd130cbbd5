const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取供暖知识列表
router.get('/', asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 12,
    keyword 
  } = req.query;
  
  const offset = (page - 1) * limit;
  let whereConditions = ['status = 1']; // 只返回已发布的知识
  let params = [];
  
  // 关键词搜索
  if (keyword) {
    whereConditions.push('(title LIKE ? OR summary LIKE ? OR description LIKE ?)');
    params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
  }
  
  const whereClause = whereConditions.join(' AND ');
  
  // 查询知识列表
  const knowledge = await query(`
    SELECT
      id,
      title,
      summary,
      thumbnail,
      description,
      tags,
      created_at
    FROM heating_knowledge
    WHERE ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `, params);
  
  // 处理JSON字段
  const processedKnowledge = knowledge.map(item => ({
    ...item,
    tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : []
  }));
  
  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM heating_knowledge
    WHERE ${whereClause}
  `, params);
  
  const total = totalResult[0].total;
  
  res.json({
    success: true,
    message: '获取供暖知识列表成功',
    data: {
      knowledge: processedKnowledge,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取供暖知识详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 查询知识详情
  const knowledge = await query(`
    SELECT *
    FROM heating_knowledge
    WHERE id = ? AND status = 1
  `, [id]);
  
  if (knowledge.length === 0) {
    return res.status(404).json({
      success: false,
      message: '供暖知识不存在或已下架'
    });
  }
  
  const knowledgeItem = knowledge[0];
  
  // 增加阅读次数
  await query('UPDATE heating_knowledge SET view_count = view_count + 1 WHERE id = ?', [id]);
  knowledgeItem.view_count += 1;
  
  // 处理JSON字段
  knowledgeItem.tags = knowledgeItem.tags ? (typeof knowledgeItem.tags === 'string' ? JSON.parse(knowledgeItem.tags) : knowledgeItem.tags) : [];
  
  // 获取相关知识（相似标签的其他知识）
  let relatedKnowledge = [];
  if (knowledgeItem.tags && knowledgeItem.tags.length > 0) {
    const tagConditions = knowledgeItem.tags.map(() => 'tags LIKE ?').join(' OR ');
    const tagParams = knowledgeItem.tags.map(tag => `%${tag}%`);
    
    relatedKnowledge = await query(`
      SELECT id, title, view_count, created_at
      FROM heating_knowledge 
      WHERE (${tagConditions}) AND id != ? AND status = 1
      ORDER BY view_count DESC, created_at DESC
      LIMIT 4
    `, [...tagParams, id]);
  }
  
  res.json({
    success: true,
    message: '获取供暖知识详情成功',
    data: {
      knowledge: knowledgeItem,
      related_knowledge: relatedKnowledge
    }
  });
}));

// 获取热门供暖知识
router.get('/hot/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;
  
  const knowledge = await query(`
    SELECT
      id,
      title,
      summary,
      created_at
    FROM heating_knowledge
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);
  
  res.json({
    success: true,
    message: '获取热门供暖知识成功',
    data: {
      knowledge
    }
  });
}));

// 获取最新供暖知识
router.get('/latest/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;
  
  const knowledge = await query(`
    SELECT 
      id,
      title,
      created_at
    FROM heating_knowledge
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);
  
  res.json({
    success: true,
    message: '获取最新供暖知识成功',
    data: {
      knowledge
    }
  });
}));

// 按标签获取供暖知识
router.get('/tags/:tag', asyncHandler(async (req, res) => {
  const { tag } = req.params;
  const { page = 1, limit = 12 } = req.query;
  const offset = (page - 1) * limit;
  
  const knowledge = await query(`
    SELECT 
      id,
      title,
      content,
      tags,
      view_count,
      created_at
    FROM heating_knowledge
    WHERE status = 1 AND tags LIKE ?
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ? OFFSET ?
  `, [`%${tag}%`, parseInt(limit), parseInt(offset)]);
  
  // 处理字段
  const processedKnowledge = knowledge.map(item => ({
    ...item,
    tags: item.tags ? (typeof item.tags === 'string' ? JSON.parse(item.tags) : item.tags) : [],
    summary: item.content ? item.content.substring(0, 200) + (item.content.length > 200 ? '...' : '') : ''
  }));
  
  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM heating_knowledge
    WHERE status = 1 AND tags LIKE ?
  `, [`%${tag}%`]);
  
  const total = totalResult[0].total;
  
  res.json({
    success: true,
    message: `获取标签"${tag}"的供暖知识成功`,
    data: {
      knowledge: processedKnowledge,
      tag,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取推荐供暖知识（首页展示）
router.get('/featured/list', asyncHandler(async (req, res) => {
  const { limit = 4 } = req.query;
  
  const knowledge = await query(`
    SELECT 
      id,
      title,
      view_count,
      created_at
    FROM heating_knowledge
    WHERE status = 1
    ORDER BY view_count DESC, sort_order ASC, created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);
  
  res.json({
    success: true,
    message: '获取推荐供暖知识成功',
    data: {
      knowledge
    }
  });
}));

module.exports = router;
