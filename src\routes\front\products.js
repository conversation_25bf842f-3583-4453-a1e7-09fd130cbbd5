const express = require('express');
const { query } = require('../../config/database');
const { CustomError, asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取分类及其所有子分类的ID
async function getCategoryAndChildrenIds(categoryId) {
  const allCategories = await query(`
    SELECT id, parent_id
    FROM product_categories
  `);

  const categoryIds = [];

  // 递归收集子分类ID
  function collectChildrenIds(parentId) {
    categoryIds.push(parentId);
    const children = allCategories.filter(cat => cat.parent_id === parentId);
    children.forEach(child => {
      collectChildrenIds(child.id);
    });
  }

  collectChildrenIds(parseInt(categoryId));
  return categoryIds;
}

// 获取产品分类列表（树形结构）
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = await query(`
    SELECT id, name, parent_id, sort_order, description
    FROM product_categories
    ORDER BY parent_id ASC, sort_order ASC
  `);

  // 构建树形结构
  const buildCategoryTree = (categories, parentId = 0) => {
    return categories
      .filter(category => category.parent_id === parentId)
      .map(category => ({
        ...category,
        children: buildCategoryTree(categories, category.id)
      }));
  };

  const categoryTree = buildCategoryTree(categories);

  res.json({
    success: true,
    message: '获取产品分类成功',
    data: {
      categories: categoryTree,
      total: categories.length
    }
  });
}));

// 获取产品列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    category_id,
    page = 1,
    limit = 12,
    keyword
  } = req.query;

  const limitNum = parseInt(limit) || 12;
  const offsetNum = (parseInt(page) - 1) * limitNum;

  // 先简化查询，不使用动态条件
  let products, totalResult;

  if (category_id) {
    // 有分类筛选 - 获取该分类及其所有子分类的产品
    const categoryIds = await getCategoryAndChildrenIds(category_id);
    const placeholders = categoryIds.map(() => '?').join(',');

    products = await query(`
      SELECT
        p.id,
        p.name,
        p.main_image,
        p.summary,
        p.view_count,
        p.created_at,
        c.name as category_name
      FROM products p
      LEFT JOIN product_categories c ON p.category_id = c.id
      WHERE p.status = 1 AND p.category_id IN (${placeholders})
      ORDER BY p.sort_order ASC, p.created_at DESC
      LIMIT ${limitNum} OFFSET ${offsetNum}
    `, categoryIds);

    totalResult = await query(`
      SELECT COUNT(*) as total
      FROM products p
      WHERE p.status = 1 AND p.category_id IN (${placeholders})
    `, categoryIds);
  } else {
    // 无筛选条件
    products = await query(`
      SELECT
        p.id,
        p.name,
        p.main_image,
        p.summary,
        p.view_count,
        p.created_at,
        c.name as category_name
      FROM products p
      LEFT JOIN product_categories c ON p.category_id = c.id
      WHERE p.status = 1
      ORDER BY p.sort_order ASC, p.created_at DESC
      LIMIT ${limitNum} OFFSET ${offsetNum}
    `);

    totalResult = await query(`
      SELECT COUNT(*) as total
      FROM products p
      WHERE p.status = 1
    `);
  }

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取产品列表成功',
    data: {
      products,
      pagination: {
        page: parseInt(page),
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    }
  });
}));

// 获取产品详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 查询产品详情
  const products = await query(`
    SELECT 
      p.*,
      c.name as category_name
    FROM products p
    LEFT JOIN product_categories c ON p.category_id = c.id
    WHERE p.id = ? AND p.status = 1
  `, [id]);

  if (products.length === 0) {
    throw new CustomError('产品不存在或已下架', 404);
  }

  const product = products[0];

  // 增加浏览量
  await query('UPDATE products SET view_count = view_count + 1 WHERE id = ?', [id]);
  product.view_count += 1;

  // 获取相关产品（同分类的其他产品）
  const relatedProducts = await query(`
    SELECT id, name, main_image, summary
    FROM products 
    WHERE category_id = ? AND id != ? AND status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 4
  `, [product.category_id, id]);

  res.json({
    success: true,
    message: '获取产品详情成功',
    data: {
      product,
      related_products: relatedProducts
    }
  });
}));

// 获取热门产品（按浏览量排序）
router.get('/hot/list', asyncHandler(async (req, res) => {
  const { limit = 8 } = req.query;

  const products = await query(`
    SELECT 
      p.id,
      p.name,
      p.main_image,
      p.summary,
      p.view_count,
      c.name as category_name
    FROM products p
    LEFT JOIN product_categories c ON p.category_id = c.id
    WHERE p.status = 1
    ORDER BY p.view_count DESC, p.created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取热门产品成功',
    data: {
      products
    }
  });
}));

// 获取最新产品
router.get('/latest/list', asyncHandler(async (req, res) => {
  const { limit = 8 } = req.query;

  const products = await query(`
    SELECT 
      p.id,
      p.name,
      p.main_image,
      p.summary,
      p.view_count,
      c.name as category_name
    FROM products p
    LEFT JOIN product_categories c ON p.category_id = c.id
    WHERE p.status = 1
    ORDER BY p.created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取最新产品成功',
    data: {
      products
    }
  });
}));

module.exports = router;
