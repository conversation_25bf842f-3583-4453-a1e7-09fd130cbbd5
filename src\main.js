import Vue from 'vue';
import Vuex from 'vuex';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import App from './App.vue';
import router from './router';
import store from './store';
import './assets/styles/main.less';

Vue.use(Vuex);

// 全局配置
Vue.config.productionTip = false;

// 使用Element UI
Vue.use(ElementUI, {
  size: 'medium' // 设置组件默认尺寸
});


// 设置全局默认值
if (ElementUI.Dialog) {
  ElementUI.Dialog.props.closeOnClickModal.default = false
}
// 全局过滤器
Vue.filter('formatDate', function (value) {
  if (!value) return '';
  const date = new Date(value);
  return date.toLocaleString('zh-CN');
});

// 全局混入
Vue.mixin({
  methods: {
    // 全局消息提示方法
    $success(message) {
      this.$message({
        type: 'success',
        message: message,
        duration: 3000
      });
    },
    $error(message) {
      this.$message({
        type: 'error',
        message: message,
        duration: 5000
      });
    },
    $warning(message) {
      this.$message({
        type: 'warning',
        message: message,
        duration: 4000
      });
    }
  }
});

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app');
