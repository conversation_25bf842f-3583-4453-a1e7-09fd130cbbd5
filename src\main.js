import Vue from 'vue'
import App from './App.vue'
import router from './router'

// 引入全局样式
import './assets/styles/main.less'

// 引入插件
import AOS from 'aos'
import 'aos/dist/aos.css'






Vue.config.productionTip = false

// 初始化AOS动画库
document.addEventListener('DOMContentLoaded', function () {
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
  })
})

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
