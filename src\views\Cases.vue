<template>
  <div class="cases">
    <!-- 页面头部 -->
    <PageHeader tag="节能案例" />

    <!-- 案例展示 -->
    <section class="cases-showcase">
      <div class="container">
        <SectionHeader pageKey="cases" blockKey="case_showcase" />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchCases">重试</button>
          </div>
        </div>

        <!-- 案例列表 -->
        <div v-else>
          <!-- 案例类型筛选 -->
          <div class="case-filters">
            <div class="filter-tabs">
              <button :class="['filter-tab', { active: activeFilter === 'all' }]" @click="setActiveFilter('all')">
                全部案例
              </button>
              <button v-for="caseType in caseTypes" :key="caseType.key"
                :class="['filter-tab', { active: activeFilter === caseType.key }]"
                @click="setActiveFilter(caseType.key)">
                {{ caseType.name }}
              </button>
            </div>
          </div>

          <!-- 无案例状态 -->
          <div v-if="cases.length === 0" class="no-cases">
            <div class="no-cases-content">
              <i class="fas fa-folder-open"></i>
              <h3>暂无相关案例</h3>
              <p>该分类下暂时没有案例，请选择其他分类查看</p>
              <button class="btn btn-primary" @click="setActiveFilter('all')">
                查看全部案例
              </button>
            </div>
          </div>

          <!-- 案例列表 -->
          <div v-else class="cases-grid">
            <div v-for="(caseItem, index) in cases" :key="caseItem.id" @click="viewCase(caseItem)" class="case-card"
              data-aos="fade-up" :data-aos-delay="index * 100">
              <div class="case-image">
                <div v-if="caseItem.case_type" class="case-category" :style="getCaseTypeStyle(caseItem.case_type)">{{
                  getCaseTypeName(caseItem.case_type) }}</div>
                <img v-if="caseItem.main_image" :src="caseItem.main_image" :alt="caseItem.title">
                <div v-else class="no-image">
                  <i class="fas fa-image"></i>
                  <p>暂无图片</p>
                </div>
                <div class="case-overlay">
                  <div class="overlay-content">
                    <button class="view-details-btn">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
              <div class="case-info">

                <h3>{{ caseItem.title }}</h3>
                <p v-if="caseItem.summary">{{ caseItem.summary }}</p>
                <div class="case-details">
                  <div v-if="caseItem.case_area" class="detail-item">
                    <i class="fas fa-home"></i>
                    <span>{{ caseItem.case_area }}</span>
                  </div>
                  <div v-if="caseItem.product_category" class="detail-item">
                    <i class="fas fa-thermometer-half"></i>
                    <span>{{ caseItem.product_category }}</span>
                  </div>
                </div>
                <div class="case-meta">
                  <div v-if="caseItem.case_tags" class="case-tags">
                    <span v-for="tag in caseItem.case_tags.split(',')" :key="tag.trim()" class="tag">
                      {{ tag.trim() }}
                    </span>
                  </div>
                  <div class="case-date">
                    <i class="fas fa-calendar"></i>
                    <span>{{ formatDate(caseItem.created_at) }}</span>
                  </div>
                </div>

              </div>
            </div>
          </div>

          <!-- 分页组件 -->
          <Pagination v-if="pagination.total > 0" :current-page="pagination.page" :total-pages="pagination.pages"
            @page-change="handlePageChange" />
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import Pagination from '@/components/common/Pagination.vue'
import api, { API } from '@/api'
import { formatDateTime } from '@/utils/dateFormat'
import { isMobile } from '@/utils/deviceDetect'

export default {
  name: 'Cases',
  components: {
    PageHeader,
    SectionHeader,
    Pagination
  },
  data() {
    return {
      loading: true,
      error: null,
      cases: [],
      caseTypes: [], // 动态案例类型
      activeFilter: 'all',
      pagination: {
        page: 1,
        limit: 9,
        total: 0,
        pages: 0
      },

    }
  },
  created() {
    this.pagination.limit = isMobile() ? 9 : 9;
    this.fetchCaseTypes();
    this.fetchCases();
    console.log(this.pagination.limit);
  },
  methods: {
    // 获取案例类型配置
    async fetchCaseTypes() {
      try {
        const response = await API.getSystemConfig('case_types')
        if (response.success) {
          this.caseTypes = response.data || []
        }
      } catch (error) {
        console.error('获取案例类型配置失败:', error)
      }
    },

    async fetchCases() {
      try {
        this.loading = true
        this.error = null

        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        // 添加筛选参数
        if (this.activeFilter !== 'all') {
          params.case_type = this.activeFilter
        }

        const response = await api.get('/api/front/energy-cases', { params })

        if (response.success) {
          this.cases = response.data.cases
          this.pagination = response.data.pagination
        } else {
          throw new Error(response.message || '获取数据失败')
        }

      } catch (error) {
        console.error('获取案例列表失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.fetchCases()
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    viewCase(caseItem) {
      // 新开页签
      window.open(`/cases/${caseItem.id}`)

    },

    formatDate(dateString) {
      return formatDateTime(dateString, 'YYYY-MM-DD')
    },

    // 获取案例类型中文名称
    getCaseTypeName(caseTypeKey) {
      const caseType = this.caseTypes.find(type => type.key === caseTypeKey);
      return caseType ? caseType.name : caseTypeKey;
    },

    // 根据案例类型key从颜色池获取样式
    getCaseTypeStyle(caseTypeKey) {
      // 案例类型颜色池（10个精心挑选的颜色）
      const colorPool = [
        '#1890ff', // 蓝色 - 适合别墅
        '#52c41a', // 绿色 - 适合商业
        '#fa541c', // 橙色 - 适合公寓
        '#722ed1', // 紫色 - 适合公建
        '#999999', // 灰色 - 适合其他
        '#13c2c2', // 青色
        '#faad14', // 金色
        '#f5222d', // 红色
        '#2f54eb', // 深蓝
        '#a0d911'  // 浅绿
      ];

      // 根据key生成稳定的索引

      const index = this.caseTypes.findIndex(type => type.key === caseTypeKey);

      return {
        background: colorPool[index],
        color: 'white',
      };
    },

    setActiveFilter(filter) {
      this.activeFilter = filter
      this.pagination.page = 1 // 重置到第一页
      this.fetchCases()
    },
  }
}
</script>


<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.cases-showcase {
  padding: 66px 0;
  background-color: #f8f9fa;

  .case-filters {
    margin-bottom: 40px;

    .filter-tabs {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;

      .filter-tab {
        padding: 12px 30px;
        border: 2px solid #e5e5e5;
        background: white;
        color: @text-color;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        font-weight: 500;

        &.active {
          border-color: @primary-color;
          color: @primary-color;
        }

        &:hover {
          border-color: @primary-color;
          color: @primary-color;
        }
      }
    }
  }

  .no-cases {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .no-cases-content {
      text-align: center;

      i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
      }

      h3 {
        color: #666;
        margin-bottom: 10px;
        font-size: 24px;
      }

      p {
        color: #999;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .btn {
        background: @primary-color;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        cursor: pointer;
        transition: @transition;
        font-size: 14px;

        &:hover {
          background: @primary-hover;
          transform: translateY(-2px);
        }
      }
    }
  }

  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .loading-spinner,
    .error-content {
      text-align: center;

      i {
        font-size: 48px;
        color: @text-lighter;
        margin-bottom: 16px;
      }

      h3 {
        margin: 16px 0 8px;
        color: @text-color;
      }

      p {
        color: @text-light;
        margin-bottom: 16px;
      }

      .btn {
        background: @primary-color;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: @border-radius-small;
        cursor: pointer;
        transition: @transition;

        &:hover {
          background: @primary-hover;
        }
      }
    }
  }

  .cases-grid {
    display: grid;
    grid-template-columns: 1fr; // 移动端优先，默认单列
    gap: 20px;
    margin-bottom: 40px;

    // 平板及以上设备显示多列
    @media (min-width: 769px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }

    // 桌面端显示三列
    @media (min-width: 1025px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 30px;
    }
  }
}

.case-card {
  background: white;
  border-radius: @border-radius-large;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;


  .case-image {
    position: relative;
    height: 220px;
    overflow: hidden;

    .case-category {
      position: absolute;
      top: 15px;
      left: 15px;
      padding: 5px 15px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      z-index: 99;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .no-image {
      width: 100%;
      height: 100%;
      background: @bg-light;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: @text-lighter;

      i {
        font-size: 48px;
        margin-bottom: 12px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }

    }

    .case-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5); // 半透明遮罩
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .overlay-content {
        text-align: center;
        color: white;
        padding: 20px;

        h3 {
          color: white;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          line-height: 1.3;
        }

        .case-details {
          margin-bottom: 20px;

          .detail-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            color: white;

            i {
              color: white;
              font-size: 14px;
            }
          }
        }


      }
    }
  }

  .case-info {
    padding: 25px;



    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #333;
    }

    p {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      margin-bottom: 10px;
    }

    .case-details {
      display: flex;
      margin-bottom: 15px;
      gap: 20px;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 12px;
        color: #999;

        i {
          color: @primary-color;
        }
      }
    }

    .case-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag {
        padding: 4px 8px;
        background: #f0f0f0;
        border-radius: 12px;
        font-size: 11px;
        color: #666;
      }
    }

    .case-meta {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 12px;
    }

    .case-date {
      display: flex;
      align-items: center;
      justify-content: right;
      gap: 6px;
      color: @text-lighter;
      font-size: 14px;
    }
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);


    .case-overlay {
      opacity: 1;

    }

    .case-image img {
      transform: scale(1.05);
    }
  }
}

.service-process {
  padding: 80px 0;
  background: #f8f9fa;

  .process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
  }
}

.process-step {
  text-align: center;
  position: relative;

  .step-number {
    position: absolute;
    top: -10px;
    right: 20px;
    width: 30px;
    height: 30px;
    background: #D80514;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
  }

  .step-icon {
    width: 80px;
    height: 80px;
    background: white;
    border: 3px solid #D80514;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;

    i {
      font-size: 28px;
      color: #D80514;
    }
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    color: #666;
    line-height: 1.6;
  }
}

.customer-reviews {
  padding: 80px 0;

  .reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }
}

.review-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  }

  .review-content {
    margin-bottom: 20px;

    .quote-icon {
      color: #D80514;
      font-size: 24px;
      margin-bottom: 15px;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 15px;
      font-style: italic;
    }

    .rating {
      .fa-star {
        color: #ddd;
        margin-right: 3px;

        &.active {
          color: #ffc107;
        }
      }
    }
  }

  .review-author {
    display: flex;
    align-items: center;
    gap: 15px;

    .author-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .author-info {
      h4 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
      }

      p {
        color: #999;
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

.project-stats {
  padding: 80px 0;
  background: #D80514;
  color: white;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
  }
}

.stat-item {
  text-align: center;

  .stat-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.8;
  }

  .stat-content {
    .stat-number {
      font-size: 36px;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .stat-label {
      font-size: 16px;
      opacity: 0.9;
    }
  }
}

@media (max-width: 768px) {

  .cases-showcase,
  .service-process,
  .customer-reviews,
  .project-stats {
    padding: 40px 0;
  }

  .process-steps,
  .reviews-grid,
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }


  .case-details {
    flex-direction: row;
    gap: 10px;
  }

  .cases-showcase .case-filters .filter-tabs {
    justify-content: flex-start;

    .filter-tab {
      padding: 10px 20px;
    }
  }
}
</style>
