const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取公司简介
router.get('/profile', asyncHandler(async (req, res) => {
  const profiles = await query(`
    SELECT *
    FROM company_profile
    ORDER BY updated_at DESC
    LIMIT 1
  `);

  if (profiles.length === 0) {
    return res.status(404).json({
      success: false,
      message: '公司简介信息不存在'
    });
  }

  res.json({
    success: true,
    message: '获取公司简介成功',
    data: {
      profile: profiles[0]
    }
  });
}));

// 获取公司荣誉列表
router.get('/honors', asyncHandler(async (req, res) => {
  const { page = 1, limit = 12 } = req.query;
  const offset = (page - 1) * limit;

  const honors = await query(`
    SELECT
      id,
      title,
      summary,
      DATE_FORMAT(award_date, '%Y-%m-%d') as award_date,
      image_url,
      description,
      sort_order,
      created_at
    FROM company_honors
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 12 OFFSET 0
  `);

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM company_honors
    WHERE status = 1
  `);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取公司荣誉列表成功',
    data: {
      honors,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取发展历程
router.get('/history', asyncHandler(async (req, res) => {
  const history = await query(`
    SELECT
      id,
      year,
      title,
      subtitle,
      summary,
      image_url,
      sort_order
    FROM development_history
    WHERE status = 1
    ORDER BY year DESC, sort_order ASC
  `);

  res.json({
    success: true,
    message: '获取发展历程成功',
    data: {
      history
    }
  });
}));

// 获取联系信息
router.get('/contact', asyncHandler(async (req, res) => {
  const contacts = await query(`
    SELECT *
    FROM contact_information
    WHERE status = 1
    ORDER BY updated_at DESC
    LIMIT 1
  `);

  if (contacts.length === 0) {
    return res.status(404).json({
      success: false,
      message: '联系信息不存在'
    });
  }

  res.json({
    success: true,
    message: '获取联系信息成功',
    data: {
      contact: contacts[0]
    }
  });
}));

// 获取二维码列表
router.get('/qr-codes', asyncHandler(async (req, res) => {
  const qrCodes = await query(`
    SELECT 
      id,
      image_url,
      description,
      sort_order
    FROM qr_codes
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
  `);

  res.json({
    success: true,
    message: '获取二维码列表成功',
    data: {
      qr_codes: qrCodes
    }
  });
}));

// 获取关于我们完整信息（一次性获取所有信息）
router.get('/complete', asyncHandler(async (req, res) => {
  // 获取公司简介
  const profiles = await query(`
    SELECT *
    FROM company_profile
    ORDER BY updated_at DESC
    LIMIT 1
  `);

  // 获取公司荣誉（限制数量）
  const honors = await query(`
    SELECT 
      id,
      image_url,
      description,
      sort_order
    FROM company_honors
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 8
  `);

  // 获取发展历程
  const history = await query(`
    SELECT
      id,
      year,
      title,
      subtitle,
      summary,
      sort_order
    FROM development_history
    WHERE status = 1
    ORDER BY year DESC, sort_order ASC
  `);

  // 获取联系信息
  const contacts = await query(`
    SELECT *
    FROM contact_information
    WHERE status = 1
    ORDER BY updated_at DESC
    LIMIT 1
  `);

  // 获取二维码
  const qrCodes = await query(`
    SELECT 
      id,
      image_url,
      description,
      sort_order
    FROM qr_codes
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
  `);

  res.json({
    success: true,
    message: '获取关于我们完整信息成功',
    data: {
      profile: profiles.length > 0 ? profiles[0] : null,
      honors,
      history,
      contact: contacts.length > 0 ? contacts[0] : null,
      qr_codes: qrCodes
    }
  });
}));

// 获取公司统计信息
router.get('/stats', asyncHandler(async (req, res) => {
  // 这里可以添加一些统计信息，比如成立年限、服务客户数等
  // 由于没有专门的统计表，我们可以从现有数据中计算一些基本统计

  // 获取发展历程中的最早年份作为成立年份
  const foundingYearResult = await query(`
    SELECT MIN(year) as founding_year
    FROM development_history
    WHERE status = 1 AND year IS NOT NULL
  `);

  // 获取荣誉数量
  const honorsCountResult = await query(`
    SELECT COUNT(*) as honors_count
    FROM company_honors
    WHERE status = 1
  `);

  // 获取产品数量
  const productsCountResult = await query(`
    SELECT COUNT(*) as products_count
    FROM products
    WHERE status = 1
  `);

  // 获取案例数量
  const casesCountResult = await query(`
    SELECT COUNT(*) as cases_count
    FROM energy_cases
    WHERE status = 1
  `);

  const currentYear = new Date().getFullYear();
  const foundingYear = foundingYearResult[0].founding_year;
  const yearsInBusiness = foundingYear ? currentYear - foundingYear : 0;

  res.json({
    success: true,
    message: '获取公司统计信息成功',
    data: {
      founding_year: foundingYear,
      years_in_business: yearsInBusiness,
      honors_count: honorsCountResult[0].honors_count,
      products_count: productsCountResult[0].products_count,
      cases_count: casesCountResult[0].cases_count
    }
  });
}));

module.exports = router;
