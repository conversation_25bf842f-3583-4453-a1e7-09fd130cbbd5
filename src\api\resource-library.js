import { request } from './http'

export const resourceLibrary = {
  // 获取资料库列表
  getList(params = {}) {
    return request.get('/api/resource-library', params)
  },

  // 获取资料库详情
  getDetail(id) {
    return request.get(`/api/resource-library/${id}`)
  },

  // 创建资料库
  create(data) {
    return request.post('/api/resource-library', data)
  },

  // 更新资料库
  update(id, data) {
    return request.put(`/api/resource-library/${id}`, data)
  },

  // 删除资料库
  delete(id) {
    return request.delete(`/api/resource-library/${id}`)
  }
}

export default resourceLibrary
