import { request } from './http'

// 经销商API服务
export const dealers = {
  // 获取经销商列表
  getList(params = {}) {
    return request.get('/api/dealers', params)
  },

  // 获取经销商详情
  getDetail(id) {
    return request.get(`/api/dealers/${id}`)
  },

  // 创建经销商
  create(data) {
    return request.post('/api/dealers', data)
  },

  // 更新经销商
  update(id, data) {
    return request.put(`/api/dealers/${id}`, data)
  },

  // 删除经销商
  delete(id) {
    return request.delete(`/api/dealers/${id}`)
  },

  // 批量更新状态
  batchUpdateStatus(ids, status) {
    return request.post('/api/dealers/batch-status', { ids, status })
  }
}

export default dealers
