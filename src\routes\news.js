const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError } = require('../middleware/errorHandler');

// 获取新闻列表（支持分页、搜索、筛选）
router.get('/', async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    category = '',
    status = '',
    start_date = '',
    end_date = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (category) {
    whereConditions.push('category = ?');
    queryParams.push(category);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(status);
  }

  if (start_date) {
    whereConditions.push('DATE(created_at) >= ?');
    queryParams.push(start_date);
  }

  if (end_date) {
    whereConditions.push('DATE(created_at) <= ?');
    queryParams.push(end_date);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  try {
    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total FROM news ${whereClause}
    `, queryParams);
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (page - 1) * limit;
    const newsResult = await query(`
      SELECT id, title, summary, thumbnail, category, status, sort_order, view_count, created_at, updated_at
      FROM news ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
    `, queryParams);

    res.json({
      code: 200,
      message: '获取新闻列表成功',
      data: {
        list: newsResult,
        pagination: {
          current: parseInt(page),
          size: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取新闻列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取新闻列表失败',
      error: error.message
    });
  }
});

// 获取新闻详情
router.get('/:id', async (req, res) => {
  const { id } = req.params;

  try {
    const result = await query('SELECT * FROM news WHERE id = ?', [id]);
    
    if (result.length === 0) {
      throw new CustomError('新闻不存在', 404);
    }

    res.json({
      code: 200,
      message: '获取新闻详情成功',
      data: result[0]
    });
  } catch (error) {
    console.error('获取新闻详情失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '获取新闻详情失败',
        error: error.message
      });
    }
  }
});

// 创建新闻（需要认证）
router.post('/', authenticateToken, async (req, res) => {
  const {
    title,
    summary,
    thumbnail,
    category,
    content,
    status = 0,
    sort_order = 0
  } = req.body;

  // 参数验证
  if (!title) {
    throw new CustomError('新闻标题不能为空', 400);
  }

  try {
    const result = await query(`
      INSERT INTO news (title, summary, thumbnail, category, content, status, sort_order)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [title, summary || null, thumbnail || null, category || null, content || null, status, sort_order]);

    res.json({
      code: 200,
      message: '创建新闻成功',
      data: {
        id: result.insertId
      }
    });
  } catch (error) {
    console.error('创建新闻失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建新闻失败',
      error: error.message
    });
  }
});

// 更新新闻（需要认证）
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    title,
    summary,
    thumbnail,
    category,
    content,
    status,
    sort_order
  } = req.body;

  try {
    // 检查新闻是否存在
    const existingNews = await query('SELECT id FROM news WHERE id = ?', [id]);
    if (existingNews.length === 0) {
      throw new CustomError('新闻不存在', 404);
    }

    // 构建更新字段
    let updateFields = [];
    let updateParams = [];

    if (title !== undefined) {
      updateFields.push('title = ?');
      updateParams.push(title);
    }

    if (summary !== undefined) {
      updateFields.push('summary = ?');
      updateParams.push(summary);
    }

    if (thumbnail !== undefined) {
      updateFields.push('thumbnail = ?');
      updateParams.push(thumbnail);
    }

    if (category !== undefined) {
      updateFields.push('category = ?');
      updateParams.push(category);
    }

    if (content !== undefined) {
      updateFields.push('content = ?');
      updateParams.push(content);
    }

    if (status !== undefined) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }

    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateParams.push(sort_order);
    }

    if (updateFields.length === 0) {
      throw new CustomError('没有要更新的字段', 400);
    }

    updateParams.push(id);

    await query(`
      UPDATE news SET ${updateFields.join(', ')}
      WHERE id = ?
    `, updateParams);

    res.json({
      code: 200,
      message: '更新新闻成功'
    });
  } catch (error) {
    console.error('更新新闻失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '更新新闻失败',
        error: error.message
      });
    }
  }
});

// 删除新闻（需要认证）
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const result = await query('DELETE FROM news WHERE id = ?', [id]);
    
    if (result.affectedRows === 0) {
      throw new CustomError('新闻不存在', 404);
    }

    res.json({
      code: 200,
      message: '删除新闻成功'
    });
  } catch (error) {
    console.error('删除新闻失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '删除新闻失败',
        error: error.message
      });
    }
  }
});

// 切换新闻状态（需要认证）
router.patch('/:id/status', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  if (status === undefined || ![0, 1].includes(status)) {
    throw new CustomError('状态值无效', 400);
  }

  try {
    const result = await query('UPDATE news SET status = ? WHERE id = ?', [status, id]);
    
    if (result.affectedRows === 0) {
      throw new CustomError('新闻不存在', 404);
    }

    res.json({
      code: 200,
      message: '状态更新成功'
    });
  } catch (error) {
    console.error('更新新闻状态失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '更新新闻状态失败',
        error: error.message
      });
    }
  }
});

module.exports = router;
