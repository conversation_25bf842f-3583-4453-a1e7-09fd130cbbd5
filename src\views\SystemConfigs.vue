<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>分类配置管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加配置
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="配置类型">
            <el-select v-model="searchForm.configType" placeholder="请选择配置类型" clearable style="width: 150px;">
              <el-option label="分类配置" value="category"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置键名">
            <el-input
              v-model="searchForm.configKey"
              placeholder="请输入配置键名"
              clearable
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 配置列表 -->
      <div class="config-list">
        <div 
          v-for="config in configsList" 
          :key="config.id"
          class="config-card"
        >
          <div class="config-header">
            <div class="config-info">
              <h3>{{ getConfigIcon(config.config_key) }} {{ config.config_name }}</h3>
              <p class="config-desc">{{ config.config_desc }}</p>
              <div class="config-meta">
                <span>配置项: {{ config.item_count }}项</span>
                <span>更新时间: {{ formatDate(config.updated_at) }}</span>
                <el-tag :type="config.status === 1 ? 'success' : 'danger'" size="mini">
                  {{ config.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </div>
            <div class="config-actions">
              <el-button size="small" @click="handleEdit(config)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDelete(config)">删除</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50]"
          :page-size="pagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>

    <!-- 添加/编辑弹窗 -->
    <el-dialog
      :title="isEdit ? '编辑配置' : '添加配置'"
      :visible.sync="dialogVisible"
      width="800px"
      @close="resetForm"
    >
      <el-form
        :model="configForm"
        :rules="formRules"
        ref="configForm"
        label-width="100px"
      >
        <el-form-item label="配置键名" prop="config_key">
          <el-input 
            v-model="configForm.config_key" 
            placeholder="请输入配置键名，如：case_types"
            :disabled="isEdit"
          ></el-input>
        </el-form-item>
        <el-form-item label="配置名称" prop="config_name">
          <el-input v-model="configForm.config_name" placeholder="请输入配置名称"></el-input>
        </el-form-item>
        <el-form-item label="配置描述" prop="config_desc">
          <el-input
            type="textarea"
            v-model="configForm.config_desc"
            placeholder="请输入配置描述"
            :rows="2"
          ></el-input>
        </el-form-item>
        <el-form-item label="配置类型" prop="config_type">
          <el-select v-model="configForm.config_type" placeholder="请选择配置类型">
            <el-option label="分类配置" value="category"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="configForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 配置项目管理 -->
        <el-form-item label="配置项目">
          <div class="config-items-manager">
            <div class="items-header">
              <span>分类项目管理</span>
              <el-button size="mini" type="primary" @click="addConfigItem">添加项目</el-button>
            </div>
            
            <el-table :data="configForm.config_value" style="width: 100%">
              <el-table-column prop="key" label="标识Key" width="120">
                <template slot-scope="scope">
                  <el-input 
                    v-model="scope.row.key" 
                    size="mini" 
                    placeholder="请输入key"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="显示名称" width="120">
                <template slot-scope="scope">
                  <el-input 
                    v-model="scope.row.name" 
                    size="mini" 
                    placeholder="请输入名称"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="sort" label="排序" width="80">
                <template slot-scope="scope">
                  <el-input-number 
                    v-model="scope.row.sort" 
                    size="mini" 
                    :min="1" 
                    :max="999"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <el-switch 
                    v-model="scope.row.status" 
                    :active-value="1" 
                    :inactive-value="0"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click="moveItemUp(scope.$index)"
                    :disabled="scope.$index === 0"
                  >↑</el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click="moveItemDown(scope.$index)"
                    :disabled="scope.$index === configForm.config_value.length - 1"
                  >↓</el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    style="color: #f56c6c;" 
                    @click="removeConfigItem(scope.$index)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { systemConfigs } from '@/api/system-configs'
import { formatDate } from '@/utils/dateFormat'

export default {
  name: 'SystemConfigs',
  data() {
    return {
      loading: false,
      submitting: false,
      configsList: [],
      searchForm: {
        configType: '',
        configKey: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      dialogVisible: false,
      isEdit: false,
      editId: null,
      configForm: {
        config_key: '',
        config_name: '',
        config_desc: '',
        config_type: 'category',
        status: 1,
        config_value: []
      },
      formRules: {
        config_key: [
          { required: true, message: '请输入配置键名', trigger: 'blur' }
        ],
        config_name: [
          { required: true, message: '请输入配置名称', trigger: 'blur' }
        ],
        config_type: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchConfigsList()
  },
  methods: {
    formatDate,
    
    // 获取配置图标
    getConfigIcon(configKey) {
      const iconMap = {
        'case_types': '📋',
        'resource_categories': '📚',
        'video_types': '🎥',
        'news_categories': '📰'
      }
      return iconMap[configKey] || '⚙️'
    },
    
    // 获取配置列表
    async fetchConfigsList() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          configType: this.searchForm.configType,
          configKey: this.searchForm.configKey
        }
        
        const response = await systemConfigs.getList(params)
        this.configsList = response.data.configs || []
        this.pagination = response.data.pagination || {}
      } catch (error) {
        console.error('获取配置列表失败:', error)
        this.$message.error('获取配置列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.fetchConfigsList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        configType: '',
        configKey: ''
      }
      this.pagination.page = 1
      this.fetchConfigsList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.fetchConfigsList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchConfigsList()
    },

    // 添加
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.configForm = {
        config_key: '',
        config_name: '',
        config_desc: '',
        config_type: 'category',
        status: 1,
        config_value: []
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.editId = row.id
      this.configForm = {
        config_key: row.config_key,
        config_name: row.config_name,
        config_desc: row.config_desc || '',
        config_type: row.config_type,
        status: row.status,
        config_value: [...row.config_value] // 深拷贝数组
      }
      this.dialogVisible = true
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个配置吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await systemConfigs.delete(row.id)
        this.$message.success('删除成功')
        this.fetchConfigsList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除配置失败:', error)
          this.$message.error('删除配置失败')
        }
      }
    },

    // 添加配置项
    addConfigItem() {
      const newSort = this.configForm.config_value.length + 1
      this.configForm.config_value.push({
        key: '',
        name: '',
        sort: newSort,
        status: 1
      })
    },

    // 删除配置项
    removeConfigItem(index) {
      this.configForm.config_value.splice(index, 1)
      // 重新排序
      this.configForm.config_value.forEach((item, idx) => {
        item.sort = idx + 1
      })
    },

    // 上移配置项
    moveItemUp(index) {
      if (index > 0) {
        const temp = this.configForm.config_value[index]
        this.$set(this.configForm.config_value, index, this.configForm.config_value[index - 1])
        this.$set(this.configForm.config_value, index - 1, temp)
        // 更新排序
        this.configForm.config_value.forEach((item, idx) => {
          item.sort = idx + 1
        })
      }
    },

    // 下移配置项
    moveItemDown(index) {
      if (index < this.configForm.config_value.length - 1) {
        const temp = this.configForm.config_value[index]
        this.$set(this.configForm.config_value, index, this.configForm.config_value[index + 1])
        this.$set(this.configForm.config_value, index + 1, temp)
        // 更新排序
        this.configForm.config_value.forEach((item, idx) => {
          item.sort = idx + 1
        })
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.configForm.validate()
        
        // 验证配置项
        if (this.configForm.config_value.length === 0) {
          this.$message.error('请至少添加一个配置项')
          return
        }
        
        // 验证配置项的key和name不能为空
        for (let item of this.configForm.config_value) {
          if (!item.key || !item.name) {
            this.$message.error('配置项的Key和名称不能为空')
            return
          }
        }
        
        this.submitting = true
        
        if (this.isEdit) {
          await systemConfigs.update(this.editId, this.configForm)
          this.$message.success('更新配置成功')
        } else {
          await systemConfigs.create(this.configForm)
          this.$message.success('创建配置成功')
        }
        
        this.dialogVisible = false
        this.fetchConfigsList()
      } catch (error) {
        if (error.fields) {
          // 表单验证错误
          return
        }
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败')
      } finally {
        this.submitting = false
      }
    },

    // 重置表单
    resetForm() {
      this.configForm = {
        config_key: '',
        config_name: '',
        config_desc: '',
        config_type: 'category',
        status: 1,
        config_value: []
      }
      if (this.$refs.configForm) {
        this.$refs.configForm.resetFields()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.config-list {
  margin-bottom: 20px;
}

.config-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.config-info {
  flex: 1;
  
  h3 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 16px;
  }
  
  .config-desc {
    margin: 0 0 12px 0;
    color: #606266;
    font-size: 14px;
  }
  
  .config-meta {
    display: flex;
    gap: 16px;
    align-items: center;
    font-size: 12px;
    color: #909399;
  }
}

.config-actions {
  display: flex;
  gap: 8px;
}

.config-items-manager {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  
  .items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    span {
      font-weight: 500;
      color: #303133;
    }
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
