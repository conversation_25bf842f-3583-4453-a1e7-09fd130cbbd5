const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取联系信息列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    name = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (name) {
    whereConditions.push('name LIKE ?');
    queryParams.push(`%${name}%`);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM contact_information ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取联系信息列表
  const contactsQuery = `
    SELECT id, name, image_url, address, fax, service_phone, status, sort_order, created_at, updated_at
    FROM contact_information
    ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const contacts = await query(contactsQuery, queryParams);

  res.json({
    success: true,
    message: '获取联系信息列表成功',
    data: {
      contacts,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取联系信息详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query('SELECT * FROM contact_information WHERE id = ?', [id]);

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '联系信息记录不存在'
    });
  }

  res.json({
    success: true,
    message: '获取联系信息详情成功',
    data: rows[0]
  });
}));

// 创建联系信息
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { name, image_url, address, fax, service_phone, sort_order = 0, status = 1 } = req.body;

  // 验证必填字段
  if (!name) {
    return res.status(400).json({
      success: false,
      message: '名称不能为空'
    });
  }

  const result = await query(
    'INSERT INTO contact_information (name, image_url, address, fax, service_phone, sort_order, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [name, image_url || '', address || '', fax || '', service_phone || '', parseInt(sort_order), parseInt(status)]
  );

  const newRows = await query('SELECT * FROM contact_information WHERE id = ?', [result.insertId]);

  res.status(201).json({
    success: true,
    message: '创建联系信息成功',
    data: newRows[0]
  });
}));

// 更新联系信息
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, image_url, address, fax, service_phone, sort_order, status } = req.body;

  // 验证必填字段
  if (!name) {
    return res.status(400).json({
      success: false,
      message: '名称不能为空'
    });
  }

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM contact_information WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '联系信息记录不存在'
    });
  }

  await query(
    'UPDATE contact_information SET name = ?, image_url = ?, address = ?, fax = ?, service_phone = ?, sort_order = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [name, image_url || '', address || '', fax || '', service_phone || '', parseInt(sort_order), parseInt(status), id]
  );

  const updatedRows = await query('SELECT * FROM contact_information WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '更新联系信息成功',
    data: updatedRows[0]
  });
}));

// 删除联系信息
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM contact_information WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '联系信息记录不存在'
    });
  }

  await query('DELETE FROM contact_information WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除联系信息成功'
  });
}));

module.exports = router;
