<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>节能案例管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加案例
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="案例标题">
            <el-input v-model="searchForm.title" placeholder="请输入案例标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="案例类型">
            <el-select v-model="searchForm.case_type" placeholder="请选择案例类型" clearable style="width: 150px;">
              <el-option v-for="caseType in caseTypes" :key="caseType.key" :label="caseType.name"
                :value="caseType.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="已发布" :value="1" />
              <el-option label="草稿" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="首页展示">
            <el-select v-model="searchForm.is_homepage_featured" placeholder="请选择" clearable style="width: 120px;">
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 案例表格 -->
      <el-table v-loading="loading" :data="caseList" border class="case-table">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="主图" width="100">
          <template slot-scope="scope">
            <el-image v-if="scope.row.main_image" :src="scope.row.main_image" :preview-src-list="[scope.row.main_image]"
              class="case-image" />
            <span v-else class="no-image">暂无图片</span>
          </template>
        </el-table-column>

        <el-table-column prop="title" label="案例标题" min-width="200" />

        <el-table-column prop="summary" label="案例摘要" min-width="200" show-overflow-tooltip />

        <el-table-column label="案例类型" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ getCaseTypeName(scope.row.case_type) }}
          </template>
        </el-table-column>

        <el-table-column prop="product_category" label="产品分类" width="120" show-overflow-tooltip />

        <el-table-column prop="case_area" label="案例面积" width="120" show-overflow-tooltip />

        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column prop="view_count" label="浏览量" width="80" align="center" />

        <el-table-column prop="is_homepage_featured" label="首页展示" width="100" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.is_homepage_featured" :active-value="1" :inactive-value="0"
              @change="handleHomepageFeaturedChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          :total="pagination.total" layout="total, sizes, prev, pager, next, jumper" />
      </div>
    </div>

    <!-- 添加/编辑案例对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1000px" @close="resetForm">
      <el-form ref="caseForm" :model="caseForm" :rules="caseRules" label-width="120px">
        <el-form-item label="案例标题" prop="title">
          <el-input v-model="caseForm.title" placeholder="请输入案例标题" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="案例类型">
              <el-select v-model="caseForm.case_type" placeholder="请选择案例类型" style="width: 100%">
                <el-option v-for="caseType in caseTypes" :key="caseType.key" :label="caseType.name"
                  :value="caseType.key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例面积">
              <el-input v-model="caseForm.case_area" placeholder="请输入案例面积，如：350平方米" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品分类">
              <el-input v-model="caseForm.product_category" placeholder="请输入产品分类，如：壁挂炉、温控系统等" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例标签">
              <el-input v-model="caseForm.case_tags" placeholder="请输入案例标签，多个标签用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="主图" prop="main_image">
          <ImageUploader v-model="caseForm.main_image" size="240x180" tip="建议尺寸：800x600像素，支持jpg、png格式" :max-size="5" />
        </el-form-item>

        <el-form-item label="案例摘要" prop="summary">
          <el-input v-model="caseForm.summary" type="textarea" :rows="4" placeholder="请输入案例摘要，简要介绍案例的核心内容" />
        </el-form-item>

        <el-form-item label="详细描述" prop="description">
          <RichTextEditor v-model="caseForm.description" height="300px" placeholder="请输入案例的详细描述，包括项目背景、实施方案、节能效果等..."
            @change="handleDescriptionChange" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="caseForm.sort_order" :min="0" :max="999" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="caseForm.status">
                <el-radio :label="1">已发布</el-radio>
                <el-radio :label="0">草稿</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="首页展示" prop="is_homepage_featured">
              <el-radio-group v-model="caseForm.is_homepage_featured">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { energyCases } from '@/api/energyCases'
import { systemConfigs } from '@/api/system-configs'
import RichTextEditor from '@/components/RichTextEditor.vue'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  name: 'EnergyCaseManagement',
  components: {
    RichTextEditor,
    ImageUploader
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      caseList: [],
      caseTypes: [], // 动态案例类型配置
      searchForm: {
        title: '',
        case_type: '',
        status: '',
        is_homepage_featured: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      caseForm: {
        title: '',
        main_image: '',
        summary: '',
        description: '',
        case_type: '',
        case_area: '',
        product_category: '',
        case_tags: '',
        status: 0,
        sort_order: 0,
        is_homepage_featured: 0
      },
      caseRules: {
        title: [
          { required: true, message: '请输入案例标题', trigger: 'blur' },
          { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑案例' : '添加案例'
    }
  },
  created() {
    this.loadCaseTypes()
    this.loadCaseList()
  },
  methods: {
    // 获取案例类型配置
    async loadCaseTypes() {
      try {
        const response = await systemConfigs.getByKey('case_types')
        if (response.success && response.data) {
          this.caseTypes = response.data
        }
      } catch (error) {
        console.error('获取案例类型配置失败:', error)
        this.$message.error('获取案例类型配置失败')
      }
    },

    // 根据key获取案例类型中文名称
    getCaseTypeName(caseTypeKey) {
      const caseType = this.caseTypes.find(type => type.key === caseTypeKey)
      return caseType ? caseType.name : caseTypeKey
    },

    // 加载案例列表
    async loadCaseList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          title: this.searchForm.title,
          case_type: this.searchForm.case_type,
          status: this.searchForm.status,
          is_homepage_featured: this.searchForm.is_homepage_featured
        }

        const response = await energyCases.getList(params)
        if (response.success) {
          this.caseList = response.data.cases
          this.pagination.total = response.data.pagination.total_items
        }
      } catch (error) {
        console.error('加载案例列表失败:', error)
        this.$message.error('加载案例列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadCaseList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        case_type: '',
        status: '',
        is_homepage_featured: ''
      }
      this.pagination.current = 1
      this.loadCaseList()
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadCaseList()
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.current = page
      this.loadCaseList()
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        await energyCases.update(row.id, { status: row.status })
        this.$message.success('状态更新成功')
      } catch (error) {
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 首页展示切换
    async handleHomepageFeaturedChange(row) {
      try {
        await energyCases.update(row.id, { is_homepage_featured: row.is_homepage_featured })
        this.$message.success('首页展示设置更新成功')
      } catch (error) {
        console.error('首页展示设置更新失败:', error)
        this.$message.error('首页展示设置更新失败')
        // 恢复原状态
        row.is_homepage_featured = row.is_homepage_featured === 1 ? 0 : 1
      }
    },

    // 添加案例
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.resetForm()
      this.dialogVisible = true
    },

    // 编辑案例
    async handleEdit(row) {
      this.isEdit = true
      this.editId = row.id

      try {
        const response = await energyCases.getDetail(row.id)
        if (response.success) {
          const caseData = response.data.case
          this.caseForm = {
            title: caseData.title || '',
            main_image: caseData.main_image || '',
            summary: caseData.summary || '',
            description: caseData.description || '',
            case_type: caseData.case_type || '',
            case_area: caseData.case_area || '',
            product_category: caseData.product_category || '',
            case_tags: caseData.case_tags || '',
            status: caseData.status || 0,
            sort_order: caseData.sort_order || 0,
            is_homepage_featured: caseData.is_homepage_featured || 0
          }
          this.dialogVisible = true
        }
      } catch (error) {
        console.error('获取案例详情失败:', error)
        this.$message.error('获取案例详情失败')
      }
    },

    // 删除案例
    handleDelete(row) {
      this.$confirm('确定要删除这个案例吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await energyCases.delete(row.id)
          this.$message.success('删除成功')
          this.loadCaseList()
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      })
    },

    // 富文本内容变化
    handleDescriptionChange(content) {
      this.caseForm.description = content
    },

    // 提交表单
    handleSubmit() {
      this.$refs.caseForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        this.submitLoading = true
        try {
          const formData = { ...this.caseForm }

          let response
          if (this.isEdit) {
            response = await energyCases.update(this.editId, formData)
          } else {
            response = await energyCases.create(formData)
          }

          if (response.success) {
            this.$message.success(this.isEdit ? '案例更新成功' : '案例创建成功')
            this.dialogVisible = false
            this.loadCaseList()
          }
        } catch (error) {
          console.error('提交失败:', error)
          this.$message.error(this.isEdit ? '案例更新失败' : '案例创建失败')
        } finally {
          this.submitLoading = false
        }
      })
    },

    // 重置表单
    resetForm() {
      this.caseForm = {
        title: '',
        main_image: '',
        summary: '',
        description: '',
        case_type: '',
        case_area: '',
        product_category: '',
        case_tags: '',
        status: 0,
        sort_order: 0,
        is_homepage_featured: 0
      }
      if (this.$refs.caseForm) {
        this.$refs.caseForm.resetFields()
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.case-table {
  margin-bottom: 20px;
}

.case-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

.no-image {
  color: #999;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409EFF;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.form-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 100px;
}
</style>
