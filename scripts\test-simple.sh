#!/bin/bash

# 简单的前台API测试脚本
BASE_URL="http://localhost:3333/api/front"

echo "🚀 开始测试前台API接口..."
echo "基础URL: $BASE_URL"
echo "=================================="

# 测试函数
test_api() {
    local name="$1"
    local endpoint="$2"
    
    echo -n "🧪 测试 $name: "
    
    response=$(curl -s -w "%{http_code}" "$BASE_URL$endpoint")
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        # 检查响应是否包含success字段
        if echo "$body" | grep -q '"success":true'; then
            echo "✅ 通过"
        else
            echo "❌ 失败 - 响应格式错误"
        fi
    else
        echo "❌ 失败 - HTTP状态码: $http_code"
    fi
}

# 基础API测试
test_api "健康检查" "/health"
test_api "产品分类" "/products/categories"
test_api "产品列表" "/products"
test_api "轮播图列表" "/banners"
test_api "新闻列表" "/news"
test_api "经销商列表" "/dealers"

# 关于我们API测试
test_api "公司简介" "/about-us/profile"
test_api "公司荣誉" "/about-us/honors"
test_api "发展历程" "/about-us/history"
test_api "联系信息" "/about-us/contact"

echo "=================================="
echo "🎉 测试完成!"
