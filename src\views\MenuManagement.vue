<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>菜单管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加菜单
        </el-button>
      </div>
      
      <!-- 菜单表格 -->
      <el-table
        v-loading="loading"
        :data="menuList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border
        class="menu-table"
      >
        <el-table-column prop="name" label="菜单名称" min-width="150" />
        
        <el-table-column prop="path" label="路由路径" min-width="150" />
        
        <el-table-column prop="url" label="页面URL" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="sort_order" label="排序" width="80" align="center" />
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ scope.row.created_at | formatDate }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              v-if="scope.row.parent_id === 0"
              type="text"
              size="small"
              @click="handleAddChild(scope.row)"
            >
              添加子菜单
            </el-button>
            <el-button type="text" size="small" style="color: #f56c6c;" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 添加/编辑菜单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="menuForm"
        :model="menuForm"
        :rules="menuRules"
        label-width="100px"
      >
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="menuForm.name" placeholder="请输入菜单名称" />
        </el-form-item>
        
        <el-form-item label="路由路径" prop="path">
          <el-input v-model="menuForm.path" placeholder="请输入路由路径，如：/products" />
        </el-form-item>
        
        <el-form-item label="页面URL" prop="url">
          <el-input v-model="menuForm.url" placeholder="请输入页面URL，如：/products" />
        </el-form-item>
        
        <el-form-item label="父级菜单" prop="parent_id">
          <el-select v-model="menuForm.parent_id" placeholder="请选择父级菜单" style="width: 100%;">
            <el-option label="顶级菜单" :value="0" />
            <el-option
              v-for="menu in parentMenuOptions"
              :key="menu.id"
              :label="menu.name"
              :value="menu.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="menuForm.sort_order" :min="0" :max="999" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="menuForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="menuForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入菜单描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import api from '@/api';

export default {
  name: 'MenuManagement',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      menuForm: {
        name: '',
        path: '',
        url: '',
        parent_id: 0,
        sort_order: 0,
        status: 1,
        description: ''
      },
      menuRules: {
        name: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' },
          { min: 1, max: 50, message: '菜单名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '请输入路由路径', trigger: 'blur' },
          { min: 1, max: 100, message: '路由路径长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(['menuList']),
    
    dialogTitle() {
      return this.isEdit ? '编辑菜单' : '添加菜单';
    },
    
    // 父级菜单选项（只显示顶级菜单）
    parentMenuOptions() {
      return this.menuList.filter(menu => menu.parent_id === 0);
    }
  },
  created() {
    this.loadMenuList();
  },
  methods: {
    ...mapActions(['getMenuList']),
    
    async loadMenuList() {
      this.loading = true;
      try {
        await this.getMenuList();
      } catch (error) {
        this.$error('获取菜单列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    handleAdd() {
      this.isEdit = false;
      this.editId = null;
      this.dialogVisible = true;
    },
    
    handleAddChild(parentMenu) {
      this.isEdit = false;
      this.editId = null;
      this.menuForm.parent_id = parentMenu.id;
      this.dialogVisible = true;
    },
    
    handleEdit(menu) {
      this.isEdit = true;
      this.editId = menu.id;
      this.menuForm = {
        name: menu.name,
        path: menu.path,
        url: menu.url || '',
        parent_id: menu.parent_id,
        sort_order: menu.sort_order,
        status: menu.status,
        description: menu.description || ''
      };
      this.dialogVisible = true;
    },
    
    async handleDelete(menu) {
      try {
        await this.$confirm(`确定要删除菜单"${menu.name}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        await api.menu.delete(menu.id);
        this.$success('删除成功');
        await this.loadMenuList();
      } catch (error) {
        if (error !== 'cancel') {
          this.$error(error.message || '删除失败');
        }
      }
    },
    
    async handleSubmit() {
      try {
        await this.$refs.menuForm.validate();
        
        this.submitLoading = true;
        
        if (this.isEdit) {
          await api.menu.update(this.editId, this.menuForm);
          this.$success('更新成功');
        } else {
          await api.menu.create(this.menuForm);
          this.$success('创建成功');
        }
        
        this.dialogVisible = false;
        await this.loadMenuList();
      } catch (error) {
        this.$error(error.message || '操作失败');
      } finally {
        this.submitLoading = false;
      }
    },
    
    resetForm() {
      this.menuForm = {
        name: '',
        path: '',
        url: '',
        parent_id: 0,
        sort_order: 0,
        status: 1,
        description: ''
      };
      this.$refs.menuForm && this.$refs.menuForm.clearValidate();
    }
  }
};
</script>


