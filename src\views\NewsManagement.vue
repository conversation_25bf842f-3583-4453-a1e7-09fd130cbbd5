<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>新闻资讯管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增新闻
        </el-button>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="新闻标题">
            <el-input v-model="searchForm.title" placeholder="请输入新闻标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="所属分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 150px;">
              <el-option v-for="newsCategory in newsCategories" :key="newsCategory.key" :label="newsCategory.name"
                :value="newsCategory.key" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 390px;" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="已发布" :value="1" />
              <el-option label="草稿" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 新闻列表表格 -->
      <el-table v-loading="loading" :data="newsList" border class="news-table">
        <el-table-column prop="id" label="ID" width="80" align="center" />

        <el-table-column prop="title" label="新闻标题" min-width="200" show-overflow-tooltip />

        <el-table-column label="缩略图" width="100" align="center">
          <template slot-scope="scope">
            <el-image v-if="scope.row.thumbnail" :src="scope.row.thumbnail"
              style="width: 60px; height: 45px; border-radius: 4px;" fit="cover"
              :preview-src-list="[scope.row.thumbnail]" />
            <span v-else class="no-image">无图片</span>
          </template>
        </el-table-column>

        <el-table-column label="所属分类" width="120" align="center">
          <template slot-scope="scope">
            {{ getNewsCategoryName(scope.row.category) }}
          </template>
        </el-table-column>

        <el-table-column prop="summary" label="新闻摘要" min-width="200" show-overflow-tooltip />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="view_count" label="浏览量" width="100" align="center" />

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <!-- <el-button
              size="mini"
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '下架' : '发布' }}
            </el-button> -->
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" />
      </div>
    </div>

    <!-- 新增/编辑新闻弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1000px" :close-on-click-modal="false"
      @close="handleDialogClose">
      <el-form ref="newsForm" :model="newsForm" :rules="formRules" label-width="100px">
        <el-form-item label="新闻标题" prop="title">
          <el-input v-model="newsForm.title" placeholder="请输入新闻标题" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属分类">
              <el-select v-model="newsForm.category" placeholder="请选择分类" style="width: 100%;">
                <el-option v-for="newsCategory in newsCategories" :key="newsCategory.key" :label="newsCategory.name"
                  :value="newsCategory.key" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number v-model="newsForm.sort_order" :min="0" :max="9999" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="缩略图">
          <ImageUploader v-model="newsForm.thumbnail" size="240x180" tip="建议尺寸：360x200像素，支持jpg、png格式" :max-size="5" />
        </el-form-item>

        <el-form-item label="新闻摘要">
          <el-input v-model="newsForm.summary" type="textarea" :rows="3" placeholder="请输入新闻摘要，简要描述新闻内容..." />
        </el-form-item>

        <el-form-item label="详情内容">
          <RichTextEditor v-model="newsForm.content" height="300px" placeholder="请输入新闻的详细内容..."
            @change="handleContentChange" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="newsForm.status">
                <el-radio :label="0">草稿</el-radio>
                <el-radio :label="1">已发布</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { news } from '@/api/news'
import { systemConfigs } from '@/api/system-configs'
import RichTextEditor from '@/components/RichTextEditor.vue'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  name: 'NewsManagement',
  components: {
    RichTextEditor,
    ImageUploader
  },
  data() {
    return {
      loading: false,
      submitting: false,
      newsList: [],
      newsCategories: [], // 动态新闻分类配置

      // 搜索表单
      searchForm: {
        title: '',
        category: '',
        dateRange: [],
        status: ''
      },

      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },

      // 弹窗相关
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      editId: null,

      // 新闻表单
      newsForm: {
        title: '',
        category: '',
        thumbnail: '',
        summary: '',
        content: '',
        status: 0,
        sort_order: 0
      },

      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入新闻标题', trigger: 'blur' },
          { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },

  mounted() {
    this.loadNewsCategories()
    this.loadNewsList()
  },

  methods: {
    // 获取新闻分类配置
    async loadNewsCategories() {
      try {
        const response = await systemConfigs.getByKey('news_categories')
        if (response.success && response.data) {
          this.newsCategories = response.data
        }
      } catch (error) {
        console.error('获取新闻分类配置失败:', error)
        this.$message.error('获取新闻分类配置失败')
      }
    },

    // 根据key获取新闻分类中文名称
    getNewsCategoryName(categoryKey) {
      const category = this.newsCategories.find(cat => cat.key === categoryKey)
      return category ? category.name : categoryKey
    },

    // 加载新闻列表
    async loadNewsList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          title: this.searchForm.title,
          category: this.searchForm.category,
          status: this.searchForm.status
        }

        // 处理创建时间范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.start_date = this.searchForm.dateRange[0]
          params.end_date = this.searchForm.dateRange[1]
        }

        const response = await news.getList(params)
        if (response.code === 200) {
          this.newsList = response.data.list
          this.pagination = response.data.pagination
        } else {
          this.$message.error(response.message || '获取新闻列表失败')
        }
      } catch (error) {
        console.error('获取新闻列表失败:', error)
        this.$message.error('获取新闻列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadNewsList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        category: '',
        dateRange: [],
        status: ''
      }
      this.pagination.current = 1
      this.loadNewsList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadNewsList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadNewsList()
    },

    // 新增新闻
    handleAdd() {
      this.dialogTitle = '新增新闻'
      this.isEdit = false
      this.editId = null
      this.resetForm()
      this.dialogVisible = true
    },

    // 编辑新闻
    async handleEdit(row) {
      this.dialogTitle = '编辑新闻'
      this.isEdit = true
      this.editId = row.id

      try {
        const response = await news.getDetail(row.id)
        if (response.code === 200) {
          const newsData = response.data
          this.newsForm = {
            title: newsData.title || '',
            category: newsData.category || '',
            thumbnail: newsData.thumbnail || '',
            summary: newsData.summary || '',
            content: newsData.content || '',
            status: newsData.status || 0,
            sort_order: newsData.sort_order || 0
          }
          this.dialogVisible = true
        } else {
          this.$message.error(response.message || '获取新闻详情失败')
        }
      } catch (error) {
        console.error('获取新闻详情失败:', error)
        this.$message.error('获取新闻详情失败')
      }
    },

    // 删除新闻
    handleDelete(row) {
      this.$confirm(`确定要删除新闻"${row.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await news.delete(row.id)
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.loadNewsList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除新闻失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 状态开关变化
    async handleStatusChange(row) {
      const statusText = row.status === 1 ? '发布' : '下架'

      try {
        const response = await news.toggleStatus(row.id, row.status)
        if (response.code === 200) {
          this.$message.success(`${statusText}成功`)
          this.loadNewsList()
        } else {
          this.$message.error(response.message || `${statusText}失败`)
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1
        }
      } catch (error) {
        console.error('切换状态失败:', error)
        this.$message.error(`${statusText}失败`)
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 切换状态
    async handleToggleStatus(row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '发布' : '下架'

      try {
        const response = await news.toggleStatus(row.id, newStatus)
        if (response.code === 200) {
          this.$message.success(`${statusText}成功`)
          this.loadNewsList()
        } else {
          this.$message.error(response.message || `${statusText}失败`)
        }
      } catch (error) {
        console.error('切换状态失败:', error)
        this.$message.error(`${statusText}失败`)
      }
    },

    // 提交表单
    async handleSubmit() {
      this.$refs.newsForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            let response
            if (this.isEdit) {
              response = await news.update(this.editId, this.newsForm)
            } else {
              response = await news.create(this.newsForm)
            }

            if (response.code === 200) {
              this.$message.success(this.isEdit ? '更新成功' : '创建成功')
              this.dialogVisible = false
              this.loadNewsList()
            } else {
              this.$message.error(response.message || (this.isEdit ? '更新失败' : '创建失败'))
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error(this.isEdit ? '更新失败' : '创建失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },

    // 富文本内容变化
    handleContentChange(content) {
      this.newsForm.content = content
    },

    // 弹窗关闭
    handleDialogClose() {
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.newsForm = {
        title: '',
        category: '',
        thumbnail: '',
        summary: '',
        content: '',
        status: 0,
        sort_order: 0
      }
      if (this.$refs.newsForm) {
        this.$refs.newsForm.resetFields()
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.news-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }

  .content-card {
    padding: 16px;
  }

  .search-bar .el-form {
    display: block;
  }

  .search-bar .el-form-item {
    display: block;
    margin-bottom: 15px;
  }

  .search-bar .el-form-item .el-input,
  .search-bar .el-form-item .el-select,
  .search-bar .el-form-item .el-date-picker {
    width: 100% !important;
  }
}
</style>
