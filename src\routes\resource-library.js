const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取资料库列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    category = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (category) {
    whereConditions.push('category = ?');
    queryParams.push(category);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM resource_library ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取资料库列表
  const resourcesQuery = `
    SELECT id, title, summary, category, status, files, sort_order, created_at, updated_at
    FROM resource_library
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const resources = await query(resourcesQuery, queryParams);

  // 处理files字段
  const processedResources = resources.map(row => {
    let files = [];
    try {
      if (row.files && typeof row.files === 'string') {
        files = JSON.parse(row.files);
      } else if (Array.isArray(row.files)) {
        files = row.files;
      }
    } catch (error) {
      console.error('解析files字段失败:', error, 'files值:', row.files);
      files = [];
    }
    return {
      ...row,
      files
    };
  });

  res.json({
    success: true,
    message: '获取资料库列表成功',
    data: {
      resources: processedResources,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取资料库详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query(
    'SELECT * FROM resource_library WHERE id = ?',
    [id]
  );

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '资料库记录不存在'
    });
  }

  let files = [];
  try {
    if (rows[0].files && typeof rows[0].files === 'string') {
      files = JSON.parse(rows[0].files);
    } else if (Array.isArray(rows[0].files)) {
      files = rows[0].files;
    }
  } catch (error) {
    console.error('解析files字段失败:', error);
    files = [];
  }

  const resource = {
    ...rows[0],
    files
  };

  res.json({
    success: true,
    message: '获取资料库详情成功',
    data: resource
  });
}));

// 创建资料库
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { title, summary, category = '其他', status = 1, files = [], sort_order = 0 } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '标题不能为空'
    });
  }



  const result = await query(
    'INSERT INTO resource_library (title, summary, category, status, files, sort_order) VALUES (?, ?, ?, ?, ?, ?)',
    [title, summary, category, parseInt(status), JSON.stringify(files), parseInt(sort_order)]
  );

  res.status(201).json({
    success: true,
    message: '创建资料库成功',
    data: {
      id: result.insertId,
      title,
      summary,
      category,
      status: parseInt(status),
      files,
      sort_order: parseInt(sort_order)
    }
  });
}));

// 更新资料库
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { title, summary, category, status, files, sort_order } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '标题不能为空'
    });
  }



  // 检查记录是否存在
  const existingRows = await query(
    'SELECT id FROM resource_library WHERE id = ?',
    [id]
  );

  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '资料库记录不存在'
    });
  }

  await query(
    'UPDATE resource_library SET title = ?, summary = ?, category = ?, status = ?, files = ?, sort_order = ? WHERE id = ?',
    [title, summary, category || '其他', parseInt(status), JSON.stringify(files || []), parseInt(sort_order || 0), id]
  );

  res.json({
    success: true,
    message: '更新资料库成功'
  });
}));

// 删除资料库
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query(
    'SELECT id FROM resource_library WHERE id = ?',
    [id]
  );

  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '资料库记录不存在'
    });
  }

  await query('DELETE FROM resource_library WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除资料库成功'
  });
}));

module.exports = router;
