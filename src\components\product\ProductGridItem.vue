<template>
  <div @click="$emit('view-detail', product)" class="product-grid-item">
    <div class="product-image">
      <img :src="product.main_image" v-if="product.main_image" :alt="product.name" />
      <div v-else class="no-image">
        <i class="fas fa-image"></i>
        <p>暂无图片</p>
      </div>
      <div class="product-overlay">
        <button class="view-details-btn">
          查看详情
        </button>
      </div>
    </div>

    <div class="product-info">
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-summary" v-html="product.summary"></p>

      <div class="product-meta">
        <span v-if="product.category_name" class="category">
          {{ product.category_name }}
        </span>
        <span v-if="product.view_count" class="view-count">
          {{ product.view_count }}次浏览
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductGridItem',
  props: {
    product: {
      type: Object,
      required: true
    }
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.product-grid-item {
  background: white;
  border-radius: @border-radius-large;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-grid-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  // border: 2px solid #D80514;
}

.product-grid-item:hover .product-overlay {
  opacity: 1;
}

.product-grid-item:hover .product-image img {
  transform: scale(1.05);
}

.product-image {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;

  img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .no-image {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #ccc;

    i {
      font-size: 48px;
      margin-bottom: 12px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .btn {
      padding: 10px 20px;
      border: 2px solid white;
      border-radius: 4px;
      background: transparent;
      color: white;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: white;
        color: #D80514;
      }
    }
  }
}

.product-info {
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 1;

  .product-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-summary {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin: 0 0 15px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
  }

  .product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;

    span {
      font-size: 12px;
      color: #999;
    }

    .category {
      background: #f5f5f5;
      padding: 2px 8px;
      border-radius: 12px;
      color: #666;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .product-grid-item {
    .product-image {
      height: 180px;

      img {
        height: 180px;
      }
    }

    .product-info {
      padding: 15px;

      .product-name {
        font-size: 18px;
      }

      .product-summary {
        font-size: 14px;
      }

      .product-meta {
        flex-direction: row;
        align-items: center;
        gap: 5px;
      }
    }
  }
}
</style>
