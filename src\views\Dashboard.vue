<template>
  <div class="page-container">
    <div class="content-card">
      <h2>欢迎使用雅克菲管理后台</h2>
      <!-- <p class="welcome-text">这里是系统仪表盘，您可以查看系统概况和快速操作。</p> -->
      
      <el-row :gutter="20" class="stats-row">
        <!-- <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-menu"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ menuCount }}</div>
              <div class="stat-label">菜单数量</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">1</div>
              <div class="stat-label">管理员数量</div>
            </div>
          </div>
        </el-col> -->
        
        <!-- <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-view"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ todayVisits }}</div>
              <div class="stat-label">今日访问</div>
            </div>
          </div>
        </el-col> -->
        
        <!-- <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-s-data"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalVisits }}</div>
              <div class="stat-label">总访问量</div>
            </div>
          </div>
        </el-col> -->
      </el-row>
      
      <el-row :gutter="20" class="quick-actions">
        <!-- <el-col :span="12">
          <el-card header="快速操作">
            <div class="action-buttons">
              <el-button type="primary" icon="el-icon-plus" @click="$router.push('/menu-management')">
                添加菜单
              </el-button>
              <el-button type="success" icon="el-icon-refresh" @click="refreshData">
                刷新数据
              </el-button>
              <el-button type="info" icon="el-icon-setting" @click="$message.info('功能开发中')">
                系统设置
              </el-button>
            </div>
          </el-card>
        </el-col> -->
        
        <el-col :span="24">
          <el-card header="系统信息">
            <div class="system-info">
              <div class="info-item">
                <span class="label">当前用户：</span>
                <span class="value">{{ userInfo ? userInfo.real_name || userInfo.username : '未知' }}</span>
              </div>
              <div class="info-item">
                <span class="label">登录时间：</span>
                <span class="value">{{ loginTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">系统版本：</span>
                <span class="value">v1.0.0</span>
              </div>
              <div class="info-item">
                <span class="label">服务状态：</span>
                <el-tag type="success" size="mini">正常运行</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'Dashboard',
  data() {
    return {
      menuCount: 0,
      todayVisits: 128,
      totalVisits: 5432,
      loginTime: ''
    };
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.initData();
  },
  methods: {
    ...mapActions(['getMenuList']),
    
    async initData() {
      this.loginTime = new Date().toLocaleString('zh-CN');
      await this.loadMenuCount();
    },
    
    async loadMenuCount() {
      try {
        const response = await this.getMenuList();
        this.menuCount = response.data.total || 0;
      } catch (error) {
        console.error('获取菜单数量失败:', error);
      }
    },
    
    async refreshData() {
      try {
        await this.loadMenuCount();
        this.$success('数据刷新成功');
      } catch (error) {
        this.$error('数据刷新失败');
      }
    }
  }
};
</script>

<style lang="less" scoped>
.welcome-text {
  color: #606266;
  margin-bottom: 30px;
  font-size: 16px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    
    i {
      font-size: 24px;
      color: white;
    }
  }
  
  .stat-content {
    flex: 1;
    
    .stat-number {
      font-size: 28px;
      font-weight: 600;
      color: #303133;
      line-height: 1;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #909399;
    }
  }
}

.quick-actions {
  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .system-info {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        width: 80px;
        color: #606266;
        font-size: 14px;
      }
      
      .value {
        flex: 1;
        color: #303133;
        font-size: 14px;
      }
    }
  }
}

/deep/ .el-card {
  .el-card__header {
    background: #f8f9fa;
    border-bottom: 1px solid #ebeef5;
    font-weight: 600;
    color: #303133;
  }
}
</style>
