<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>发展历程</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加历程
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="年份">
            <el-input v-model="searchForm.year" placeholder="请输入年份" clearable style="width: 120px;" />
          </el-form-item>
          <el-form-item label="标题">
            <el-input v-model="searchForm.title" placeholder="请输入标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 历程表格 -->
      <el-table v-loading="loading" :data="historyList" border class="history-table">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="year" label="年份" width="100" />
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="subtitle" label="副标题" min-width="150" />
        <el-table-column prop="summary" label="摘要" min-width="200" show-overflow-tooltip />

        <el-table-column label="图片" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.image_url" class="table-image">
              <img :src="scope.row.image_url" alt="历程图片" />
            </div>
            <span v-else class="no-image">无图片</span>
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="排序" width="80" />

        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="left">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current_page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.per_page"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total_items" />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="handleDialogClose">
      <el-form ref="historyForm" :model="historyForm" :rules="formRules" label-width="100px">
        <el-form-item label="年份" prop="year">
          <el-input-number v-model="historyForm.year" :min="1900" :max="2100" placeholder="请输入年份"
            style="width: 100%;" />
        </el-form-item>

        <el-form-item label="标题" prop="title">
          <el-input v-model="historyForm.title" placeholder="请输入标题" maxlength="200" show-word-limit />
        </el-form-item>

        <el-form-item label="副标题" prop="subtitle">
          <el-input v-model="historyForm.subtitle" placeholder="请输入副标题（可选）" maxlength="200" show-word-limit />
        </el-form-item>

        <el-form-item label="摘要" prop="summary">
          <el-input v-model="historyForm.summary" type="textarea" :rows="4" placeholder="请输入摘要" maxlength="1000"
            show-word-limit />
        </el-form-item>

        <el-form-item label="历程图片">
          <ImageUploader v-model="historyForm.image_url" text="上传历程图片" tip="建议尺寸：400x200px，支持jpg、png格式，大小不超过5MB"
            size="400x200" :max-size="5" />
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="historyForm.sort_order" :min="0" :max="9999" placeholder="数字越小排序越靠前" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="historyForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { developmentHistory } from '@/api'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  name: 'DevelopmentHistory',
  components: {
    ImageUploader
  },
  data() {
    return {
      loading: false,
      submitting: false,
      historyList: [],
      searchForm: {
        year: '',
        title: '',
        status: ''
      },
      pagination: {
        current_page: 1,
        per_page: 10,
        total_items: 0,
        total_pages: 0
      },
      dialogVisible: false,
      isEdit: false,
      historyForm: {
        year: new Date().getFullYear(),
        title: '',
        subtitle: '',
        summary: '',
        image_url: '',
        sort_order: 0,
        status: 1
      },
      formRules: {
        year: [
          { required: true, message: '请输入年份', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑历程' : '添加历程'
    }
  },
  created() {
    this.fetchHistoryList()
  },
  methods: {
    // 获取历程列表
    async fetchHistoryList() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.current_page,
          limit: this.pagination.per_page,
          year: this.searchForm.year,
          title: this.searchForm.title,
          status: this.searchForm.status
        }

        const response = await developmentHistory.getList(params)
        this.historyList = response.data.history || []
        this.pagination = response.data.pagination || {}
      } catch (error) {
        console.error('获取历程列表失败:', error)
        this.$message.error('获取历程列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current_page = 1
      this.fetchHistoryList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        year: '',
        title: '',
        status: ''
      }
      this.pagination.current_page = 1
      this.fetchHistoryList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.per_page = val
      this.pagination.current_page = 1
      this.fetchHistoryList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current_page = val
      this.fetchHistoryList()
    },

    // 添加
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
      this.historyForm = {
        year: new Date().getFullYear(),
        title: '',
        subtitle: '',
        summary: '',
        image_url: '',
        sort_order: 0,
        status: 1
      }
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.dialogVisible = true
      this.historyForm = {
        id: row.id,
        year: row.year,
        title: row.title,
        subtitle: row.subtitle,
        summary: row.summary,
        image_url: row.image_url || '',
        sort_order: row.sort_order,
        status: row.status
      }
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除历程 "${row.title}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await developmentHistory.delete(row.id)
          this.$message.success('删除成功')
          this.fetchHistoryList()
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.historyForm.validate()
        this.submitting = true

        if (this.isEdit) {
          await developmentHistory.update(this.historyForm.id, this.historyForm)
          this.$message.success('更新成功')
        } else {
          await developmentHistory.create(this.historyForm)
          this.$message.success('创建成功')
        }

        this.dialogVisible = false
        this.fetchHistoryList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.historyForm.resetFields()
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form {
  margin: 0;
}

.history-table {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.table-image {
  width: 80px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.table-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #999;
  font-size: 12px;
}
</style>
