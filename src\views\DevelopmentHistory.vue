<template>
  <div class="development-history">
    <!-- 页面头部 -->
    <PageHeader tag="发展历史" />
    <!-- 发展历程时间轴 -->
    <section class="timeline-section">
      <div class="container">
        <SectionHeader pageKey="development" blockKey="history_timeline" />

        <div class="timeline-container" v-if="!loading && historyData.length > 0">
          <div class="timeline-line"></div>
          <div v-for="(item, index) in historyData" :key="item.id"
            :class="['timeline-item', { 'timeline-item-left': index % 2 === 0, 'timeline-item-right': index % 2 === 1 }]"
            :ref="`timelineItem${index}`">
            <div class="timeline-content">
              <div class="timeline-year">{{ item.year }}</div>
              <div class="timeline-card">
                <h3 class="timeline-title">{{ item.title }}</h3>
                <p class="timeline-subtitle" v-if="item.subtitle">{{ item.subtitle }}</p>
                <p class="timeline-summary" v-html="item.summary"></p>
              </div>
            </div>

            <!-- 图片展示区域 - 放在时间轴的另一侧 -->
            <div class="timeline-image-container">
              <div class="timeline-image" v-if="item.image_url && item.image_url.trim()"
                @click="showImageModal(item.image_url, item.title)">
                <img :src="item.image_url" :alt="item.title" @error="onImageError" @load="onImageLoad" />
                <div class="image-overlay">
                  <i class="fas fa-search-plus"></i>
                </div>
              </div>
              <!-- 默认占位图 -->
              <div class="timeline-image placeholder" v-else>
                <div class="placeholder-content">
                  <i class="fas fa-image"></i>
                  <span>{{ item.year }}</span>
                </div>
              </div>
            </div>
            <div class="timeline-dot"></div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          <p>加载中...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-state">
          <i class="fas fa-exclamation-triangle"></i>
          <p>{{ error }}</p>
        </div>
      </div>
    </section>

    <!-- 图片查看器模态框 -->
    <div class="image-modal" v-if="imageModalVisible" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <button class="modal-close" @click="closeImageModal">
          <i class="fas fa-times"></i>
        </button>
        <img :src="currentImage.url" :alt="currentImage.title" />
        <div class="modal-info" v-if="currentImage.title">
          <h3>{{ currentImage.title }}</h3>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import SectionHeader from '@/components/common/SectionHeader.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import api from '@/api/index.js'

export default {
  name: 'DevelopmentHistory',
  components: {
    PageHeader,
    SectionHeader,
    Swiper,
    SwiperSlide
  },
  data() {
    return {
      // 发展历程数据
      historyData: [],
      loading: false,
      error: null,

      // 图片查看器
      imageModalVisible: false,
      currentImage: {
        url: '',
        title: ''
      },

      // 公司荣誉数据
      honorsData: [],
      honorsLoading: false,
      honorsError: null,
      flippedCards: {}, // 记录翻转状态

      // Swiper配置
      swiperOptions: {
        slidesPerView: 'auto',
        spaceBetween: 30,
        centeredSlides: true,
        loop: true,
        autoplay: {
          delay: 4000,
          disableOnInteraction: false
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          dynamicBullets: true
        },
        // navigation: {
        //   nextEl: '.swiper-button-next',
        //   prevEl: '.swiper-button-prev'
        // },
        breakpoints: {
          320: {
            slidesPerView: 1,
            spaceBetween: 20
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 25
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 30
          },
          1200: {
            slidesPerView: 4,
            spaceBetween: 30
          }
        }
      },

      // 动画观察器
      observer: null,

      // 窗口大小变化定时器
      resizeTimer: null
    }
  },
  mounted() {
    this.fetchHistoryData()
    // this.fetchHonorsData()
    // 延迟初始化动画，确保数据加载完成
    setTimeout(() => {
      this.initScrollAnimation()
    }, 500)

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeydown)
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
    // 移除键盘事件监听
    document.removeEventListener('keydown', this.handleKeydown)
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
    // 恢复页面滚动
    document.body.style.overflow = 'auto'
  },
  watch: {
    historyData: {
      handler(newData) {
        if (newData && newData.length > 0) {
          console.log('历程数据已加载，重新初始化动画')
          this.$nextTick(() => {
            setTimeout(() => {
              this.initScrollAnimation()
            }, 100)
          })
        }
      },
      immediate: false
    }
  },
  methods: {
    // 获取发展历程数据
    async fetchHistoryData() {
      try {
        this.loading = true
        this.error = null

        const response = await api.get('/api/front/about-us/history')
        if (response.success && response.data.history && response.data.history.length > 0) {
          this.historyData = response.data.history
          console.log('从API获取发展历程数据:', this.historyData)
        } else {
          this.error = '暂无发展历程数据'
        }
      } catch (error) {
        console.error('获取发展历程数据失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    // 获取公司荣誉数据
    async fetchHonorsData() {
      try {
        this.honorsLoading = true
        this.honorsError = null
        const response = await api.get('/api/front/about-us/honors')
        if (response.success) {
          this.honorsData = response.data.honors || []
        } else {
          throw new Error(response.message || '获取荣誉数据失败')
        }
      } catch (error) {
        console.error('获取荣誉数据失败:', error)
        this.honorsError = '网络错误，请稍后重试'
      } finally {
        this.honorsLoading = false
      }
    },

    // 初始化滚动动画
    initScrollAnimation() {
      if (!window.IntersectionObserver) {
        // 如果不支持IntersectionObserver，直接显示所有项目
        this.$nextTick(() => {
          const timelineItems = this.$el.querySelectorAll('.timeline-item')
          timelineItems.forEach(item => {
            item.classList.add('animate-in')
          })
          // this.adjustImageHeights()
        })
        return
      }

      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in')
          }
        })
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      })

      // 延迟观察，等待DOM渲染完成
      this.$nextTick(() => {
        const timelineItems = this.$el.querySelectorAll('.timeline-item')
        timelineItems.forEach((item) => {
          this.observer.observe(item)
        })
        // 调整图片高度
        // setTimeout(() => {
        //   this.adjustImageHeights()
        // }, 100)
      })
    },

    // 调整图片高度与文字卡片保持一致
    adjustImageHeights() {
      // 只在桌面端调整高度
      if (window.innerWidth <= 768) return

      this.$nextTick(() => {
        const timelineItems = this.$el.querySelectorAll('.timeline-item')
        timelineItems.forEach((item) => {
          const card = item.querySelector('.timeline-card')
          const imageContainer = item.querySelector('.timeline-image')

          if (card && imageContainer) {
            const cardHeight = card.offsetHeight
            imageContainer.style.height = cardHeight + 'px'
          }
        })
      })
    },

    // 切换卡片翻转状态
    toggleCard(honorId) {
      this.$set(this.flippedCards, honorId, !this.flippedCards[honorId])
    },

    // 暂停自动播放
    pauseAutoplay() {
      console.log('尝试暂停自动播放') // 调试日志
      if (this.$refs.honorsSwiper) {
        const swiperInstance = this.$refs.honorsSwiper.$swiper || this.$refs.honorsSwiper.swiper
        if (swiperInstance && swiperInstance.autoplay) {
          swiperInstance.autoplay.stop()
          console.log('自动播放已暂停') // 调试日志
        } else {
          console.log('无法访问swiper实例或autoplay') // 调试日志
        }
      } else {
        console.log('无法访问honorsSwiper ref') // 调试日志
      }
    },

    // 恢复自动播放
    resumeAutoplay() {
      console.log('尝试恢复自动播放') // 调试日志
      if (this.$refs.honorsSwiper) {
        const swiperInstance = this.$refs.honorsSwiper.$swiper || this.$refs.honorsSwiper.swiper
        if (swiperInstance && swiperInstance.autoplay) {
          swiperInstance.autoplay.start()
          console.log('自动播放已恢复') // 调试日志
        } else {
          console.log('无法访问swiper实例或autoplay') // 调试日志
        }
      } else {
        console.log('无法访问honorsSwiper ref') // 调试日志
      }
    },

    // 显示图片模态框
    showImageModal(imageUrl, title) {
      this.currentImage = {
        url: imageUrl,
        title: title
      }
      this.imageModalVisible = true
      // 防止页面滚动
      document.body.style.overflow = 'hidden'
    },

    // 关闭图片模态框
    closeImageModal() {
      this.imageModalVisible = false
      this.currentImage = {
        url: '',
        title: ''
      }
      // 恢复页面滚动
      document.body.style.overflow = 'auto'
    },

    // 图片加载错误处理
    onImageError(event) {
      console.warn('图片加载失败:', event.target.src)
      // 可以设置默认图片
      event.target.src = 'https://via.placeholder.com/400x200/f0f0f0/999999?text=图片加载失败'
    },

    // 图片加载成功处理
    onImageLoad(event) {
      console.log('图片加载成功:', event.target.src)
    },



    // 键盘事件处理
    handleKeydown(event) {
      if (event.key === 'Escape' && this.imageModalVisible) {
        this.closeImageModal()
      }
    },

    // 窗口大小变化处理
    handleResize() {
      // 防抖处理
      // clearTimeout(this.resizeTimer)
      // this.resizeTimer = setTimeout(() => {
      //   this.adjustImageHeights()
      // }, 200)
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.development-history {
  min-height: 100vh;
  background: @bg-color;
}

// 页面头部
.page-header {
  position: relative;
  height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: @bg-color;
  text-align: center;
  overflow: hidden;

  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }



  .container {
    position: relative;
    z-index: 3;
  }

  .page-title {
    color: #fff;
    font-size: @font-size-xxl;
    font-weight: @font-weight-bold;
    margin-bottom: @spacing-md;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .page-subtitle {
    color: #fff;
    font-size: @font-size-lg;
    opacity: 0.95;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// 时间轴部分
.timeline-section {
  padding: 66px 0;
  background: #f8f9fa;

  @media (max-width: 768px) {
    padding: 40px 0;
  }
}

.timeline-container {
  position: relative;
  max-width: 1000px;
  margin: @spacing-xxl auto 0;
  padding: 0 @spacing-lg;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, @primary-color, lighten(@primary-color, 20%));
  transform: translateX(-50%);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(216, 5, 20, 0.3);
}

.timeline-item {
  position: relative;
  margin-bottom: @spacing-xxl;
  opacity: 1; // 默认显示，避免动画失效时不可见
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  align-items: center;

  // 如果支持动画，则初始隐藏
  &:not(.animate-in) {
    opacity: 0;

    .timeline-content {
      transform: translateX(-50px);
    }

    .timeline-image-container {
      transform: translateX(50px);
    }
  }

  &.animate-in {
    opacity: 1;

    .timeline-content {
      transform: translateX(0);
    }

    .timeline-image-container {
      transform: translateX(0);
    }
  }

  &.timeline-item-left {
    .timeline-content {
      width: 45%;
      margin-right: 5%;
      text-align: right;
      transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s;
    }

    .timeline-image-container {
      width: 45%;
      margin-left: 5%;
      transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
    }

    .timeline-card {
      transform-origin: right center;
    }

    // 左侧项目的动画：文字从左侧进入，图片从右侧进入
    &:not(.animate-in) {
      .timeline-content {
        transform: translateX(-80px);
        opacity: 0;
      }

      .timeline-image-container {
        transform: translateX(80px);
        opacity: 0;
      }
    }

    &.animate-in {
      .timeline-content {
        transform: translateX(0);
        opacity: 1;
      }

      .timeline-image-container {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }

  &.timeline-item-right {
    flex-direction: row-reverse;

    .timeline-content {
      width: 45%;
      margin-left: 5%;
      text-align: left;
      transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s;
    }

    .timeline-image-container {
      width: 45%;
      margin-right: 5%;
      transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
    }

    .timeline-card {
      transform-origin: left center;
    }

    // 右侧项目的动画：文字从右侧进入，图片从左侧进入
    &:not(.animate-in) {
      .timeline-content {
        transform: translateX(80px);
        opacity: 0;
      }

      .timeline-image-container {
        transform: translateX(-80px);
        opacity: 0;
      }
    }

    &.animate-in {
      .timeline-content {
        transform: translateX(0);
        opacity: 1;
      }

      .timeline-image-container {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }
}

.timeline-dot {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 20px;
  height: 20px;
  background: @primary-color;
  border: 4px solid @bg-color;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  box-shadow: 0 0 0 4px rgba(216, 5, 20, 0.2);
  z-index: 2;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
  opacity: 0;

  .timeline-item.animate-in & {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    animation: pulse 2s infinite 0.5s;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(216, 5, 20, 0.2), 0 0 0 8px rgba(216, 5, 20, 0.1);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(216, 5, 20, 0.1), 0 0 0 16px rgba(216, 5, 20, 0.05);
  }

  100% {
    box-shadow: 0 0 0 4px rgba(216, 5, 20, 0.2), 0 0 0 8px rgba(216, 5, 20, 0.1);
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }

  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }

  100% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
}

.timeline-year {
  font-size: @font-size-xl;
  font-weight: @font-weight-bold;
  color: @primary-color;
  margin-bottom: @spacing-md;
  letter-spacing: 1px;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
  transform: translateY(20px);
  opacity: 0;

  .timeline-item.animate-in & {
    transform: translateY(0);
    opacity: 1;
  }
}

.timeline-card {
  background: @bg-color;
  padding: @spacing-xl;
  border-radius: @border-radius-large;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid @border-color;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

// 图片容器区域
.timeline-image-container {
  display: flex;
  align-items: center;
}

// 图片容器
.timeline-image {
  position: relative;
  width: 100%;
  min-height: 180px; // 默认最小高度
  overflow: hidden;
  cursor: pointer;
  border-radius: @border-radius-large;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid @border-color;
  transition: all 0.3s ease;
  background: @bg-color;
  display: flex;
  align-items: center;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }

  // 图片遮罩层
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    i {
      color: @bg-color;
      font-size: @font-size-xl;
    }
  }

  &:hover .image-overlay {
    opacity: 1;
  }

  // 占位图样式
  &.placeholder {
    background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: default;

    .placeholder-content {
      text-align: center;
      color: #999;

      i {
        font-size: @font-size-xxl;
        margin-bottom: @spacing-sm;
        display: block;
      }

      span {
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;
      }
    }

    &:hover {
      transform: none;
    }

    &:hover img {
      transform: none;
    }

    .image-overlay {
      display: none;
    }
  }
}

.timeline-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-medium;
  color: @text-color;
  margin-bottom: @spacing-sm;
  line-height: 1.4;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s;
  transform: translateY(20px);
  opacity: 0;

  .timeline-item.animate-in & {
    transform: translateY(0);
    opacity: 1;
  }
}

.timeline-subtitle {
  font-size: @font-size-base;
  color: @primary-color;
  font-weight: @font-weight-medium;
  margin-bottom: @spacing-md;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s;
  transform: translateY(20px);
  opacity: 0;

  .timeline-item.animate-in & {
    transform: translateY(0);
    opacity: 1;
  }
}

.timeline-summary {
  font-size: @font-size-base;
  color: @text-light;
  line-height: 1.6;
  margin: 0;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.7s;
  transform: translateY(20px);
  opacity: 0;
  text-align: left;

  .timeline-item.animate-in & {
    transform: translateY(0);
    opacity: 1;
  }
}

// 荣誉展示部分
.honors-section {
  padding: @spacing-xxl 0;
  background: @bg-light;
}

.honors-container {
  margin-top: @spacing-xxl;
}

.honors-swiper {
  padding: @spacing-lg 0 @spacing-xxl;
  overflow: visible;

  .swiper-slide {
    width: 240px;
    height: 320px;
  }
}

.honor-card {
  position: relative;
  width: 100%;
  height: 100%;
  perspective: 1000px;
  cursor: pointer;

  .card-front,
  .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: @border-radius-large;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
  }

  .card-front {
    background: @bg-color;
    transform: rotateY(0deg);
  }

  .card-back {
    background: linear-gradient(45deg, @primary-color, #ff6b6b, #4ecdc4, @primary-color);
    background-size: 400% 400%;
    animation: gradientFlow 8s ease infinite;
    color: @bg-color;
    transform: rotateY(-180deg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: @spacing-xl;
    text-align: center;
    position: relative;
    overflow: hidden;

    // 添加光泽效果
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      animation: shine 3s ease-in-out infinite;
      pointer-events: none;
    }
  }

  &.flipped {
    .card-front {
      transform: rotateY(180deg);
    }

    .card-back {
      transform: rotateY(0deg);
    }
  }

  // 桌面端hover效果
  @media (min-width: 769px) {
    &:hover:not(.flipped) {
      .card-front {
        transform: rotateY(180deg);
      }

      .card-back {
        transform: rotateY(0deg);
      }
    }
  }
}

.honor-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.flip-btn {
  position: absolute;
  top: @spacing-md;
  right: @spacing-md;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  color: @bg-color;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-base;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
  }

  &.back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: @bg-color;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.honor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.honor-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-bold;
  margin-bottom: @spacing-md;
  line-height: 1.3;
  color: @bg-color !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.honor-date {
  font-size: @font-size-base;
  opacity: 0.95;
  margin-bottom: @spacing-lg;
  font-weight: @font-weight-medium;
  color: @bg-color !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.honor-summary {
  font-size: @font-size-base;
  line-height: 1.6;
  opacity: 0.95;
  margin: 0;
  color: @bg-color !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

// Swiper自定义样式
/deep/ .honors-swiper {
  .swiper-pagination {
    bottom: -@spacing-md;
    margin-top: @spacing-md;

    .swiper-pagination-bullet-active {
      background: @primary-color !important;
    }
  }

  .swiper-button-prev,
  .swiper-button-next {
    width: 50px;
    height: 50px;
    background: @bg-color;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    color: @primary-color;
    font-size: @font-size-lg;
    transition: all 0.3s ease;

    &:hover {
      background: @primary-color;
      color: @bg-color;
      transform: scale(1.1);
    }

    &::after {
      font-size: @font-size-base;
      font-weight: @font-weight-bold;
    }
  }

  .swiper-button-prev {
    left: -25px;
  }

  .swiper-button-next {
    right: -25px;
  }
}

// 图片模态框
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
  padding: @spacing-lg;

  .modal-content {
    position: relative;
    max-width: 95vw;
    max-height: 95vh;
    background: @bg-color;
    border-radius: @border-radius-large;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);

    img {
      width: 100%;
      height: auto;
      max-height: 80vh;
      min-height: 400px;
      max-width: 90vw;
      object-fit: contain;
      display: block;
    }

    .modal-info {
      padding: @spacing-lg;
      text-align: center;
      border-top: 1px solid @border-color;

      h3 {
        margin: 0;
        color: @text-color;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;
      }
    }
  }

  .modal-close {
    position: absolute;
    top: @spacing-md;
    right: @spacing-md;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    color: @bg-color;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: scale(1.1);
    }

    i {
      font-size: @font-size-base;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

// 加载和错误状态
.loading-state,
.error-state {
  text-align: center;
  padding: @spacing-xxl;
  color: @text-light;

  i {
    font-size: @font-size-xxl;
    margin-bottom: @spacing-md;
    display: block;
  }

  p {
    font-size: @font-size-lg;
    margin: 0;
  }
}

.loading-state i {
  color: @primary-color;
}

.error-state i {
  color: @primary-color;
}

// 移动端显示控制
.mobile-only {
  display: none;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    // height: 300px;
    height: auto;

    .page-title {
      font-size: @font-size-xl;
    }

    .page-subtitle {
      font-size: @font-size-base;
    }
  }

  .timeline-container {
    padding: 0 @spacing-md;
  }

  .timeline-line {
    left: 30px;
    transform: none;
  }

  .timeline-container {
    padding: 0 @spacing-sm;
  }

  .timeline-item {
    flex-direction: column;
    align-items: stretch;

    &.timeline-item-left,
    &.timeline-item-right {
      flex-direction: column;

      .timeline-content {
        width: calc(100% - 60px);
        margin: 0 0 @spacing-lg 60px;
        text-align: left;
      }

      .timeline-image-container {
        width: calc(100% - 60px);
        margin: 0 0 0 60px;
      }

      // 移动端统一动画：从下方进入
      &:not(.animate-in) {
        .timeline-content {
          transform: translateY(30px);
          opacity: 0;
        }

        .timeline-image-container {
          transform: translateY(40px);
          opacity: 0;
        }
      }

      &.animate-in {
        .timeline-content {
          transform: translateY(0);
          opacity: 1;
        }

        .timeline-image-container {
          transform: translateY(0);
          opacity: 1;
        }
      }
    }
  }

  .timeline-dot {
    left: 22px;
    transform: translateX(-50%);
  }

  .timeline-card {
    padding: @spacing-lg;
  }

  .timeline-image {
    height: 150px;
  }

  .timeline-title {
    font-size: @font-size-base;
  }

  .timeline-summary {
    font-size: @font-size-sm;
  }

  // 图片模态框移动端优化
  .image-modal {
    .modal-content {
      max-width: 98vw;
      max-height: 98vh;
      margin: @spacing-sm;

      img {
        max-height: 80vh;
        min-height: 250px;
      }

      .modal-info {
        padding: @spacing-md;

        h3 {
          font-size: @font-size-base;
        }
      }
    }

    .modal-close {
      width: 35px;
      height: 35px;
      top: @spacing-sm;
      right: @spacing-sm;
    }
  }

  .honors-swiper {
    .swiper-slide {
      width: 210px;
      height: 280px;
    }

    .swiper-button-prev,
    .swiper-button-next {
      display: none;
    }
  }

  .honor-card {

    .card-front,
    .card-back {
      border-radius: @border-radius;
    }

    .card-back {
      padding: @spacing-lg;
    }
  }

  .honor-title {
    font-size: @font-size-base;
  }

  .honor-date,
  .honor-summary {
    font-size: @font-size-sm;
  }

  .mobile-only {
    display: flex;
  }
}

@media (max-width: 480px) {
  .timeline-container {
    padding: 0 @spacing-xs;
  }

  .timeline-item {
    margin-bottom: @spacing-xl;

    &.timeline-item-left,
    &.timeline-item-right {
      .timeline-content {
        width: calc(100% - 50px);
        margin: 0 0 @spacing-lg 50px;
      }

      .timeline-image-container {
        width: calc(100% - 50px);
        margin: 0 0 0 50px;
      }
    }
  }

  .timeline-line {
    left: 25px;
  }

  .timeline-dot {
    left: 22px;
  }

  .timeline-card {
    padding: @spacing-md;
  }

  .timeline-image {
    height: 120px;

    &.placeholder .placeholder-content {
      i {
        font-size: @font-size-lg;
      }

      span {
        font-size: @font-size-base;
      }
    }
  }

  .honors-swiper {
    .swiper-slide {
      width: 180px;
      height: 240px;
    }
  }

  .honor-card {
    .card-back {
      padding: @spacing-md;
    }
  }
}

// 高分辨率屏幕优化
@media (min-width: 1400px) {
  .timeline-container {
    max-width: 1200px;
  }

  .honors-swiper {
    .swiper-slide {
      width: 270px;
      height: 360px;
    }
  }
}
</style>
