const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取视频教程列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    type = '',
    status = '',
    tag = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (type) {
    whereConditions.push('type = ?');
    queryParams.push(type);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  if (tag) {
    whereConditions.push('JSON_CONTAINS(tags, ?)');
    queryParams.push(JSON.stringify(tag));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM video_tutorials ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取视频教程列表
  const tutorialsQuery = `
    SELECT id, title, summary, description, video_code, status, tags, type, sort_order, created_at, updated_at
    FROM video_tutorials
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const tutorials = await query(tutorialsQuery, queryParams);

  // 处理tags字段
  const processedTutorials = tutorials.map(row => {
    let tags = [];
    try {
      if (row.tags && typeof row.tags === 'string') {
        tags = JSON.parse(row.tags);
      } else if (Array.isArray(row.tags)) {
        tags = row.tags;
      }
    } catch (error) {
      console.error('解析tags字段失败:', error, 'tags值:', row.tags);
      tags = [];
    }
    return {
      ...row,
      tags
    };
  });

  res.json({
    success: true,
    message: '获取视频教程列表成功',
    data: {
      tutorials: processedTutorials,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取视频教程详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query(
    'SELECT * FROM video_tutorials WHERE id = ?',
    [id]
  );

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '视频教程记录不存在'
    });
  }

  let tags = [];
  try {
    if (rows[0].tags && typeof rows[0].tags === 'string') {
      tags = JSON.parse(rows[0].tags);
    } else if (Array.isArray(rows[0].tags)) {
      tags = rows[0].tags;
    }
  } catch (error) {
    console.error('解析tags字段失败:', error);
    tags = [];
  }

  const tutorial = {
    ...rows[0],
    tags
  };

  res.json({
    success: true,
    message: '获取视频教程详情成功',
    data: tutorial
  });
}));

// 创建视频教程
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    title,
    summary,
    description,
    video_code,
    status = 1,
    tags = [],
    type,
    sort_order = 0
  } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '标题不能为空'
    });
  }

  // if (!type || !['使用教程', '安装教程'].includes(type)) {
  //   return res.status(400).json({
  //     success: false,
  //     message: '视频类型必须是"使用教程"或"安装教程"'
  //   });
  // }

  const result = await query(
    'INSERT INTO video_tutorials (title, summary, description, video_code, status, tags, type, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [title, summary, description, video_code, parseInt(status), JSON.stringify(tags), type, parseInt(sort_order)]
  );

  res.status(201).json({
    success: true,
    message: '创建视频教程成功',
    data: {
      id: result.insertId,
      title,
      summary,
      description,
      video_code,
      status: parseInt(status),
      tags,
      type,
      sort_order: parseInt(sort_order)
    }
  });
}));

// 更新视频教程
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { title, summary, description, video_code, status, tags, type, sort_order } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '标题不能为空'
    });
  }

  // if (!type || !['使用教程', '安装教程'].includes(type)) {
  //   return res.status(400).json({
  //     success: false,
  //     message: '视频类型必须是"使用教程"或"安装教程"'
  //   });
  // }

  // 检查记录是否存在
  const existingRows = await query(
    'SELECT id FROM video_tutorials WHERE id = ?',
    [id]
  );

  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '视频教程记录不存在'
    });
  }

  await query(
    'UPDATE video_tutorials SET title = ?, summary = ?, description = ?, video_code = ?, status = ?, tags = ?, type = ?, sort_order = ? WHERE id = ?',
    [title, summary, description, video_code, parseInt(status), JSON.stringify(tags || []), type, parseInt(sort_order || 0), id]
  );

  res.json({
    success: true,
    message: '更新视频教程成功'
  });
}));

// 删除视频教程
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query(
    'SELECT id FROM video_tutorials WHERE id = ?',
    [id]
  );

  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '视频教程记录不存在'
    });
  }

  await query('DELETE FROM video_tutorials WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除视频教程成功'
  });
}));

module.exports = router;
