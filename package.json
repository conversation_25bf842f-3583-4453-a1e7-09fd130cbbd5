{"name": "airfit-admin", "version": "1.0.0", "description": "雅克菲采暖官网管理后台", "main": "src/main.js", "scripts": {"dev": "webpack-dev-server --mode development --port 8089 --open", "build": "webpack --mode production", "serve": "webpack-dev-server --mode development --port 8089", "lint": "eslint --ext .js,.vue src"}, "keywords": ["vue", "element-ui", "admin", "airfit"], "author": "Airfit Development Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "quill": "^2.0.3", "vue": "^2.6.14", "vue-router": "^3.5.4", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.17.0", "babel-loader": "^8.2.3", "copy-webpack-plugin": "^10.2.4", "css-loader": "^6.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "less": "^4.1.2", "less-loader": "^10.2.0", "style-loader": "^3.3.1", "url-loader": "^4.1.1", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14", "webpack": "^5.69.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}