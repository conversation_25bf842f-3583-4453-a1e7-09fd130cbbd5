{"name": "airfit-backend", "version": "1.0.0", "description": "雅克菲采暖官网后端API服务", "main": "src/app.js", "scripts": {"start": "pm2 start src/app.js --name airfit-backend --watch", "dev": "nodemon src/app.js", "init-db": "node scripts/init-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["airfit", "heating", "backend", "api"], "author": "Airfit Development Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.6.5", "playwright": "^1.53.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}