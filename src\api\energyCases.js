import { request } from './http'

// 节能案例API服务
export const energyCases = {
  // 获取案例列表
  getList(params = {}) {
    return request.get('/api/energy-cases', params)
  },

  // 获取案例详情
  getDetail(id) {
    return request.get(`/api/energy-cases/${id}`)
  },

  // 创建案例
  create(data) {
    return request.post('/api/energy-cases', data)
  },

  // 更新案例
  update(id, data) {
    return request.put(`/api/energy-cases/${id}`, data)
  },

  // 删除案例
  delete(id) {
    return request.delete(`/api/energy-cases/${id}`)
  },

  // 批量更新状态
  batchUpdateStatus(ids, status) {
    return request.post('/api/energy-cases/batch-status', { ids, status })
  }
}

export default energyCases
