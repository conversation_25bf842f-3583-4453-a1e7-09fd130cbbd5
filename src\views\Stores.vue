<template>
  <div class="stores">
    <!-- 页面头部 -->
    <PageHeader tag="经销体系" />

    <!-- 经销商查询 -->
    <section class="dealer-search">
      <div class="container">
        <SectionHeader pageKey="stores" blockKey="dealer_search" />
        <div class="search-form">
          <select v-model="selectedProvince" @change="handleProvinceChange" class="province-select">
            <option value="">全部省份</option>
            <option v-for="province in provinces" :key="province.value" :value="province.value">
              {{ province.label }}
            </option>
          </select>
          <select v-model="selectedCity" @change="handleSearch" class="city-select" :disabled="!selectedProvince">
            <option value="">全部城市</option>
            <option v-for="city in cities" :key="city.value" :value="city.value">
              {{ city.label }}
            </option>
          </select>
          <input v-model="companyName" @input="handleSearch" type="text" placeholder="请输入公司名称" class="company-input" />
          <button @click="handleReset" class="reset-btn">
            <i class="fas fa-redo"></i>
            <span>重置</span>
          </button>
        </div>
      </div>
    </section>

    <!-- 经销商列表 -->
    <section class="dealers-list">
      <div class="container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchDealers">重试</button>
          </div>
        </div>

        <!-- 经销商表格 -->
        <div v-else>
          <div v-if="dealers.length === 0" class="no-dealers">
            <div class="no-dealers-content">
              <i class="fas fa-store"></i>
              <h3>暂无经销商</h3>
              <p>该省份暂时没有经销商，请选择其他省份查看</p>
              <button class="btn btn-primary" @click="selectedProvince = ''; selectedCity = ''; fetchDealers()">
                查看全部经销商
              </button>
            </div>
          </div>

          <div v-else class="dealers-table-container">
            <table class="dealers-table">
              <thead>
                <tr>
                  <th>所属省份</th>
                  <th>所属城市</th>
                  <th>公司名称</th>
                  <th>公司地址</th>
                  <th>联系电话</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="dealer in dealers" :key="dealer.id" class="dealer-row">
                  <td>{{ dealer.province }}</td>
                  <td>{{ dealer.city_name }}</td>
                  <td class="company-name">{{ dealer.company_name }}</td>
                  <td class="company-location">
                    <div v-html="dealer.company_location"></div>
                  </td>
                  <td class="phone-number">
                    <a :href="`tel:${dealer.phone}`" class="phone-link">
                      <i class="fas fa-phone"></i>
                      {{ dealer.phone }}
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <Pagination v-if="pagination.pages > 1" :current-page="pagination.page" :total-pages="pagination.pages"
            :total-items="pagination.total" @page-change="handlePageChange" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import Pagination from '@/components/common/Pagination.vue'
import api from '@/api/index.js'
import { getProvinceList, getCityListByProvince } from '@/utils/area'

export default {
  name: 'Stores',
  components: {
    PageHeader,
    SectionHeader,
    Pagination
  },
  data() {
    return {
      selectedProvince: '',
      selectedCity: '',
      companyName: '',
      loading: false,
      error: null,
      dealers: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        pages: 0
      },
      // 省份列表
      provinces: [],
      // 城市列表
      cities: [],
      // 搜索防抖定时器
      searchTimer: null,
      // 省市数据加载状态
      provincesLoading: false,
      citiesLoading: false
    }
  },
  mounted() {
    this.loadProvinces()
    this.fetchDealers()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  },
  methods: {
    async fetchDealers() {
      try {
        this.loading = true
        this.error = null

        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        // 添加省份筛选参数
        if (this.selectedProvince) {
          params.province = this.selectedProvince
        }

        // 添加城市筛选参数
        if (this.selectedCity) {
          params.city_name = this.selectedCity
        }

        // 添加公司名称筛选参数
        if (this.companyName && this.companyName.trim()) {
          params.keyword = this.companyName.trim()
        }

        const response = await api.get('/api/front/dealers', { params })

        if (response.success) {
          this.dealers = response.data.dealers
          this.pagination = response.data.pagination
        } else {
          throw new Error(response.message || '获取数据失败')
        }

      } catch (error) {
        console.error('获取经销商列表失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    // 加载省份列表
    loadProvinces() {
      this.provinces = getProvinceList()
    },

    // 根据省份获取城市列表
    loadCities(province) {
      this.cities = getCityListByProvince(province)
    },

    // 省份变化处理
    handleProvinceChange() {
      this.selectedCity = '' // 重置城市选择
      this.cities = [] // 清空城市列表

      if (this.selectedProvince) {
        this.loadCities(this.selectedProvince)
      }

      this.handleSearch()
    },

    // 搜索处理（带防抖）
    handleSearch() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      // 重置到第一页
      this.pagination.page = 1

      // 设置防抖延迟
      this.searchTimer = setTimeout(() => {
        this.fetchDealers()
      }, 500)
    },

    // 重置搜索条件
    handleReset() {
      this.selectedProvince = ''
      this.selectedCity = ''
      this.cities = []
      this.companyName = ''
      this.pagination.page = 1
      this.fetchDealers()
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.fetchDealers()
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.stores {
  min-height: 100vh;
}

.dealer-search {
  padding: 66px 0;
  background: @bg-light;

  .container {
    max-width: @container-max-width;
    margin: 0 auto;
    padding: 0 @spacing-lg;
  }

  .search-form {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: @spacing-lg;
    margin-top: @spacing-xl;
    flex-wrap: nowrap; // 桌面端不换行

    .province-select,
    .city-select,
    .company-input {
      flex: 1;
      max-width: 220px;
      min-width: 180px;
      padding: @spacing-md @spacing-lg;
      border: 2px solid @border-color;
      border-radius: @border-radius;
      font-size: @font-size-sm;
      background: @bg-color;
      color: @text-color;
      transition: @transition;

      &:focus {
        outline: none;
        border-color: @primary-color;
        box-shadow: 0 0 0 3px rgba(216, 5, 20, 0.1);
      }

      &:hover {
        border-color: @primary-color;
      }

      &:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
        border-color: #e0e0e0;

        &:hover {
          border-color: #e0e0e0;
        }
      }
    }

    .province-select,
    .city-select {
      cursor: pointer;

      // 重置默认样式，确保跨平台一致性
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;

      // 添加自定义下拉箭头
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right @spacing-md center;
      background-size: 16px;
      padding-right: 40px; // 为箭头留出空间

      // 移除 IE 的默认箭头
      &::-ms-expand {
        display: none;
      }

      // 确保在所有浏览器中都有统一的字体
      font-family: inherit;
      line-height: 1.5;
    }

    .company-input {
      &::placeholder {
        color: @text-light;
      }
    }

    .reset-btn {
      flex-shrink: 0;
      padding: @spacing-md @spacing-lg;
      background: @bg-light;
      border: 2px solid @border-color;
      border-radius: @border-radius;
      color: @text-color;
      font-size: @font-size-base;
      cursor: pointer;
      transition: @transition;
      display: flex;
      align-items: center;
      gap: @spacing-sm;
      white-space: nowrap;

      &:hover {
        background: @primary-color;
        border-color: @primary-color;
        color: @bg-color;
      }

      i {
        font-size: @font-size-sm;
      }
    }

    // 平板端适配 (768px - 1024px)
    @media (max-width: 1024px) and (min-width: 769px) {
      gap: @spacing-md;

      .province-select,
      .city-select,
      .company-input {
        max-width: 180px;
        min-width: 150px;
      }

      .reset-btn {
        padding: @spacing-md;

        span {
          display: none; // 隐藏文字，只显示图标
        }
      }
    }

    // 移动端适配 (≤768px)
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: @spacing-md;

      .province-select,
      .city-select,
      .company-input,
      .reset-btn {
        flex: none;
        width: 100%;
        max-width: none;
        min-width: auto;
      }

      .reset-btn {
        span {
          display: inline; // 移动端显示文字
        }
      }
    }

    // 小屏移动端适配 (≤480px)
    @media (max-width: 480px) {
      gap: @spacing-sm;

      .province-select,
      .city-select,
      .company-input,
      .reset-btn {
        padding: @spacing-sm @spacing-md;
        font-size: @font-size-sm;
      }

      // 移动端下拉箭头位置调整
      .province-select,
      .city-select {
        background-size: 14px;
        padding-right: 35px;
      }
    }
  }
}

.dealers-list {
  padding: @spacing-xxl 0;
  background: #f8f9fa;
  padding-top: 0;

  .container {
    max-width: @container-max-width;
    margin: 0 auto;
    padding: 0 @spacing-lg;
  }
}

// 加载和错误状态
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-spinner,
.error-content {
  text-align: center;

  i {
    font-size: 48px;
    color: @primary-color;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 无经销商状态
.no-dealers {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.no-dealers-content {
  text-align: center;

  i {
    font-size: 64px;
    color: @text-lighter;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 经销商表格
.dealers-table-container {
  background: @bg-color;
  border-radius: @border-radius;
  box-shadow: @shadow;
  overflow: hidden;
  margin-bottom: @spacing-xxl;
}

.dealers-table {
  width: 100%;
  border-collapse: collapse;

  thead {
    background: @primary-color;

    th {
      padding: @spacing-lg @spacing-md;
      text-align: left;
      font-weight: @font-weight-medium;
      color: white;
      font-size: @font-size-base;
      border-bottom: none;

      &:first-child {
        padding-left: @spacing-lg;
      }

      &:last-child {
        padding-right: @spacing-lg;
      }
    }
  }

  tbody {
    .dealer-row {
      transition: @transition;

      &:hover {
        background: @bg-light;
      }

      &:not(:last-child) {
        border-bottom: 1px solid @border-color;
      }

      td {
        padding: @spacing-lg @spacing-md;
        vertical-align: middle;
        font-size: @font-size-base;
        color: @text-color;

        &:first-child {
          padding-left: @spacing-lg;
          font-weight: @font-weight-medium;
          color: @primary-color;
        }

        &:last-child {
          padding-right: @spacing-lg;
        }

        &.company-name {
          font-weight: @font-weight-medium;
          color: @text-color;
        }

        &.company-location {
          color: @text-light;
          max-width: 300px;
          word-wrap: break-word;
        }

        &.phone-number {
          .phone-link {
            display: inline-flex;
            align-items: center;
            gap: @spacing-xs;
            color: @primary-color;
            text-decoration: none;
            transition: @transition;

            &:hover {
              color: @primary-hover;
              text-decoration: underline;
            }

            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

}

@media (max-width: @screen-sm) {


  .dealers-table {
    width: 100% !important;
    min-width: auto !important;
    font-size: @font-size-xs;

    thead th {
      padding: @spacing-sm;
    }

    tbody td {
      padding: @spacing-sm;
      font-size: 12px !important;

      &.company-location {
        max-width: 150px;
        font-size: @font-size-xs;
      }

      &.phone-number .phone-link {
        font-size: @font-size-xs;
      }
    }
  }
}

// 响应式设计
@media (max-width: @screen-lg) {
  .dealers-table-container {
    overflow-x: auto;
  }

  .dealers-table {
    min-width: 600px;
  }
}

@media (max-width: @screen-md) {
  .dealer-search {
    padding: 40px 0;

    .container {
      padding: 0 @spacing-md;
    }

    .search-form {

      .province-select,
      .city-select {
        min-width: 150px;
        padding: @spacing-sm @spacing-md;
      }
    }
  }

  .dealers-list {
    padding: @spacing-xl 0;
    background-color: #f8f9fa;

    .container {
      padding: 0 @spacing-md;
    }
  }

  .dealers-table {
    font-size: @font-size-sm;

    thead th {
      padding: @spacing-md @spacing-sm;
      font-size: @font-size-sm;
    }

    tbody td {
      padding: @spacing-md @spacing-sm;

      &.company-location {
        max-width: 200px;
      }
    }
  }
}

@media (max-width: @screen-sm) {
  .city-select {
    min-width: 100% !important;
  }
}
</style>
