const express = require('express');
const { query } = require('../../config/database');
const { CustomError, asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取能源案例列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 12,
    case_type,
    case_area,
    product_category,
    keyword
  } = req.query;

  let whereConditions = ['status = 1']; // 只返回已发布的案例
  let params = [];

  // 案例类型筛选
  if (case_type) {
    whereConditions.push('case_type = ?');
    params.push(case_type);
  }

  // 案例区域筛选
  if (case_area) {
    whereConditions.push('case_area = ?');
    params.push(case_area);
  }

  // 产品分类筛选
  if (product_category) {
    whereConditions.push('product_category = ?');
    params.push(product_category);
  }

  // 关键词搜索
  if (keyword) {
    whereConditions.push('(title LIKE ? OR summary LIKE ? OR case_tags LIKE ?)');
    params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
  }

  const whereClause = whereConditions.length > 0
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  // 计算分页
  const offset = (page - 1) * limit;

  // 查询案例列表
  const casesQuery = `
    SELECT
      id,
      title,
      main_image,
      summary,
      case_type,
      case_area,
      product_category,
      case_tags,
      view_count,
      created_at
    FROM energy_cases
    ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const cases = await query(casesQuery, params);

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM energy_cases
    ${whereClause}
  `, params);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取能源案例列表成功',
    data: {
      cases,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取能源案例详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 查询案例详情
  const cases = await query(`
    SELECT *
    FROM energy_cases
    WHERE id = ? AND status = 1
  `, [id]);

  if (cases.length === 0) {
    throw new CustomError('案例不存在或已下架', 404);
  }

  const energyCase = cases[0];

  // 增加浏览量
  await query('UPDATE energy_cases SET view_count = view_count + 1 WHERE id = ?', [id]);
  energyCase.view_count += 1;

  // 获取相关案例（同类型的其他案例）
  const relatedCases = await query(`
    SELECT id, title, main_image, summary, case_type
    FROM energy_cases 
    WHERE case_type = ? AND id != ? AND status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 4
  `, [energyCase.case_type || '', id]);

  res.json({
    success: true,
    message: '获取能源案例详情成功',
    data: {
      case: energyCase,
      related_cases: relatedCases
    }
  });
}));

// 获取热门能源案例
router.get('/hot/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;

  const cases = await query(`
    SELECT 
      id,
      title,
      main_image,
      summary,
      case_type,
      view_count
    FROM energy_cases
    WHERE status = 1
    ORDER BY view_count DESC, created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取热门能源案例成功',
    data: {
      cases
    }
  });
}));

// 获取最新能源案例
router.get('/latest/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;

  const cases = await query(`
    SELECT 
      id,
      title,
      main_image,
      summary,
      case_type,
      created_at
    FROM energy_cases
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取最新能源案例成功',
    data: {
      cases
    }
  });
}));

// 获取首页精选案例
router.get('/homepage/featured', asyncHandler(async (req, res) => {
  const { limit = 4 } = req.query;

  const cases = await query(`
    SELECT
      id,
      title,
      main_image,
      summary,
      case_type,
      case_area,
      product_category,
      view_count,
      created_at
    FROM energy_cases
    WHERE status = 1 AND is_homepage_featured = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)}
  `);

  res.json({
    success: true,
    message: '获取首页精选案例成功',
    data: {
      cases
    }
  });
}));

// 获取案例统计信息
router.get('/stats/overview', asyncHandler(async (req, res) => {
  // 获取各类型案例数量
  const typeStats = await query(`
    SELECT
      case_type,
      COUNT(*) as count
    FROM energy_cases
    WHERE status = 1 AND case_type IS NOT NULL AND case_type != ''
    GROUP BY case_type
    ORDER BY count DESC
  `);

  // 获取各区域案例数量
  const areaStats = await query(`
    SELECT
      case_area,
      COUNT(*) as count
    FROM energy_cases
    WHERE status = 1 AND case_area IS NOT NULL AND case_area != ''
    GROUP BY case_area
    ORDER BY count DESC
  `);

  // 获取总案例数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM energy_cases
    WHERE status = 1
  `);

  res.json({
    success: true,
    message: '获取案例统计信息成功',
    data: {
      total: totalResult[0].total,
      type_stats: typeStats,
      area_stats: areaStats
    }
  });
}));

module.exports = router;
