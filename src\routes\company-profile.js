const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取公司简介（单条记录）
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const rows = await query('SELECT * FROM company_profile ORDER BY id DESC LIMIT 1');

  let profile = null;
  if (rows.length > 0) {
    profile = rows[0];
  } else {
    // 如果没有记录，创建一条默认记录
    const result = await query(
      'INSERT INTO company_profile (main_image, sub_image, summary, content, culture) VALUES (?, ?, ?, ?, ?)',
      ['', '', '请在此输入公司简介摘要...', '<p>请在此输入公司详细介绍内容...</p>', '<p>请在此输入公司文化内容...</p>']
    );

    const newRows = await query('SELECT * FROM company_profile WHERE id = ?', [result.insertId]);
    profile = newRows[0];
  }

  res.json({
    success: true,
    message: '获取公司简介成功',
    data: profile
  });
}));

// 更新公司简介
router.put('/', authenticateToken, asyncHandler(async (req, res) => {
  const { main_image, sub_image, summary, content, culture } = req.body;

  // 验证必填字段
  if (!summary || !content) {
    return res.status(400).json({
      success: false,
      message: '摘要和详情内容不能为空'
    });
  }

  // 检查是否已有记录
  const existingRows = await query('SELECT id FROM company_profile ORDER BY id DESC LIMIT 1');

  if (existingRows.length > 0) {
    // 更新现有记录
    await query(
      'UPDATE company_profile SET main_image = ?, sub_image = ?, summary = ?, content = ?, culture = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [main_image || '', sub_image || '', summary, content, culture || '', existingRows[0].id]
    );

    const updatedRows = await query('SELECT * FROM company_profile WHERE id = ?', [existingRows[0].id]);

    res.json({
      success: true,
      message: '更新公司简介成功',
      data: updatedRows[0]
    });
  } else {
    // 创建新记录
    const result = await query(
      'INSERT INTO company_profile (main_image, sub_image, summary, content, culture) VALUES (?, ?, ?, ?, ?)',
      [main_image || '', sub_image || '', summary, content, culture || '']
    );

    const newRows = await query('SELECT * FROM company_profile WHERE id = ?', [result.insertId]);

    res.status(201).json({
      success: true,
      message: '创建公司简介成功',
      data: newRows[0]
    });
  }
}));

module.exports = router;
