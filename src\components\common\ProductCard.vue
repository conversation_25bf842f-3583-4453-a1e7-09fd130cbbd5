<template>
  <div class="product-card" data-aos="fade-up" :data-aos-delay="delay" @click="handleClick">
    <div class="product-image">
      <img :src="product.image" :alt="product.name">
      <!-- hover遮罩和按钮 -->
      <div class="product-overlay">
        <button class="view-details-btn" @click.stop="handleViewDetails">
          查看详情
        </button>
      </div>
    </div>
    <div class="product-info">
      <h3>{{ product.name }}</h3>
      <p>{{ product.description }}</p>
      <div v-if="product.features" class="product-features">
        <span v-for="feature in product.features" :key="feature" class="feature-tag">
          {{ feature }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true,
      validator(value) {
        return value.name && value.description && value.image
      }
    },
    delay: {
      type: [String, Number],
      default: 0
    }
  },
  emits: ['click', 'view-details'],
  methods: {
    handleClick() {
      this.$emit('click', this.product)
    },
    handleViewDetails() {
      this.$emit('view-details', this.product)
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.product-card {
  cursor: pointer;
  transition: @transition;
  border-radius: @border-radius-large;
  overflow: hidden;
  background: @bg-color;
  box-shadow: @shadow-light;

  &:hover {
    transform: translateY(-5px);
    box-shadow: @shadow-hover;
  }
}

.product-image {
  position: relative;
  overflow: hidden;

  img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: @transition;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: @transition;

  .product-card:hover & {
    opacity: 1;
  }
}



.product-info {
  padding: @spacing-lg;

  h3 {
    margin: 0 0 @spacing-sm 0;
    font-size: @font-size-lg;
    font-weight: @font-weight-medium;
    color: @text-color;
    line-height: 1.4;
  }

  p {
    margin: 0 0 @spacing-md 0;
    color: @text-light;
    font-size: @font-size-sm;
    line-height: 1.5;
  }
}

.product-features {
  margin-top: @spacing-md;
  display: flex;
  flex-wrap: wrap;
  gap: @spacing-xs;
}

.feature-tag {
  display: inline-block;
  padding: @spacing-xs @spacing-sm;
  background: @bg-light;
  border: 1px solid @border-color;
  border-radius: @border-radius-small;
  font-size: @font-size-xs;
  color: @text-light;
  transition: @transition-fast;

  &:hover {
    background: @primary-light;
    border-color: @primary-color;
    color: @primary-color;
  }
}

// 移动端适配
@media (max-width: @screen-sm) {
  .product-card {
    &:hover {
      transform: none;
    }
  }

  .product-image {
    aspect-ratio: 16/9;
  }



  .product-info {
    padding: @spacing-md;

    h3 {
      font-size: @font-size-base;
    }
  }
}
</style>
