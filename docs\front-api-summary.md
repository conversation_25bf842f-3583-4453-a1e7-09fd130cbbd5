# 前台API接口总结

## 概述
为Airfit官网创建了一套独立的前台API接口，这些接口无需身份验证，专门为官网前台页面提供数据查询服务。

## 技术特点
- **无需认证**: 所有接口都不需要JWT token或其他身份验证
- **只读操作**: 只提供GET查询接口，不包含任何增删改操作
- **数据安全**: 只返回状态为"已发布"或"启用"的数据
- **RESTful设计**: 保持与管理后台API一致的设计风格
- **统一响应格式**: 所有接口返回格式保持一致

## API路由结构
```
/api/front/
├── health                    # 健康检查
├── products/                 # 产品相关
│   ├── categories           # 产品分类（树形结构）
│   ├── /                    # 产品列表
│   ├── hot/list             # 热门产品
│   └── latest/list          # 最新产品
├── banners/                 # 轮播图
│   ├── /                    # 轮播图列表
│   └── home/list            # 首页轮播图
├── energy-cases/            # 能源案例
│   ├── /                    # 案例列表
│   ├── hot/list             # 热门案例
│   ├── latest/list          # 最新案例
│   └── stats/overview       # 案例统计
├── news/                    # 新闻案例
│   ├── /                    # 新闻列表
│   ├── hot/list             # 热门新闻
│   ├── latest/list          # 最新新闻
│   └── featured/list        # 推荐新闻
├── dealers/                 # 经销商
│   ├── /                    # 经销商列表
│   ├── group/by-city        # 按城市分组
│   ├── group/by-province    # 按省份分组
│   ├── stats/distribution   # 分布统计
│   └── search/nearby        # 搜索附近经销商
├── resource-library/        # 资源库
│   ├── /                    # 资源列表
│   ├── hot/list             # 热门资源
│   └── latest/list          # 最新资源
├── video-tutorials/         # 视频教程
│   ├── /                    # 视频列表
│   ├── hot/list             # 热门视频
│   ├── latest/list          # 最新视频
│   └── stats/types          # 类型统计
├── heating-knowledge/       # 供暖知识
│   ├── /                    # 知识列表
│   ├── hot/list             # 热门知识
│   ├── latest/list          # 最新知识
│   └── featured/list        # 推荐知识
└── about-us/                # 关于我们
    ├── profile              # 公司简介
    ├── honors               # 公司荣誉
    ├── history              # 发展历程
    ├── contact              # 联系信息
    ├── qr-codes             # 二维码
    ├── complete             # 完整信息
    └── stats                # 统计信息
```

## 测试结果
使用测试脚本验证了主要API接口的可用性：

### ✅ 正常工作的接口
- 健康检查 `/health`
- 产品分类 `/products/categories`
- 产品列表 `/products`
- 轮播图列表 `/banners`
- 新闻列表 `/news`
- 经销商列表 `/dealers`
- 公司简介 `/about-us/profile`
- 公司荣誉 `/about-us/honors`
- 发展历程 `/about-us/history`

### ⚠️ 需要数据的接口
- 联系信息 `/about-us/contact` - 数据库中暂无数据

## 响应格式示例
```json
{
  "success": true,
  "message": "获取数据成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 100,
      "pages": 9
    }
  }
}
```

## 使用方法
```bash
# 获取产品列表
curl "http://localhost:3333/api/front/products"

# 获取轮播图
curl "http://localhost:3333/api/front/banners"

# 获取新闻列表
curl "http://localhost:3333/api/front/news"
```

## 注意事项
1. 所有接口都已在app.js中注册到 `/api/front` 路径下
2. 接口只返回已发布/启用状态的数据
3. 分页参数默认值：page=1, limit=12
4. 所有查询都包含基本的错误处理
5. 部分高级功能（如搜索、筛选）在当前版本中被简化，可根据需要后续完善

## 后续优化建议
1. 恢复动态查询参数支持（搜索、筛选、分页）
2. 添加缓存机制提高性能
3. 完善错误处理和日志记录
4. 添加API文档和Swagger支持
5. 考虑添加数据验证和限流机制
