import { request } from './http'

// 页面内容块管理API服务
export const pageContentBlocks = {
  // 获取内容块列表
  getList(params = {}) {
    return request.get('/api/page-content-blocks', params)
  },

  // 获取内容块详情
  getDetail(id) {
    return request.get(`/api/page-content-blocks/${id}`)
  },

  // 创建内容块
  create(data) {
    return request.post('/api/page-content-blocks', data)
  },

  // 更新内容块
  update(id, data) {
    return request.put(`/api/page-content-blocks/${id}`, data)
  },

  // 删除内容块
  delete(id) {
    return request.delete(`/api/page-content-blocks/${id}`)
  },

  // 批量更新状态
  batchUpdateStatus(ids, status) {
    return request.post('/api/page-content-blocks/batch-status', { ids, status })
  }
}

export default pageContentBlocks
