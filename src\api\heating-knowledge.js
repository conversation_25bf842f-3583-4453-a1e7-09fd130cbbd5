import { request } from './http'

export const heatingKnowledge = {
  // 获取采暖知识列表
  getList(params = {}) {
    return request.get('/api/heating-knowledge', params)
  },

  // 获取采暖知识详情
  getDetail(id) {
    return request.get(`/api/heating-knowledge/${id}`)
  },

  // 创建采暖知识
  create(data) {
    return request.post('/api/heating-knowledge', data)
  },

  // 更新采暖知识
  update(id, data) {
    return request.put(`/api/heating-knowledge/${id}`, data)
  },

  // 删除采暖知识
  delete(id) {
    return request.delete(`/api/heating-knowledge/${id}`)
  }
}

export default heatingKnowledge
