@import './variables.less';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: @font-family-base;
  line-height: 1.6;
  color: @text-color;
  background-color: @bg-color;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 容器样式
.container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @spacing-lg;
  
  @media (max-width: @screen-sm) {
    padding: 0 @spacing-md;
  }
}

// 文本对齐
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 按钮基础样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: @spacing-sm;
  padding: 12px 24px;
  border: none;
  border-radius: @border-radius;
  font-size: @font-size-base;
  font-weight: @font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: @transition;
  white-space: nowrap;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(216, 5, 20, 0.2);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-primary {
  background: @primary-color;
  color: white;
  
  &:hover:not(:disabled) {
    background: @primary-hover;
    transform: translateY(-2px);
    box-shadow: @shadow-hover;
    color: #fff;
  }
  
  &:active {
    transform: translateY(0);
  }
}

.btn-outline {
  background: transparent;
  color: @primary-color;
  border: 2px solid @primary-color;
  
  &:hover:not(:disabled) {
    background: @primary-color;
    color: white;
    transform: translateY(-2px);
    box-shadow: @shadow-hover;
  }
}

.btn-secondary {
  background: @secondary-color;
  color: white;
  
  &:hover:not(:disabled) {
    background: lighten(@secondary-color, 10%);
    transform: translateY(-2px);
    box-shadow: @shadow-hover;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-weight: @font-weight-bold;
  line-height: 1.2;
  margin-bottom: @spacing-md;
  color: @text-color;
}

h1 {
  font-size: @font-size-xxl;
  
  @media (max-width: @screen-sm) {
    font-size: 28px;
  }
}

h2 {
  font-size: @font-size-xl;
  
  @media (max-width: @screen-sm) {
    font-size: 22px;
  }
}

h3 {
  font-size: @font-size-lg;
  
  @media (max-width: @screen-sm) {
    font-size: @font-size-base;
  }
}

// 段落样式
p {
  margin-bottom: @spacing-md;
  line-height: 1.6;
  color: @text-light;
}

// 链接样式
a {
  color: @primary-color;
  text-decoration: none;
  transition: @transition;
  
  &:hover {
   opacity: 0.8;
  }
}

// 列表样式
ul, ol {
  margin-bottom: @spacing-md;
  padding-left: @spacing-lg;
}

li {
  margin-bottom: @spacing-xs;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// 表单元素样式
input, textarea, select {
  width: 100%;
  padding: 12px @spacing-md;
  border: 1px solid @border-color;
  border-radius: @border-radius;
  font-size: @font-size-base;
  font-family: @font-family-base;
  transition: @transition;
  
  &:focus {
    outline: none;
    border-color: @primary-color;
    box-shadow: 0 0 0 3px rgba(216, 5, 20, 0.1);
  }
  
  &::placeholder {
    color: @text-lighter;
  }
}

textarea {
  resize: vertical;
  min-height: 120px;
}

// 卡片样式
.card {
  background: white;
  border-radius: @border-radius;
  box-shadow: @shadow;
  overflow: hidden;
  transition: @transition;
  
  &:hover {
    box-shadow: @shadow-hover;
    transform: translateY(-2px);
  }
}

// 分割线
.divider {
  height: 1px;
  background: @border-color;
  margin: @spacing-xl 0;
}

// 加载动画
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid @border-color;
  border-radius: 50%;
  border-top-color: @primary-color;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 隐藏元素
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 响应式工具类
@media (max-width: @screen-sm) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: (@screen-sm + 1)) {
  .hidden-desktop {
    display: none !important;
  }
}
