<template>
  <div class="image-uploader-wrapper">
    <div 
      class="image-uploader"
      @click="handleClick"
      :class="{ 'has-image': value }"
    >
      <img v-if="value" :src="value" class="uploaded-image" alt="上传的图片">
      <div v-else class="upload-placeholder">
        <i class="el-icon-plus upload-icon"></i>
        <div v-if="text" class="upload-text">{{ text }}</div>
      </div>
      
      <!-- 删除按钮 -->
      <div v-if="value && !disabled" class="delete-btn" @click.stop="handleDelete">
        <i class="el-icon-delete"></i>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="uploading" class="upload-loading">
        <i class="el-icon-loading"></i>
        <div>上传中...</div>
      </div>
    </div>
    
    <!-- 提示文字 -->
    <div v-if="tip" class="upload-tip">{{ tip }}</div>
    
    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileChange"
    >
  </div>
</template>

<script>
import { upload } from '@/api'

export default {
  name: 'ImageUploader',
  props: {
    // 当前图片URL
    value: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 上传按钮文字
    text: {
      type: String,
      default: ''
    },
    // 提示文字
    tip: {
      type: String,
      default: ''
    },
    // 图片尺寸限制（宽x高）
    size: {
      type: String,
      default: '200x150'
    },
    // 文件大小限制（MB）
    maxSize: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      uploading: false
    }
  },
  computed: {
    uploadStyle() {
      const [width, height] = this.size.split('x').map(s => parseInt(s))
      return {
        width: `${width}px`,
        height: `${height}px`
      }
    }
  },
  methods: {
    handleClick() {
      if (this.disabled || this.uploading) return
      this.$refs.fileInput.click()
    },

    handleDelete() {
      this.$emit('input', '')
      this.$emit('change', '')
    },

    handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return

      // 重置文件输入框
      event.target.value = ''

      this.uploadFile(file)
    },

    async uploadFile(file) {
      // 文件类型验证
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件!')
        return
      }

      // 文件大小验证
      const fileSizeMB = file.size / 1024 / 1024
      if (fileSizeMB > this.maxSize) {
        this.$message.error(`图片大小不能超过 ${this.maxSize}MB!`)
        return
      }

      try {
        this.uploading = true
        
        // 使用封装的上传方法
        const response = await upload.uploadImage(file)
        
        if (response.success) {
          const imageUrl = response.data.url
          this.$emit('input', imageUrl)
          this.$emit('change', imageUrl)
          this.$emit('success', response.data)
          this.$message.success('图片上传成功')
        } else {
          this.$message.error(response.message || '图片上传失败')
        }
      } catch (error) {
        console.error('图片上传失败:', error)
        this.$message.error('图片上传失败，请重试')
        this.$emit('error', error)
      } finally {
        this.uploading = false
      }
    }
  }
}
</script>

<style scoped>
.image-uploader-wrapper {
  display: inline-block;
}

.image-uploader {
  position: relative;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409EFF;
}

.image-uploader.has-image {
  border-color: transparent;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-placeholder {
  text-align: center;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
  display: block;
}

.upload-text {
  font-size: 14px;
}

.delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-uploader:hover .delete-btn {
  opacity: 1;
}

.delete-btn:hover {
  background: rgba(255, 0, 0, 0.7);
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409EFF;
  font-size: 14px;
}

.upload-loading i {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
  text-align: center;
}
</style>
