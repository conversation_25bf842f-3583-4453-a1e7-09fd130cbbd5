const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取视频教程列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 12,
    type,
    keyword
  } = req.query;

  const offset = (page - 1) * limit;
  let whereConditions = ['status = 1']; // 只返回已发布的视频
  let params = [];

  // 类型筛选
  if (type) {
    whereConditions.push('type = ?');
    params.push(type);
  }

  // 关键词搜索
  if (keyword) {
    whereConditions.push('(title LIKE ? OR summary LIKE ? OR description LIKE ?)');
    params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
  }

  const whereClause = whereConditions.join(' AND ');

  // 查询视频列表
  let sql = `
    SELECT
      id,
      title,
      summary,
      description,
      video_code,
      type,
      tags,
      created_at
    FROM video_tutorials
    WHERE ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const videos = await query(sql, params);

  // 处理JSON字段
  const processedVideos = videos.map(video => {
    let tags = [];
    try {
      if (video.tags) {
        tags = typeof video.tags === 'string' ? JSON.parse(video.tags) : video.tags;
      }
    } catch (error) {
      console.error('JSON解析错误:', error, 'tags字段值:', video.tags);
      tags = [];
    }

    return {
      ...video,
      tags
    };
  });

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM video_tutorials
    WHERE ${whereClause}
  `, params);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取视频教程列表成功',
    data: {
      videos: processedVideos,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取视频教程详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 查询视频详情
  const videos = await query(`
    SELECT *
    FROM video_tutorials
    WHERE id = ? AND status = 1
  `, [id]);

  if (videos.length === 0) {
    return res.status(404).json({
      success: false,
      message: '视频教程不存在或已下架'
    });
  }

  const video = videos[0];

  // 增加观看次数
  await query('UPDATE video_tutorials SET view_count = view_count + 1 WHERE id = ?', [id]);
  video.view_count += 1;

  // 处理JSON字段
  try {
    video.tags = video.tags ? (typeof video.tags === 'string' ? JSON.parse(video.tags) : video.tags) : [];
  } catch (error) {
    console.error('JSON解析错误:', error, 'tags字段值:', video.tags);
    video.tags = [];
  }

  // 获取相关视频（同类型的其他视频）
  const relatedVideos = await query(`
    SELECT id, title, thumbnail, duration, view_count
    FROM video_tutorials 
    WHERE type = ? AND id != ? AND status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 4
  `, [video.type, id]);

  res.json({
    success: true,
    message: '获取视频教程详情成功',
    data: {
      video,
      related_videos: relatedVideos
    }
  });
}));

// 获取热门视频
router.get('/hot/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;

  const videos = await query(`
    SELECT
      id,
      title,
      summary,
      type,
      created_at
    FROM video_tutorials
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取热门视频成功',
    data: {
      videos
    }
  });
}));

// 获取最新视频
router.get('/latest/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;

  const videos = await query(`
    SELECT
      id,
      title,
      summary,
      type,
      created_at
    FROM video_tutorials
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取最新视频成功',
    data: {
      videos
    }
  });
}));

// 按类型获取视频
router.get('/type/:type', asyncHandler(async (req, res) => {
  const { type } = req.params;
  const { page = 1, limit = 12 } = req.query;
  const offset = (page - 1) * limit;

  const videos = await query(`
    SELECT 
      id,
      title,
      description,
      thumbnail,
      duration,
      view_count,
      created_at
    FROM video_tutorials
    WHERE status = 1 AND type = ?
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ? OFFSET ?
  `, [type, parseInt(limit), parseInt(offset)]);

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM video_tutorials
    WHERE status = 1 AND type = ?
  `, [type]);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: `获取"${type}"类型视频成功`,
    data: {
      videos,
      type,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取视频类型统计
router.get('/stats/types', asyncHandler(async (req, res) => {
  const typeStats = await query(`
    SELECT 
      type,
      COUNT(*) as count
    FROM video_tutorials
    WHERE status = 1
    GROUP BY type
    ORDER BY count DESC
  `);

  res.json({
    success: true,
    message: '获取视频类型统计成功',
    data: {
      type_stats: typeStats
    }
  });
}));

module.exports = router;
