<template>
  <div class="resource-library">
    <!-- 页面头部 -->
    <PageHeader tag="文档资料" />

    <!-- 资料展示 -->
    <section class="resource-showcase">
      <div class="container">
        <SectionHeader pageKey="resource" blockKey="resource_showcase" />

        <!-- 分类筛选 -->
        <div class="category-filter">
          <div class="filter-tabs">
            <button v-for="category in categories" :key="category.key"
              :class="['filter-tab', { active: activeCategory === category.key }]"
              @click="setActiveCategory(category.key)">
              {{ category.name }}
            </button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <button class="btn btn-primary" @click="fetchResources">重试</button>
          </div>
        </div>

        <!-- 资料列表 -->
        <div v-else>
          <!-- 无资料状态 -->
          <div v-if="resources.length === 0" class="no-resources">
            <div class="no-resources-content">
              <i class="fas fa-folder-open"></i>
              <h3>暂无资料</h3>
              <p>暂时没有可用的技术资料</p>
            </div>
          </div>

          <!-- 资料列表 -->
          <div v-else class="resources-grid">
            <div v-for="(resource, index) in resources" :key="resource.id" class="resource-card" data-aos="fade-up"
              :data-aos-delay="index * 100">
              <div class="resource-icon">
                <i class="fas fa-file"></i>
              </div>
              <div class="resource-content">
                <div class="resource-header">
                  <h3 class="resource-title">{{ resource.title }}</h3>
                  <span class="category-tag" :style="getCategoryStyle(resource.category)">
                    {{ getCategoryName(resource.category) }}
                  </span>
                </div>
                <p class="resource-summary">{{ resource.summary }}</p>
                <!-- <div class="resource-meta">
                  <span class="file-count">
                    <i class="fas fa-file"></i>
                    {{ resource.files ? resource.files.length : 0 }} 个文件
                  </span>
                  <span class="download-count">
                    <i class="fas fa-download"></i>
                    {{ resource.download_count || 0 }} 次下载
                  </span>
                </div> -->
                <div class="resource-actions">
                  <button class="btn btn-primary" @click="previewResource(resource)"
                    :disabled="!resource.files || resource.files.length === 0">
                    <i class="fas fa-download"></i>
                    预览下载
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <Pagination v-if="pagination.pages > 1" :current-page="pagination.page" :total-pages="pagination.pages"
            :total-items="pagination.total" @page-change="handlePageChange" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import SectionHeader from '@/components/common/SectionHeader.vue'
import Pagination from '@/components/common/Pagination.vue'
import api, { API } from '@/api/index.js'
import { isMobile } from '@/utils/deviceDetect'

export default {
  name: 'ResourceLibrary',
  components: {
    PageHeader,
    SectionHeader,
    Pagination
  },
  data() {
    return {
      resources: [],
      loading: false,
      error: null,
      activeCategory: '',
      categories: [], // 动态资料分类
      pagination: {
        page: 1,
        limit: 9,
        total: 0,
        pages: 0
      }
    }
  },
  mounted() {
    this.pagination.limit = isMobile() ? 9 : 9;
    this.fetchResourceCategories()
    this.fetchResources()
  },
  methods: {
    // 获取资料分类配置
    async fetchResourceCategories() {
      try {
        const response = await API.getSystemConfig('resource_categories')
        if (response.success) {
          // 添加"全部资料"选项
          this.categories = [
            { key: '', name: '全部资料' },
            ...(response.data || [])
          ]
        }
      } catch (error) {
        console.error('获取资料分类配置失败:', error)
      }
    },

    async fetchResources() {
      try {
        this.loading = true
        this.error = null

        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        // 添加分类筛选参数
        if (this.activeCategory) {
          params.category = this.activeCategory
        }

        const response = await api.get('/api/front/resource-library', { params })

        if (response.success) {
          this.resources = response.data.resources
          this.pagination = response.data.pagination
        } else {
          throw new Error(response.message || '获取数据失败')
        }

      } catch (error) {
        console.error('获取资料列表失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    handlePageChange(page) {
      this.pagination.page = page
      this.fetchResources()
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    setActiveCategory(category) {
      this.activeCategory = category
      this.pagination.page = 1
      this.fetchResources()
    },

    // 获取分类中文名称
    getCategoryName(categoryKey) {
      const category = this.categories.find(cat => cat.key === categoryKey);
      return category ? category.name : categoryKey;
    },

    // 根据分类key从颜色池获取样式
    getCategoryStyle(categoryKey) {
      // 资料分类颜色池（10个精心挑选的颜色）
      const colorPool = [
        '#1890ff', // 蓝色
        '#52c41a', // 绿色 
        '#fa541c', // 橙色 
        '#999999', // 灰色 
        '#eb2f96', // 粉色 
        '#13c2c2', // 青色
        '#faad14', // 金色
        '#f5222d', // 红色
        '#2f54eb', // 深蓝
        '#a0d911'  // 浅绿
      ];

      const index = this.categories.findIndex(cat => cat.key === categoryKey);

      return {
        backgroundColor: colorPool[index],
        color: '#fff',
        border: 'none'
      };
    },

    async downloadResource(resource) {
      try {
        if (!resource.files || resource.files.length === 0) {
          this.$message.warning('该资料暂无可下载文件')
          return
        }

        // 记录下载
        await api.post(`/api/front/resource-library/${resource.id}/download`)

        // 下载文件
        if (resource.files.length === 1) {
          // 单个文件直接下载
          const file = resource.files[0]
          this.downloadFile(file.url, file.name)
        } else {
          // 多个文件，提示用户选择或打包下载
          this.showFileSelection(resource)
        }

        // 更新下载次数
        resource.download_count = (resource.download_count || 0) + 1

      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },

    downloadFile(url, filename, index) {
      // 使用FileSaver.js下载文件
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    showFileSelection(resource) {
      if (resource.files && resource.files.length > 0) {
        resource.files.forEach((file, index) => {
          this.downloadFile(file.url, file.name, index)
        })
      }
    },

    previewResource(resource) {
      window.open(resource.files[0].url, '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.resource-library {
  min-height: 100vh;
}

.resource-showcase {
  padding: 66px 0;
  background: @bg-light;
}

.container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @spacing-lg;
}

// 加载和错误状态
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-spinner,
.error-content {
  text-align: center;

  i {
    font-size: 48px;
    color: @primary-color;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
    margin-bottom: @spacing-lg;
  }
}

// 分类筛选样式
.category-filter {
  margin: @spacing-xl 0;
  display: flex;
  justify-content: center;

  .filter-tabs {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: center;

    .filter-tab {
      padding: 12px 30px;
      border: 2px solid @border-color;
      border-radius: 50px;
      background: @bg-color;
      color: @text-color;
      font-size: @font-size-base;
      cursor: pointer;
      transition: @transition;
      white-space: nowrap;

      &.active {
        border-color: @primary-color;
        color: @primary-color;
      }

      &:hover {
        border-color: @primary-color;
        color: @primary-color;
      }
    }
  }

  // 响应式
  @media (max-width: 768px) {
    margin: @spacing-lg 0;

    .filter-tabs {
      gap: 16px;
      justify-content: flex-start;

      .filter-tab {
        padding: 10px 20px;
        font-size: @font-size-sm;
      }
    }
  }
}

// 无资料状态
.no-resources {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.no-resources-content {
  text-align: center;

  i {
    font-size: 64px;
    color: @text-lighter;
    margin-bottom: @spacing-lg;
  }

  h3 {
    margin-bottom: @spacing-md;
    color: @text-color;
  }

  p {
    color: @text-light;
  }
}

// 资料网格
.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: @spacing-xl;
  margin-bottom: @spacing-xxl;
}

// 资料卡片
.resource-card {
  background: @bg-color;
  border-radius: @border-radius-large;
  box-shadow: @shadow;
  padding: @spacing-lg;
  transition: @transition;
  display: flex;
  align-items: flex-start;
  gap: @spacing-lg;

  &:hover {
    box-shadow: @shadow-hover;
    transform: translateY(-5px);
  }
}

.resource-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background: @primary-light;
  border-radius: @border-radius;
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    font-size: 24px;
    color: @primary-color;
  }
}

.resource-content {
  flex: 1;
  width: 100%;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @spacing-sm;
  gap: @spacing-sm;
}

.resource-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-medium;
  color: @text-color;
  line-height: 1.4;
  flex: 1;
  margin: 0;
  text-align: left;
}

.category-tag {
  padding: @spacing-xs @spacing-sm;
  border-radius: @border-radius-small;
  font-size: @font-size-xs;
  font-weight: @font-weight-medium;
  white-space: nowrap;
  flex-shrink: 0;

  &.manual {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
  }

  &.error-code {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
  }

  &.other {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
  }
}

.resource-summary {
  color: @text-light;
  line-height: 1.6;
  margin-bottom: @spacing-md;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 14px;
  text-align: left;
}

.resource-meta {
  display: flex;
  gap: @spacing-lg;
  margin-bottom: @spacing-lg;

  span {
    display: flex;
    align-items: center;
    gap: @spacing-xs;
    font-size: @font-size-sm;
    color: @text-lighter;

    i {
      font-size: 12px;
    }
  }
}

.resource-actions {
  .btn {
    padding: @spacing-sm @spacing-lg;
    font-size: @font-size-sm;

    i {
      margin-right: @spacing-xs;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

@media (max-width: @screen-sm) {
  .container {
    padding: 0 @spacing-md;
  }

  .resource-showcase {
    padding: 40px 0;
  }

  .resource-meta {
    flex-direction: row;
    gap: @spacing-sm;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: @screen-md) {
  .resources-grid {
    grid-template-columns: 1fr;
    gap: @spacing-lg;
  }

  .resource-card {
    padding: @spacing-lg;
    flex-direction: column;
    text-align: center;
  }

  .resource-icon {
    align-self: center;
  }
}
</style>
