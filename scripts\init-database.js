const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 数据库连接配置
const connectionConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  charset: 'utf8mb4'
};

const dbName = process.env.DB_NAME || 'airfit';

async function initDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始初始化数据库...');
    
    // 连接MySQL服务器
    connection = await mysql.createConnection(connectionConfig);
    console.log('✅ 连接MySQL服务器成功');
    
    // 创建数据库
    await connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 ${dbName} 创建成功`);
    
    // 重新连接到指定数据库
    await connection.end();
    connection = await mysql.createConnection({
      ...connectionConfig,
      database: dbName
    });
    
    // 创建管理员表
    const createAdminsTable = `
      CREATE TABLE IF NOT EXISTS admins (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码(bcrypt加密)',
        email VARCHAR(100) COMMENT '邮箱',
        real_name VARCHAR(50) COMMENT '真实姓名',
        status TINYINT DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
        last_login_time DATETIME COMMENT '最后登录时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表'
    `;
    
    await connection.query(createAdminsTable);
    console.log('✅ 管理员表创建成功');
    
    // 创建菜单表
    const createMenusTable = `
      CREATE TABLE IF NOT EXISTS menus (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(50) NOT NULL COMMENT '菜单名称',
        path VARCHAR(100) NOT NULL COMMENT '路由路径',
        url VARCHAR(200) COMMENT '页面URL路径',
        parent_id INT DEFAULT 0 COMMENT '父级菜单ID, 0表示顶级菜单',
        sort_order INT DEFAULT 0 COMMENT '排序序号',
        status TINYINT DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
        description TEXT COMMENT '菜单描述',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_parent_id (parent_id),
        INDEX idx_sort_order (sort_order),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表'
    `;
    
    await connection.query(createMenusTable);
    console.log('✅ 菜单表创建成功');
    
    // 检查是否已存在管理员账号
    const [existingAdmin] = await connection.execute('SELECT id FROM admins WHERE username = ?', ['admin']);
    
    if (existingAdmin.length === 0) {
      // 创建默认管理员账号
      const hashedPassword = await bcrypt.hash('airfit2025', parseInt(process.env.BCRYPT_ROUNDS) || 12);
      
      await connection.execute(
        'INSERT INTO admins (username, password, email, real_name, status) VALUES (?, ?, ?, ?, ?)',
        ['admin', hashedPassword, '<EMAIL>', '系统管理员', 1]
      );
      console.log('✅ 默认管理员账号创建成功 (用户名: admin, 密码: airfit2025)');
    } else {
      console.log('ℹ️  管理员账号已存在，跳过创建');
    }
    
    // 检查是否已存在菜单数据
    const [existingMenus] = await connection.execute('SELECT id FROM menus LIMIT 1');

    if (existingMenus.length === 0) {
      // 插入默认菜单数据
      const defaultMenus = [
        { name: '首页', path: '/', url: '/', parent_id: 0, sort_order: 1 },
        { name: '产品中心', path: '/products', url: '/products', parent_id: 0, sort_order: 2 },
        { name: '壁挂炉系列', path: '/products#boilers', url: '/products#boilers', parent_id: 2, sort_order: 1 },
        { name: '空气能热泵', path: '/products#heatpumps', url: '/products#heatpumps', parent_id: 2, sort_order: 2 },
        { name: '散热器系列', path: '/products#radiators', url: '/products#radiators', parent_id: 2, sort_order: 3 },
        { name: '控制系统', path: '/products#controls', url: '/products#controls', parent_id: 2, sort_order: 4 },
        { name: '采暖学堂', path: '/academy', url: '/academy', parent_id: 0, sort_order: 3 },
        { name: '工程案例', path: '/cases', url: '/cases', parent_id: 0, sort_order: 4 },
        { name: '门店查询', path: '/stores', url: '/stores', parent_id: 0, sort_order: 5 },
        { name: '关于我们', path: '/about', url: '/about', parent_id: 0, sort_order: 6 }
      ];

      for (const menu of defaultMenus) {
        await connection.execute(
          'INSERT INTO menus (name, path, url, parent_id, sort_order, status) VALUES (?, ?, ?, ?, ?, ?)',
          [menu.name, menu.path, menu.url, menu.parent_id, menu.sort_order, 1]
        );
      }
      console.log('✅ 默认菜单数据插入成功');
    } else {
      console.log('ℹ️  菜单数据已存在，跳过插入');
    }

    console.log('🎉 数据库初始化完成！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 执行初始化
initDatabase();
