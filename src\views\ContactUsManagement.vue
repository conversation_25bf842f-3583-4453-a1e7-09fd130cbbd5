<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>联系我们</h2>
        <el-button type="primary" icon="el-icon-edit" @click="handleEdit" :loading="loading">
          编辑内容
        </el-button>
      </div>

      <!-- 联系我们内容展示 -->
      <div v-if="!isEditing" class="contact-display">
        <div class="content-section">
          <div class="title-section">
            <h3>标题</h3>
            <p>{{ contactData.title || '暂无标题' }}</p>
          </div>

          <div class="summary-section">
            <h3>摘要</h3>
            <p>{{ contactData.summary || '暂无摘要' }}</p>
          </div>

          <div class="detail-section">
            <h3>详细内容</h3>
            <div class="content" v-html="contactData.content || '暂无详细内容'"></div>
          </div>
        </div>
      </div>

      <!-- 编辑表单 -->
      <div v-if="isEditing" class="edit-form">
        <el-form ref="contactForm" :model="editForm" :rules="formRules" label-width="120px">
          <el-form-item label="标题" prop="title">
            <el-input v-model="editForm.title" placeholder="请输入标题" />
          </el-form-item>

          <el-form-item label="摘要" prop="summary">
            <el-input v-model="editForm.summary" type="textarea" :rows="3" placeholder="请输入摘要" />
          </el-form-item>

          <el-form-item label="详细内容" prop="content">
            <RichTextEditor v-model="editForm.content" placeholder="请输入详细内容" height="400px" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
            <el-button @click="handleCancel">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { contactUs } from '@/api'
import RichTextEditor from '@/components/RichTextEditor.vue'

export default {
  name: 'ContactUsManagement',
  components: {
    RichTextEditor
  },
  data() {
    return {
      loading: false,
      saving: false,
      isEditing: false,
      contactData: {},
      editForm: {
        title: '',
        summary: '',
        content: ''
      },
      formRules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    await this.fetchContactData()
  },
  methods: {
    // 获取联系我们数据
    async fetchContactData() {
      this.loading = true
      try {
        const response = await contactUs.get()
        if (response.success && response.data.contact) {
          this.contactData = response.data.contact
        }
      } catch (error) {
        console.error('获取联系我们数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 进入编辑模式
    handleEdit() {
      this.isEditing = true
      this.editForm = {
        title: this.contactData.title || '',
        summary: this.contactData.summary || '',
        content: this.contactData.content || ''
      }
    },

    // 保存数据
    async handleSave() {
      this.$refs.contactForm.validate(async (valid) => {
        if (!valid) return

        this.saving = true
        try {
          const response = await contactUs.createOrUpdate(this.editForm)

          if (response.success) {
            this.$message.success('保存成功')
            this.isEditing = false
            await this.fetchContactData()
          } else {
            throw new Error(response.message || '保存失败')
          }
        } catch (error) {
          console.error('保存失败:', error)
          this.$message.error(error.response?.data?.message || error.message || '保存失败')
        } finally {
          this.saving = false
        }
      })
    },

    // 取消编辑
    handleCancel() {
      this.isEditing = false
      this.editForm = {
        title: '',
        summary: '',
        content: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;

  h2 {
    margin: 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
  }
}

.contact-display {
  .content-section {

    .title-section,
    .summary-section,
    .detail-section {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #606266;
        line-height: 1.6;
      }

      .content {
        color: #606266;
        line-height: 1.8;

        h3 {
          color: #E6A23C;
          border-bottom: 2px solid #E6A23C;
          padding-bottom: 5px;
        }

        p {
          margin-bottom: 10px;
        }
      }
    }
  }
}

.edit-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}
</style>
