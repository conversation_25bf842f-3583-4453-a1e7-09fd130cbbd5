<template>
  <Modal
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    @close="$emit('close')"
    :title="title"
    :width="width"
    :mask-closable="maskClosable"
  >
    <div class="qr-download-content">
      <!-- 二维码区域 -->
      <div class="qr-code-section">
        <slot name="qr-code">
          <div class="qr-code-placeholder">
            <img v-if="qrCodeUrl" :src="qrCodeUrl" :alt="qrCodeAlt" class="qr-code-image">
            <div v-else class="qr-code-default">
              <i class="fas fa-qrcode"></i>
              <p>请提供二维码图片</p>
            </div>
          </div>
        </slot>
      </div>

      <!-- 下载信息 -->
      <div class="download-info">
        <slot name="download-info">
          <h4 class="download-title">{{ downloadTitle }}</h4>
          <p class="download-description">{{ downloadDescription }}</p>
          
          <!-- 下载方式 -->
          <div class="download-methods" v-if="showDownloadMethods">
            <div class="method-item" v-for="method in downloadMethods" :key="method.type">
              <i :class="method.icon"></i>
              <span>{{ method.text }}</span>
            </div>
          </div>
        </slot>
      </div>

      <!-- 额外内容插槽 -->
      <div class="extra-content" v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer v-if="showFooter">
      <slot name="footer">
        <button class="btn btn-outline" @click="$emit('close')">
          取消
        </button>
        <button class="btn btn-primary" @click="handleDownload" v-if="showDownloadButton">
          {{ downloadButtonText }}
        </button>
      </slot>
    </template>
  </Modal>
</template>

<script>
import Modal from './Modal.vue'

export default {
  name: 'QRCodeDownload',
  components: {
    Modal
  },
  props: {
    // 控制显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '下载APP'
    },
    // 弹窗宽度
    width: {
      type: String,
      default: '400px'
    },
    // 点击遮罩是否关闭
    maskClosable: {
      type: Boolean,
      default: true
    },
    // 二维码图片URL
    qrCodeUrl: {
      type: String,
      default: ''
    },
    // 二维码图片alt文本
    qrCodeAlt: {
      type: String,
      default: 'APP下载二维码'
    },
    // 下载标题
    downloadTitle: {
      type: String,
      default: '扫码下载APP'
    },
    // 下载描述
    downloadDescription: {
      type: String,
      default: '使用手机扫描二维码，快速下载安装APP'
    },
    // 是否显示下载方式
    showDownloadMethods: {
      type: Boolean,
      default: true
    },
    // 下载方式列表
    downloadMethods: {
      type: Array,
      default: () => [
        { type: 'android', icon: 'fab fa-android', text: 'Android版' },
        { type: 'ios', icon: 'fab fa-apple', text: 'iOS版' }
      ]
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: false
    },
    // 是否显示下载按钮
    showDownloadButton: {
      type: Boolean,
      default: false
    },
    // 下载按钮文本
    downloadButtonText: {
      type: String,
      default: '直接下载'
    },
    // 下载链接
    downloadUrl: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible', 'close', 'download'],
  methods: {
    handleDownload() {
      if (this.downloadUrl) {
        window.open(this.downloadUrl, '_blank')
      }
      this.$emit('download')
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.qr-download-content {
  text-align: center;
}

.qr-code-section {
  margin-bottom: @spacing-xl;

  .qr-code-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .qr-code-image {
    width: 200px;
    height: 200px;
    border: 1px solid @border-color;
    border-radius: @border-radius;
    object-fit: contain;
    background: @bg-light;
  }

  .qr-code-default {
    width: 200px;
    height: 200px;
    border: 2px dashed @border-color;
    border-radius: @border-radius;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: @bg-light;
    color: @text-lighter;

    i {
      font-size: 48px;
      margin-bottom: @spacing-md;
    }

    p {
      margin: 0;
      font-size: @font-size-sm;
    }
  }
}

.download-info {
  .download-title {
    margin: 0 0 @spacing-md 0;
    font-size: @font-size-lg;
    font-weight: @font-weight-medium;
    color: @text-color;
  }

  .download-description {
    margin: 0 0 @spacing-lg 0;
    color: @text-light;
    font-size: @font-size-sm;
    line-height: 1.5;
  }
}

.download-methods {
  display: flex;
  justify-content: center;
  gap: @spacing-xl;
  margin-top: @spacing-lg;

  .method-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: @spacing-xs;
    color: @text-light;
    font-size: @font-size-sm;

    i {
      font-size: @spacing-xl;
      color: @primary-color;
    }
  }
}

.extra-content {
  margin-top: @spacing-xl;
  padding-top: @spacing-lg;
  border-top: 1px solid @border-color;
}

// 按钮样式
.btn {
  padding: @spacing-sm @spacing-lg;
  border-radius: @border-radius;
  font-size: @font-size-sm;
  font-weight: @font-weight-medium;
  cursor: pointer;
  transition: @transition;
  border: 1px solid transparent;
  text-decoration: none;
  display: inline-block;
  text-align: center;

  &.btn-primary {
    background: @primary-color;
    color: white;
    border-color: @primary-color;

    &:hover {
      background: @primary-hover;
      border-color: @primary-hover;
    }
  }

  &.btn-outline {
    background: transparent;
    color: @text-light;
    border-color: @border-color;

    &:hover {
      background: @bg-light;
      border-color: @text-light;
    }
  }
}

// 移动端适配
@media (max-width: @screen-sm) {
  .qr-code-image,
  .qr-code-default {
    width: 160px;
    height: 160px;
  }

  .download-methods {
    gap: @spacing-lg;
  }
}
</style>