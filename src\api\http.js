import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'

// 创建axios实例
const http = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? 'http://**************:3333'
    : 'http://localhost:3333',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 添加认证token
    const token = store.getters.token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API请求错误:', error)

    // 处理认证错误
    if (error.response && error.response.status === 401) {
      Message.error('登录已过期，请重新登录')
      store.dispatch('logout')
      // 跳转到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    } else if (error.response && error.response.data && error.response.data.message) {
      Message.error(error.response.data.message)
    } else {
      Message.error('网络错误，请稍后重试')
    }

    return Promise.reject(error)
  }
)

// 封装常用的HTTP方法
export const request = {
  get(url, params = {}) {
    return http.get(url, { params })
  },

  post(url, data = {}, config = {}) {
    return http.post(url, data, config)
  },

  put(url, data = {}, config = {}) {
    return http.put(url, data, config)
  },

  patch(url, data = {}, config = {}) {
    return http.patch(url, data, config)
  },

  delete(url, config = {}) {
    return http.delete(url, config)
  }
}

// 导出axios实例（用于特殊需求）
export default http
