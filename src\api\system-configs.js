import { request } from './http'

// 系统配置管理API服务
export const systemConfigs = {
  // 获取配置列表
  getList(params = {}) {
    return request.get('/api/system-configs', params)
  },

  // 获取配置详情
  getDetail(id) {
    return request.get(`/api/system-configs/${id}`)
  },

  // 根据配置键获取配置（前端使用）
  getByKey(configKey) {
    return request.get(`/api/system-configs/key/${configKey}`)
  },

  // 创建配置
  create(data) {
    return request.post('/api/system-configs', data)
  },

  // 更新配置
  update(id, data) {
    return request.put(`/api/system-configs/${id}`, data)
  },

  // 删除配置
  delete(id) {
    return request.delete(`/api/system-configs/${id}`)
  }
}

export default systemConfigs
