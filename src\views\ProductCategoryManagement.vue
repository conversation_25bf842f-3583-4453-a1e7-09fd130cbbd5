<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>产品分类管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加分类
        </el-button>
      </div>
      
      <!-- 分类表格 -->
      <el-table
        v-loading="loading"
        :data="categoryList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border
        class="category-table"
      >
        <el-table-column prop="name" label="分类名称" min-width="200">
          <template slot-scope="scope">
            <span class="category-name">{{ scope.row.name }}</span>
            <el-tag v-if="scope.row.level" size="mini" type="info" style="margin-left: 8px;">
              {{ scope.row.level }}级
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sort_order" label="排序" width="80" align="center" />
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="260" align="center">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              type="primary" 
              @click="handleAddChild(scope.row)"
              v-if="scope.row.level < 4"
            >
              添加子分类
            </el-button>
            <el-button size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              size="mini" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="categoryForm"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        
        <el-form-item label="父级分类" v-if="!isEdit">
          <el-select v-model="categoryForm.parent_id" placeholder="请选择父级分类" style="width: 100%;">
            <el-option label="顶级分类" :value="0" />
            <el-option
              v-for="category in parentCategoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
              :disabled="category.level >= 3"
            />
          </el-select>
          <div class="form-tip">最多支持4级分类</div>
        </el-form-item>

        <el-form-item label="父级分类" v-else>
          <el-input :value="getParentCategoryName()" disabled />
          <div class="form-tip">编辑时不允许修改层级关系</div>
        </el-form-item>
        
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="categoryForm.sort_order" :min="0" :max="999" />
        </el-form-item>
        
        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api';

export default {
  name: 'ProductCategoryManagement',
  data() {
    return {
      loading: false,
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      editId: null,
      categoryList: [],
      categoryForm: {
        name: '',
        parent_id: 0,
        sort_order: 0,
        description: ''
      },
      categoryRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 100, message: '分类名称长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑分类' : '添加分类';
    },
    
    // 父级分类选项（用于下拉选择）
    parentCategoryOptions() {
      const flatten = (categories, level = 1) => {
        let result = [];
        categories.forEach(category => {
          result.push({ ...category, level });
          if (category.children && category.children.length > 0) {
            result = result.concat(flatten(category.children, level + 1));
          }
        });
        return result;
      };
      
      return flatten(this.categoryList).filter(cat => {
        // 编辑时排除自己和自己的子分类
        if (this.isEdit && this.editId) {
          return cat.id !== this.editId && !this.isDescendant(cat.id, this.editId);
        }
        return true;
      });
    }
  },
  
  created() {
    this.loadCategoryList();
  },
  
  methods: {
    async loadCategoryList() {
      this.loading = true;
      try {
        const response = await api.productCategory.getList();
        if (response.success) {
          this.categoryList = response.data.categories;
        }
      } catch (error) {
        this.$message.error('获取分类列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    handleAdd() {
      this.isEdit = false;
      this.editId = null;
      this.dialogVisible = true;
    },
    
    handleAddChild(parentCategory) {
      this.isEdit = false;
      this.editId = null;
      this.categoryForm.parent_id = parentCategory.id;
      this.dialogVisible = true;
    },
    
    handleEdit(category) {
      this.isEdit = true;
      this.editId = category.id;
      this.categoryForm = {
        name: category.name,
        sort_order: category.sort_order,
        description: category.description || ''
      };
      // 编辑时不包含parent_id，因为不允许修改层级关系
      this.dialogVisible = true;
    },
    
    async handleDelete(category) {
      try {
        await this.$confirm(`确定要删除分类"${category.name}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        await api.productCategory.delete(category.id);
        this.$message.success('删除分类成功');
        this.loadCategoryList();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除分类失败');
        }
      }
    },
    
    async handleSubmit() {
      try {
        // 表单验证
        await this.$refs.categoryForm.validate();

        this.submitLoading = true;

        if (this.isEdit) {
          await api.productCategory.update(this.editId, this.categoryForm);
          this.$message.success('更新分类成功');
        } else {
          await api.productCategory.create(this.categoryForm);
          this.$message.success('创建分类成功');
        }

        this.dialogVisible = false;
        this.loadCategoryList();
      } catch (error) {
        console.error('分类操作失败:', error);

        // 优化错误处理，避免重复显示错误
        let errorMessage = '操作失败';

        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.message && !error.message.includes('validation')) {
          errorMessage = error.message;
        }

        this.$message.error(errorMessage);
      } finally {
        this.submitLoading = false;
      }
    },
    
    resetForm() {
      this.categoryForm = {
        name: '',
        parent_id: 0,
        sort_order: 0,
        description: ''
      };
      this.$refs.categoryForm && this.$refs.categoryForm.clearValidate();
    },
    
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleString();
    },

    getParentCategoryName() {
      if (!this.isEdit || !this.editId) return '';

      // 找到当前编辑的分类
      const currentCategory = this.findCategoryById(this.editId);
      if (!currentCategory) return '';

      if (currentCategory.parent_id === 0) {
        return '顶级分类';
      }

      // 找到父级分类
      const parentCategory = this.findCategoryById(currentCategory.parent_id);
      return parentCategory ? parentCategory.name : '未知分类';
    },

    findCategoryById(id) {
      const findInCategories = (categories) => {
        for (const category of categories) {
          if (category.id === id) {
            return category;
          }
          if (category.children && category.children.length > 0) {
            const found = findInCategories(category.children);
            if (found) return found;
          }
        }
        return null;
      };

      return findInCategories(this.categoryList);
    },
    
    // 检查是否为某个分类的后代
    isDescendant(categoryId, ancestorId) {
      const findCategory = (categories, id) => {
        for (const category of categories) {
          if (category.id === id) {
            return category;
          }
          if (category.children) {
            const found = findCategory(category.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      
      const category = findCategory(this.categoryList, categoryId);
      if (!category) return false;
      
      let current = category;
      while (current.parent_id !== 0) {
        if (current.parent_id === ancestorId) {
          return true;
        }
        current = findCategory(this.categoryList, current.parent_id);
        if (!current) break;
      }
      
      return false;
    }
  }
};
</script>

<style lang="less" scoped>
.category-table {
  .category-name {
    font-weight: 500;
  }
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
