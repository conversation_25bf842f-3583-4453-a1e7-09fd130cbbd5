<template>
  <div class="product-detail">


    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="fetchProductDetail">重试</button>
      </div>
    </div>

    <!-- 产品详情内容 -->
    <div v-else-if="product" class="detail-content">
      <div class="container">
        <!-- 产品主要信息 -->
        <div class="product-main">
          <div class="product-gallery">
            <div class="main-image">
              <img v-if="product.main_image" :src="product.main_image" :alt="product.name" />
              <div v-else class="no-image">
                <i class="fas fa-image"></i>
                <p>暂无图片</p>
              </div>
            </div>
          </div>

          <div class="product-info">
            <h1 class="product-title">{{ product.name }}</h1>

            <!-- <div class="product-meta">
              <div class="meta-item">
                <span class="label">产品分类：</span>
                <span class="value">{{ product.category_name }}</span>
              </div>
              <div class="meta-item">
                <span class="label">浏览次数：</span>
                <span class="value">{{ product.view_count }} 次</span>
              </div>
              <div class="meta-item">
                <span class="label">发布时间：</span>
                <span class="value">{{ formatDate(product.created_at) }}</span>
              </div>
            </div> -->

            <div v-if="product.summary" class="product-summary">
              <h3>产品概述</h3>
              <p v-html="product.summary"></p>
            </div>

            <!-- <div class="product-actions">
              <a class="btn btn-primary" href="tel:4008-206-306">
                <i class="fas fa-phone"></i>
                咨询产品
              </a>
              <button class="btn btn-outline" @click="handleShare">
                <i class="fas fa-share-alt"></i>
                分享产品
              </button>
            </div> -->
          </div>
        </div>

        <!-- 产品详细描述 -->
        <div class="product-description">
          <h2>产品详情</h2>
          <div v-if="product.description" class="rich-content" v-html="product.description"></div>
          <div v-else class="no-content">
            <p>暂无详细内容</p>
          </div>
        </div>

        <!-- 相关产品 -->
        <!-- <div v-if="relatedProducts && relatedProducts.length > 0" class="related-products">
          <h2>相关产品</h2>
          <div class="related-products-grid">
            <ProductGridItem v-for="relatedProduct in relatedProducts" :key="relatedProduct.id"
              :product="relatedProduct" @view-detail="goToProduct" />
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'
import ProductGridItem from '@/components/product/ProductGridItem.vue'
import api from '@/api'
import formatDate from '@/utils/dateFormat'

export default {
  name: 'ProductDetail',
  components: {
    PageHeader,
    Breadcrumb,
    ProductGridItem
  },
  data() {
    return {
      loading: true,
      error: null,
      product: null,
      relatedProducts: []
    }
  },
  computed: {
    breadcrumbItems() {
      const items = [
        { name: '产品中心', path: '/products' }
      ]
      if (this.product) {
        items.push({ name: this.product.name })
      }
      return items
    }
  },
  created() {
    this.fetchProductDetail()
  },
  watch: {
    '$route'() {
      this.fetchProductDetail()
    }
  },
  methods: {
    async fetchProductDetail() {
      try {
        this.loading = true
        this.error = null

        const productId = this.$route.params.id

        // 获取产品详情
        const response = await api.get(`/api/front/products/${productId}`)

        if (response.success) {
          this.product = response.data.product
          this.relatedProducts = response.data.related_products || []

          // 更新页面标题
          if (this.product.name) {
            document.title = `${this.product.name} - 产品详情 - 雅克菲采暖`
          }
        } else {
          this.error = response.message || '获取产品详情失败'
        }
      } catch (error) {
        console.error('获取产品详情失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },



    handleShare() {
      // 分享产品功能
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href).then(() => {
        alert('链接已复制到剪贴板')
      })
    },

    goToProduct(product) {
      this.$router.push(`/products/${product.id}`)
    },

    formatDate
  }
}
</script>


<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.product-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

.detail-content {
  padding: 40px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

.product-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 30px;
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-gallery {
  .main-image {
    width: 100%;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .no-image {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: @bg-light;
    }
  }
}

.product-info {
  .product-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 30px 0;
    line-height: 1.3;
  }

  .product-meta {
    margin-bottom: 30px;

    .meta-item {
      display: flex;
      margin-bottom: 12px;

      .label {
        font-weight: 500;
        color: #666;
        min-width: 100px;
      }

      .value {
        color: #333;
      }
    }
  }

  .product-summary {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 15px 0;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }

  .product-actions {
    display: flex;
    gap: 15px;

    .btn {
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;

      &.btn-primary {
        background: #D80514;
        color: white;
        border: none;

        &:hover {
          background: #b8040f;
        }
      }

      &.btn-outline {
        background: transparent;
        color: #D80514;
        border: 2px solid #D80514;

        &:hover {
          background: #D80514;
          color: white;
        }
      }
    }
  }
}

.product-description {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // margin-bottom: 60px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 30px 0;
    padding-bottom: 15px;
    border-bottom: 2px solid #D80514;
  }

  .rich-content {
    color: #666;
    line-height: 1.8;



    p {
      margin-bottom: 15px;
    }


    img {
      width: 100%;
      height: auto;
      border-radius: 6px;
      margin: 20px 0;
    }
  }

  .no-content {
    text-align: center;
    padding: 60px 0;
    color: #999;
  }
}

.related-products {
  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 30px 0;
    text-align: center;
  }

  .related-products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;

    // 平板端：2列
    @media (max-width: 1024px) and (min-width: 769px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 25px;
    }

    // 移动端：1列
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }


}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .loading-spinner,
  .error-content {
    text-align: center;

    i {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 16px 0 8px;
      color: #333;
    }

    p {
      color: #666;
      margin-bottom: 16px;
    }

    .btn {
      background: #D80514;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #b8040f;
      }
    }
  }
}

// 移动端响应式样式
@media (max-width: 768px) {
  .detail-content {
    padding: 20px 0;

    .container {
      padding: 0 15px;
    }
  }

  .product-main {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 20px;

    .product-gallery .main-image {
      height: 250px;
    }

    .product-info {
      .product-title {
        font-size: 22px;
      }

      .product-actions {
        flex-direction: column;

        .btn {
          justify-content: center;
        }
      }
    }
  }

  .product-description {
    padding: 20px;

    h2 {
      font-size: 20px;
    }
  }


}
</style>
