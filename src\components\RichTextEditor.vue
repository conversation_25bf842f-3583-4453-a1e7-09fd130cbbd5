<template>
  <div class="rich-text-editor">
    <div :id="editorId" :style="{ height: height }"></div>
  </div>
</template>

<script>
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import api from '@/api';

export default {
  name: 'RichTextEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: '300px'
    },
    placeholder: {
      type: String,
      default: '请输入内容...'
    },
    readonly: {
      type: Boolean,
      default: false
    },
    toolbar: {
      type: Array,
      default: () => [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image']
      ]
    }
  },
  data() {
    return {
      editor: null,
      editorId: `editor-${Math.random().toString(36).substr(2, 9)}`
    };
  },

  mounted() {
    this.initEditor();
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor = null;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.editor && newVal !== this.getContent()) {
          this.setContent(newVal);
        }
      },
      immediate: false
    }
  },
  methods: {
    initEditor() {
      // 确保DOM元素存在
      const editorElement = document.getElementById(this.editorId);
      if (!editorElement) {
        console.error('编辑器容器元素不存在');
        return;
      }

      // 清空容器内容
      editorElement.innerHTML = '';

      // 初始化Quill编辑器
      this.editor = new Quill(`#${this.editorId}`, {
        theme: 'snow',
        modules: {
          toolbar: {
            container: this.toolbar,
            handlers: {
              image: this.handleImageUpload
            }
          }
        },
        placeholder: this.placeholder,
        readOnly: this.readonly
      });

      // 设置初始内容
      if (this.value) {
        this.setContent(this.value);
      }

      // 监听内容变化
      this.editor.on('text-change', () => {
        const content = this.getContent();
        this.$emit('input', content);
        this.$emit('change', content);
      });

      console.log('富文本编辑器初始化成功');
    },

    // 自定义图片上传处理
    handleImageUpload() {
      const input = document.createElement('input');
      input.setAttribute('type', 'file');
      input.setAttribute('accept', 'image/*');
      input.click();

      input.onchange = () => {
        const file = input.files[0];
        if (file) {
          this.uploadImage(file);
        }
      };
    },

    // 上传图片到服务器
    async uploadImage(file) {
      // 验证文件类型和大小
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件！');
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        this.$message.error('图片大小不能超过2MB！');
        return;
      }

      const formData = new FormData();
      formData.append('file', file);

      try {
        // 显示上传进度
        const loadingMessage = this.$message({
          message: '图片上传中...',
          type: 'info',
          duration: 0
        });

        const response = await api.upload.uploadImage(file);

        loadingMessage.close();

        if (response.success) {
          // 获取当前光标位置
          const range = this.editor.getSelection();
          const index = range ? range.index : this.editor.getLength();

          // 插入图片
          this.editor.insertEmbed(index, 'image', response.data.url);

          // 移动光标到图片后面
          this.editor.setSelection(index + 1);

          this.$message.success('图片上传成功');
        } else {
          this.$message.error(response.message || '图片上传失败');
        }
      } catch (error) {
        console.error('图片上传错误:', error);
        this.$message.error('图片上传失败，请重试');
      }
    },

    // 获取编辑器内容
    getContent() {
      if (!this.editor) return '';
      const content = this.editor.root.innerHTML;
      return content === '<p><br></p>' ? '' : content;
    },

    // 设置编辑器内容
    setContent(content) {
      if (!this.editor) return;
      this.editor.root.innerHTML = content || '';
    },

    // 获取纯文本内容
    getText() {
      if (!this.editor) return '';
      return this.editor.getText();
    },

    // 清空内容
    clear() {
      if (!this.editor) return;
      this.editor.setText('');
    },

    // 聚焦编辑器
    focus() {
      if (!this.editor) return;
      this.editor.focus();
    },

    // 设置只读状态
    setReadonly(readonly) {
      if (!this.editor) return;
      this.editor.enable(!readonly);
    }
  }
};
</script>

<style lang="less" scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  
  /deep/ .ql-toolbar {
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #dcdfe6;
    background: #fafafa;
  }
  
  /deep/ .ql-container {
    border: none;
    font-size: 14px;
    
    .ql-editor {
      padding: 12px 15px;
      
      &.ql-blank::before {
        color: #c0c4cc;
        font-style: normal;
      }
      
      img {
        max-width: 100%;
        height: auto;
      }
    }
  }
  
  /deep/ .ql-snow .ql-tooltip {
    z-index: 9999;
  }
}
</style>
