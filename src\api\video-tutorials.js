import { request } from './http'

export const videoTutorials = {
  // 获取视频教程列表
  getList(params = {}) {
    return request.get('/api/video-tutorials', params)
  },

  // 获取视频教程详情
  getDetail(id) {
    return request.get(`/api/video-tutorials/${id}`)
  },

  // 创建视频教程
  create(data) {
    return request.post('/api/video-tutorials', data)
  },

  // 更新视频教程
  update(id, data) {
    return request.put(`/api/video-tutorials/${id}`, data)
  },

  // 删除视频教程
  delete(id) {
    return request.delete(`/api/video-tutorials/${id}`)
  }
}

export default videoTutorials
