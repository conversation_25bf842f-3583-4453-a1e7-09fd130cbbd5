const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取图片列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    description = '',
    tag = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (description) {
    whereConditions.push('description LIKE ?');
    queryParams.push(`%${description}%`);
  }

  if (tag) {
    whereConditions.push('tag LIKE ?');
    queryParams.push(`%${tag}%`);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM qr_codes ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取图片列表
  const qrCodesQuery = `
    SELECT id, image_url, description, tag, sort_order, status, created_at, updated_at
    FROM qr_codes
    ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const qrCodes = await query(qrCodesQuery, queryParams);

  res.json({
    success: true,
    message: '获取图片列表成功',
    data: {
      qrCodes,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取图片详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query('SELECT * FROM qr_codes WHERE id = ?', [id]);

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '图片记录不存在'
    });
  }

  res.json({
    success: true,
    message: '获取图片详情成功',
    data: rows[0]
  });
}));

// 创建图片
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { image_url, description, tag, sort_order = 0, status = 1 } = req.body;

  // 验证必填字段
  if (!image_url) {
    return res.status(400).json({
      success: false,
      message: '图片不能为空'
    });
  }

  const result = await query(
    'INSERT INTO qr_codes (image_url, description, tag, sort_order, status) VALUES (?, ?, ?, ?, ?)',
    [image_url, description || '', tag || null, parseInt(sort_order), parseInt(status)]
  );

  const newRows = await query('SELECT * FROM qr_codes WHERE id = ?', [result.insertId]);

  res.status(201).json({
    success: true,
    message: '创建图片成功',
    data: newRows[0]
  });
}));

// 更新图片
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { image_url, description, tag, sort_order, status } = req.body;

  // 验证必填字段
  if (!image_url) {
    return res.status(400).json({
      success: false,
      message: '图片不能为空'
    });
  }

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM qr_codes WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '图片记录不存在'
    });
  }

  await query(
    'UPDATE qr_codes SET image_url = ?, description = ?, tag = ?, sort_order = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [image_url, description || '', tag || null, parseInt(sort_order), parseInt(status), id]
  );

  const updatedRows = await query('SELECT * FROM qr_codes WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '更新图片成功',
    data: updatedRows[0]
  });
}));

// 删除图片
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM qr_codes WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '图片记录不存在'
    });
  }

  await query('DELETE FROM qr_codes WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除图片成功'
  });
}));

module.exports = router;
