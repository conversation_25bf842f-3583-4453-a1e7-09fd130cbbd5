const express = require('express');
const { query, transaction } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取节能案例列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    case_type = '',
    status = '',
    is_homepage_featured = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (case_type) {
    whereConditions.push('case_type LIKE ?');
    queryParams.push(`%${case_type}%`);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(status);
  }

  if (is_homepage_featured !== '') {
    whereConditions.push('is_homepage_featured = ?');
    queryParams.push(is_homepage_featured);
  }

  const whereClause = whereConditions.length > 0
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  // 获取总数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM energy_cases
    ${whereClause}
  `;

  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取案例列表
  const casesQuery = `
    SELECT *
    FROM energy_cases
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const cases = await query(casesQuery, queryParams);

  res.json({
    success: true,
    message: '获取节能案例列表成功',
    data: {
      cases,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取单个节能案例详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const cases = await query('SELECT * FROM energy_cases WHERE id = ?', [id]);

  if (cases.length === 0) {
    throw new CustomError('节能案例不存在', 404);
  }

  const energyCase = cases[0];

  // 增加浏览次数
  await query('UPDATE energy_cases SET view_count = view_count + 1 WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '获取节能案例详情成功',
    data: {
      case: energyCase
    }
  });
}));

// 创建节能案例
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    title,
    main_image,
    summary,
    description,
    case_type,
    case_area,
    product_category,
    case_tags,
    status = 0,
    sort_order = 0,
    is_homepage_featured = 0
  } = req.body;

  // 参数验证
  if (!title) {
    throw new CustomError('案例标题不能为空', 400);
  }

  const result = await query(`
    INSERT INTO energy_cases (title, main_image, summary, description, case_type, case_area, product_category, case_tags, status, sort_order, is_homepage_featured)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [title, main_image || null, summary || null, description || null, case_type || null, case_area || null, product_category || null, case_tags || null, status, sort_order, is_homepage_featured]);

  res.json({
    success: true,
    message: '创建节能案例成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新节能案例
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    title,
    main_image,
    summary,
    description,
    case_type,
    case_area,
    product_category,
    case_tags,
    status,
    sort_order,
    is_homepage_featured
  } = req.body;

  // 检查案例是否存在
  const existingCases = await query('SELECT * FROM energy_cases WHERE id = ?', [id]);
  if (existingCases.length === 0) {
    throw new CustomError('节能案例不存在', 404);
  }

  // 构建更新字段和参数
  const updateFields = [];
  const updateParams = [];

  if (title !== undefined) {
    if (!title.trim()) {
      throw new CustomError('案例标题不能为空', 400);
    }
    updateFields.push('title = ?');
    updateParams.push(title);
  }

  if (main_image !== undefined) {
    updateFields.push('main_image = ?');
    updateParams.push(main_image);
  }

  if (summary !== undefined) {
    updateFields.push('summary = ?');
    updateParams.push(summary);
  }

  if (description !== undefined) {
    updateFields.push('description = ?');
    updateParams.push(description);
  }

  if (case_type !== undefined) {
    updateFields.push('case_type = ?');
    updateParams.push(case_type);
  }

  if (case_area !== undefined) {
    updateFields.push('case_area = ?');
    updateParams.push(case_area);
  }

  if (product_category !== undefined) {
    updateFields.push('product_category = ?');
    updateParams.push(product_category);
  }

  if (case_tags !== undefined) {
    updateFields.push('case_tags = ?');
    updateParams.push(case_tags);
  }

  if (status !== undefined) {
    updateFields.push('status = ?');
    updateParams.push(status);
  }

  if (sort_order !== undefined) {
    updateFields.push('sort_order = ?');
    updateParams.push(sort_order);
  }

  if (is_homepage_featured !== undefined) {
    updateFields.push('is_homepage_featured = ?');
    updateParams.push(is_homepage_featured);
  }

  // 如果没有要更新的字段，返回错误
  if (updateFields.length === 0) {
    throw new CustomError('没有要更新的字段', 400);
  }

  // 添加更新时间
  updateFields.push('updated_at = NOW()');
  updateParams.push(id);

  await query(`
    UPDATE energy_cases
    SET ${updateFields.join(', ')}
    WHERE id = ?
  `, updateParams);

  res.json({
    success: true,
    message: '更新节能案例成功'
  });
}));

// 删除节能案例
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查案例是否存在
  const existingCases = await query('SELECT * FROM energy_cases WHERE id = ?', [id]);
  if (existingCases.length === 0) {
    throw new CustomError('节能案例不存在', 404);
  }

  await query('DELETE FROM energy_cases WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除节能案例成功'
  });
}));

// 批量更新案例状态
router.post('/batch-status', authenticateToken, asyncHandler(async (req, res) => {
  const { ids, status } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new CustomError('案例ID列表不能为空', 400);
  }

  if (![0, 1].includes(status)) {
    throw new CustomError('状态值无效', 400);
  }

  const placeholders = ids.map(() => '?').join(',');
  await query(`UPDATE energy_cases SET status = ? WHERE id IN (${placeholders})`, [status, ...ids]);

  res.json({
    success: true,
    message: '批量更新状态成功'
  });
}));

module.exports = router;
