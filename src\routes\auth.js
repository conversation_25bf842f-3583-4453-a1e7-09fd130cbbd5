const express = require('express');
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const { generateToken, authenticateToken } = require('../middleware/auth');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 管理员登录
router.post('/login', asyncHandler(async (req, res) => {
  const { username, password } = req.body;

  // 参数验证
  if (!username || !password) {
    throw new CustomError('用户名和密码不能为空', 400);
  }

  // 查询用户
  const users = await query(
    'SELECT id, username, password, email, real_name, status FROM admins WHERE username = ?',
    [username]
  );

  if (users.length === 0) {
    throw new CustomError('用户名或密码错误', 401);
  }

  const user = users[0];

  // 检查用户状态
  if (user.status !== 1) {
    throw new CustomError('账号已被禁用', 401);
  }

  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    throw new CustomError('用户名或密码错误', 401);
  }

  // 更新最后登录时间
  await query(
    'UPDATE admins SET last_login_time = NOW() WHERE id = ?',
    [user.id]
  );

  // 生成token
  const token = generateToken(user.id);

  // 返回用户信息（不包含密码）
  const userInfo = {
    id: user.id,
    username: user.username,
    email: user.email,
    real_name: user.real_name
  };

  res.json({
    success: true,
    message: '登录成功',
    data: {
      user: userInfo,
      token,
      expires_in: process.env.JWT_EXPIRES_IN || '24h'
    }
  });
}));

// 获取当前用户信息
router.get('/profile', authenticateToken, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: '获取用户信息成功',
    data: {
      user: req.user
    }
  });
}));

// 登出（客户端删除token即可，这里主要用于日志记录）
router.post('/logout', authenticateToken, asyncHandler(async (req, res) => {
  // 这里可以添加登出日志记录
  console.log(`用户 ${req.user.username} 于 ${new Date().toLocaleString()} 登出`);
  
  res.json({
    success: true,
    message: '登出成功'
  });
}));

// 修改密码
router.post('/change-password', authenticateToken, asyncHandler(async (req, res) => {
  const { oldPassword, newPassword } = req.body;

  // 参数验证
  if (!oldPassword || !newPassword) {
    throw new CustomError('旧密码和新密码不能为空', 400);
  }

  if (newPassword.length < 6) {
    throw new CustomError('新密码长度不能少于6位', 400);
  }

  // 查询当前用户密码
  const users = await query(
    'SELECT password FROM admins WHERE id = ?',
    [req.user.id]
  );

  if (users.length === 0) {
    throw new CustomError('用户不存在', 404);
  }

  // 验证旧密码
  const isOldPasswordValid = await bcrypt.compare(oldPassword, users[0].password);
  if (!isOldPasswordValid) {
    throw new CustomError('旧密码错误', 400);
  }

  // 加密新密码
  const hashedNewPassword = await bcrypt.hash(newPassword, parseInt(process.env.BCRYPT_ROUNDS) || 12);

  // 更新密码
  await query(
    'UPDATE admins SET password = ?, updated_at = NOW() WHERE id = ?',
    [hashedNewPassword, req.user.id]
  );

  res.json({
    success: true,
    message: '密码修改成功'
  });
}));

module.exports = router;
