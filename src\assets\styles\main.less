// 全局样式变量
@primary-color: #409EFF;
@success-color: #67C23A;
@warning-color: #E6A23C;
@danger-color: #F56C6C;
@info-color: #909399;

@text-color-primary: #303133;
@text-color-regular: #606266;
@text-color-secondary: #909399;
@text-color-placeholder: #C0C4CC;

@border-color-base: #DCDFE6;
@border-color-light: #E4E7ED;
@border-color-lighter: #EBEEF5;
@border-color-extra-light: #F2F6FC;

@background-color-base: #F5F7FA;
@background-color-light: #FAFAFA;

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 布局相关
.full-height {
  height: 100vh;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 间距工具类
.m-0 {
  margin: 0 !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mr-0 {
  margin-right: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.ml-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 8px !important;
}

.mt-1 {
  margin-top: 8px !important;
}

.mr-1 {
  margin-right: 8px !important;
}

.mb-1 {
  margin-bottom: 8px !important;
}

.ml-1 {
  margin-left: 8px !important;
}

.m-2 {
  margin: 16px !important;
}

.mt-2 {
  margin-top: 16px !important;
}

.mr-2 {
  margin-right: 16px !important;
}

.mb-2 {
  margin-bottom: 16px !important;
}

.ml-2 {
  margin-left: 16px !important;
}

.m-3 {
  margin: 24px !important;
}

.mt-3 {
  margin-top: 24px !important;
}

.mr-3 {
  margin-right: 24px !important;
}

.mb-3 {
  margin-bottom: 24px !important;
}

.ml-3 {
  margin-left: 24px !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 8px !important;
}

.pt-1 {
  padding-top: 8px !important;
}

.pr-1 {
  padding-right: 8px !important;
}

.pb-1 {
  padding-bottom: 8px !important;
}

.pl-1 {
  padding-left: 8px !important;
}

.p-2 {
  padding: 16px !important;
}

.pt-2 {
  padding-top: 16px !important;
}

.pr-2 {
  padding-right: 16px !important;
}

.pb-2 {
  padding-bottom: 16px !important;
}

.pl-2 {
  padding-left: 16px !important;
}

.p-3 {
  padding: 24px !important;
}

.pt-3 {
  padding-top: 24px !important;
}

.pr-3 {
  padding-right: 24px !important;
}

.pb-3 {
  padding-bottom: 24px !important;
}

.pl-3 {
  padding-left: 24px !important;
}

// 自定义滚动条
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Element UI 样式覆盖
.el-table {
  .el-table__header {
    th {
      background-color: @background-color-base;
      color: @text-color-primary;
      font-weight: 600;
    }
  }
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button {
  border-radius: 4px;
}

// 页面容器
.page-container {
  padding: 20px;
  background-color: @background-color-base;
  min-height: calc(100vh - 60px);
}

.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
    }
  }

}