/**
 * 菜单工具函数
 */

/**
 * 根据路径查找菜单项
 * @param {Array} menuItems 菜单配置数组
 * @param {String} path 路径
 * @returns {Object|null} 找到的菜单项
 */
export function findMenuByPath(menuItems, path) {
  for (const item of menuItems) {
    if (item.path === path) {
      return item
    }
    
    if (item.children && item.children.length > 0) {
      const found = findMenuByPath(item.children, path)
      if (found) {
        return found
      }
    }
  }
  
  return null
}

/**
 * 获取面包屑导航
 * @param {Array} menuItems 菜单配置数组
 * @param {String} currentPath 当前路径
 * @returns {Array} 面包屑数组
 */
export function getBreadcrumb(menuItems, currentPath) {
  const breadcrumb = []
  
  function findPath(items, path, parents = []) {
    for (const item of items) {
      const currentParents = [...parents, item]
      
      if (item.path === path) {
        breadcrumb.push(...currentParents)
        return true
      }
      
      if (item.children && item.children.length > 0) {
        if (findPath(item.children, path, currentParents)) {
          return true
        }
      }
    }
    return false
  }
  
  findPath(menuItems, currentPath)
  return breadcrumb
}

/**
 * 检查菜单项是否有权限
 * @param {Object} menuItem 菜单项
 * @param {Array} userPermissions 用户权限列表
 * @returns {Boolean} 是否有权限
 */
export function hasPermission(menuItem, userPermissions = []) {
  // 如果菜单项没有设置权限要求，默认有权限
  if (!menuItem.meta || !menuItem.meta.permissions) {
    return true
  }
  
  // 检查用户是否有所需权限
  const requiredPermissions = menuItem.meta.permissions
  return requiredPermissions.some(permission => 
    userPermissions.includes(permission)
  )
}

/**
 * 过滤用户有权限的菜单
 * @param {Array} menuItems 菜单配置数组
 * @param {Array} userPermissions 用户权限列表
 * @returns {Array} 过滤后的菜单数组
 */
export function filterMenuByPermission(menuItems, userPermissions = []) {
  return menuItems.filter(item => {
    // 检查当前菜单项权限
    if (!hasPermission(item, userPermissions)) {
      return false
    }
    
    // 如果有子菜单，递归过滤
    if (item.children && item.children.length > 0) {
      item.children = filterMenuByPermission(item.children, userPermissions)
    }
    
    return true
  })
}

/**
 * 获取所有叶子菜单项（没有子菜单的菜单项）
 * @param {Array} menuItems 菜单配置数组
 * @returns {Array} 叶子菜单项数组
 */
export function getLeafMenuItems(menuItems) {
  const leafItems = []
  
  function traverse(items) {
    items.forEach(item => {
      if (!item.children || item.children.length === 0) {
        leafItems.push(item)
      } else {
        traverse(item.children)
      }
    })
  }
  
  traverse(menuItems)
  return leafItems
}

/**
 * 验证菜单配置的完整性
 * @param {Array} menuItems 菜单配置数组
 * @returns {Object} 验证结果
 */
export function validateMenuConfig(menuItems) {
  const errors = []
  const warnings = []
  const paths = new Set()
  
  function validate(items, parentPath = '') {
    items.forEach((item, index) => {
      const currentPath = `${parentPath}[${index}]`
      
      // 检查必需字段
      if (!item.path) {
        errors.push(`${currentPath}: 缺少 path 字段`)
      }
      
      if (!item.name) {
        errors.push(`${currentPath}: 缺少 name 字段`)
      }
      
      if (!item.meta || !item.meta.title) {
        errors.push(`${currentPath}: 缺少 meta.title 字段`)
      }
      
      // 检查路径重复
      if (item.path && paths.has(item.path)) {
        errors.push(`${currentPath}: 路径 "${item.path}" 重复`)
      } else if (item.path) {
        paths.add(item.path)
      }
      
      // 检查组件
      if (!item.children && !item.component) {
        warnings.push(`${currentPath}: 叶子节点缺少 component 字段`)
      }
      
      // 递归检查子菜单
      if (item.children && item.children.length > 0) {
        validate(item.children, `${currentPath}.children`)
      }
    })
  }
  
  validate(menuItems)
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}
