<template>
  <div class="case-detail">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="fetchCaseDetail">重试</button>
      </div>
    </div>

    <!-- 案例详情内容 -->
    <div v-else-if="caseData" class="detail-content">
      <div class="container">
        <!-- 文章头部 -->
        <div class="article-header">
          <h1 class="article-title">{{ caseData.title }}</h1>
          <div class="article-meta">
            <span class="publish-time">{{ formatDate(caseData.created_at) }}</span>
            <span v-if="caseData.view_count" class="view-count">
              <i class="fas fa-eye"></i>
              {{ caseData.view_count }} 次阅读
            </span>
          </div>
        </div>

        <!-- 文章内容 -->
        <div class="article-body">
          <div v-if="caseData.description" class="rich-content" v-html="caseData.description"></div>
          <div v-else class="no-content">
            <p>暂无详细内容</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PageHeader from '@/components/common/PageHeader.vue'
import Breadcrumb from '@/components/common/Breadcrumb.vue'
import api from '@/api'
import { formatDateTime } from '@/utils/dateFormat'

export default {
  name: 'CaseDetail',
  components: {
    PageHeader,
    Breadcrumb
  },
  data() {
    return {
      loading: true,
      error: null,
      caseData: null
    }
  },
  computed: {
    breadcrumbItems() {
      const items = [
        { name: '工程案例', path: '/cases' },
      ]
      if (this.caseData) {
        items.push({ name: this.caseData.title })
      }
      return items
    }
  },
  created() {
    this.fetchCaseDetail()
  },
  watch: {
    '$route'() {
      this.fetchCaseDetail()
    }
  },
  methods: {
    async fetchCaseDetail() {
      try {
        this.loading = true
        this.error = null

        const caseId = this.$route.params.id

        // 获取案例详情
        const response = await api.get(`/api/front/energy-cases/${caseId}`)

        if (response.success) {
          this.caseData = response.data.case

          // 更新页面标题
          if (this.caseData.title) {
            document.title = `${this.caseData.title} - 案例详情 - 雅克菲采暖`
          }
        } else {
          this.error = response.message || '获取案例详情失败'
        }
      } catch (error) {
        console.error('获取案例详情失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    formatDate(dateString) {
      return formatDateTime(dateString, 'YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>


<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.case-detail {
  min-height: 100vh;
  background: #f8f9fa;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .loading-spinner,
  .error-content {
    text-align: center;

    i {
      font-size: 48px;
      color: @text-lighter;
      margin-bottom: 16px;
    }

    h3 {
      margin: 16px 0 8px;
      color: @text-color;
    }

    p {
      color: @text-light;
      margin-bottom: 16px;
    }

    .btn {
      background: @primary-color;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: @border-radius-small;
      cursor: pointer;
      transition: @transition;

      &:hover {
        background: @primary-hover;
      }
    }
  }
}

.detail-content {
  padding: @spacing-xxl 0;

  .container {
    max-width: @container-max-width;
    margin: 0 auto;
    padding: 0 @spacing-lg;
  }
}

.article-header {
  text-align: center;
  margin-bottom: @spacing-xxl;
  padding-bottom: @spacing-xl;
  border-bottom: 1px solid @border-color;

  .article-title {
    font-size: @font-size-xxl;
    font-weight: @font-weight-bold;
    color: @text-color;
    margin-bottom: @spacing-lg;
    line-height: 1.3;
    font-family: @font-family-base;
  }

  .article-meta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: @spacing-lg;
    margin-bottom: @spacing-lg;
    color: @text-light;
    font-size: @font-size-sm;

    .publish-time {
      display: flex;
      align-items: center;
      gap: @spacing-xs;
    }

    .view-count {
      display: flex;
      align-items: center;
      gap: @spacing-xs;

      i {
        color: @primary-color;
      }
    }
  }
}

.article-body {
  .rich-content {
    line-height: 1.8;
    color: @text-color;
    font-size: @font-size-base;

    :deep(img) {
      width: 100%;
      height: auto;
      border-radius: @border-radius-small;
      margin: @spacing-lg 0;
      box-shadow: @shadow-light;
    }

    :deep(p) {
      margin-bottom: @spacing-md;
      text-align: justify;
    }
  }

  .no-content {
    text-align: center;
    padding: @spacing-xxl 0;
    color: @text-lighter;

    p {
      font-size: @font-size-lg;
    }
  }
}

// 移动端适配
@media (max-width: @screen-sm) {
  .detail-content .container {
    padding: 0 @spacing-md;
  }

  .article-header {
    margin-bottom: @spacing-xl;
    padding-bottom: @spacing-lg;

    .article-title {
      font-size: @font-size-xl;
    }
  }

  .article-body .rich-content {
    :deep(img) {
      width: 100%;
    }

    :deep(h1) {
      font-size: @font-size-lg;
    }

    :deep(h2) {
      font-size: @font-size-base;
    }

    :deep(h3) {
      font-size: @font-size-sm;
    }
  }
}
</style>
