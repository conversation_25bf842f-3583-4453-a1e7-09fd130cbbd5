const express = require('express');
const { query } = require('../../config/database');
const { CustomError, asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取新闻案例列表
router.get('/', asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 12,
    category,
    keyword
  } = req.query;

  const offset = (page - 1) * limit;
  let whereConditions = ['status = 1']; // 只返回已发布的新闻
  let params = [];

  // 分类筛选
  if (category) {
    whereConditions.push('category = ?');
    params.push(category);
  }

  // 关键词搜索
  if (keyword) {
    whereConditions.push('(title LIKE ? OR summary LIKE ?)');
    params.push(`%${keyword}%`, `%${keyword}%`);
  }

  const whereClause = whereConditions.join(' AND ');

  // 查询新闻列表
  let sql = `
    SELECT
      id,
      title,
      summary,
      thumbnail,
      category,
      view_count,
      created_at
    FROM news
    WHERE ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const news = await query(sql, params);

  // 查询总数
  const totalResult = await query(`
    SELECT COUNT(*) as total
    FROM news
    WHERE ${whereClause}
  `, params);

  const total = totalResult[0].total;

  res.json({
    success: true,
    message: '获取新闻案例列表成功',
    data: {
      news,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取新闻案例详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 查询新闻详情
  const news = await query(`
    SELECT *
    FROM news
    WHERE id = ? AND status = 1
  `, [id]);
  
  if (news.length === 0) {
    throw new CustomError('新闻不存在或已下架', 404);
  }
  
  const newsItem = news[0];
  
  // 增加浏览量
  await query('UPDATE news SET view_count = view_count + 1 WHERE id = ?', [id]);
  newsItem.view_count += 1;
  
  // 获取相关新闻（同类型的其他新闻）
  const relatedNews = await query(`
    SELECT id, title, summary, category
    FROM news
    WHERE category = ? AND id != ? AND status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 4
  `, [newsItem.category || '', id]);
  
  res.json({
    success: true,
    message: '获取新闻案例详情成功',
    data: {
      news: newsItem,
      related_news: relatedNews
    }
  });
}));

// 获取热门新闻
router.get('/hot/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;
  
  const news = await query(`
    SELECT
      id,
      title,
      summary,
      thumbnail,
      category,
      view_count
    FROM news
    WHERE status = 1
    ORDER BY view_count DESC, created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);
  
  res.json({
    success: true,
    message: '获取热门新闻成功',
    data: {
      news
    }
  });
}));

// 获取最新新闻
router.get('/latest/list', asyncHandler(async (req, res) => {
  const { limit = 6 } = req.query;
  
  const news = await query(`
    SELECT
      id,
      title,
      summary,
      thumbnail,
      category,
      created_at
    FROM news
    WHERE status = 1
    ORDER BY created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);
  
  res.json({
    success: true,
    message: '获取最新新闻成功',
    data: {
      news
    }
  });
}));

// 获取推荐新闻（首页展示）
router.get('/featured/list', asyncHandler(async (req, res) => {
  const { limit = 4 } = req.query;
  
  const news = await query(`
    SELECT
      id,
      title,
      summary,
      thumbnail,
      created_at
    FROM news
    WHERE status = 1
    ORDER BY view_count DESC, sort_order ASC, created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);
  
  res.json({
    success: true,
    message: '获取推荐新闻成功',
    data: {
      news
    }
  });
}));

module.exports = router;
