const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { VueLoaderPlugin } = require('vue-loader');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const webpack = require('webpack');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    entry: './src/main.js',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'js/[name].[contenthash].js',
      clean: true,
      publicPath: '/'
    },
    resolve: {
      extensions: ['.js', '.vue', '.json'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
        'vue$': 'vue/dist/vue.esm.js'
      }
    },
    module: {
      rules: [
        {
          test: /\.vue$/,
          loader: 'vue-loader'
        },
        {
          test: /\.js$/,
          loader: 'babel-loader',
          exclude: /node_modules/
        },
        {
          test: /\.css$/,
          use: [
            'style-loader',
            'css-loader'
          ]
        },
        {
          test: /\.less$/,
          use: [
            'style-loader',
            'css-loader',
            'less-loader'
          ]
        },
        {
          test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
          type: 'asset',
          parser: {
            dataUrlCondition: {
              maxSize: 10 * 1024 // 10kb
            }
          },
          generator: {
            filename: 'images/[name].[contenthash][ext]'
          }
        },
        {
          test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
          type: 'asset',
          generator: {
            filename: 'media/[name].[contenthash][ext]'
          }
        },
        {
          test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
          type: 'asset',
          generator: {
            filename: 'fonts/[name].[contenthash][ext]'
          }
        }
      ]
    },
    plugins: [
      new VueLoaderPlugin(),
      new webpack.DefinePlugin({
        'process.env': {
          NODE_ENV: JSON.stringify(isProduction ? 'production' : 'development'),
          BASE_URL: JSON.stringify('/')
        }
      }),
      new HtmlWebpackPlugin({
        template: './public/index.html',
        filename: 'index.html',
        inject: true,
        minify: {
          removeComments: true,
          collapseWhitespace: true,
          removeAttributeQuotes: true
        }
      }),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, 'public'),
            to: path.resolve(__dirname, 'dist'),
            globOptions: {
              ignore: ['**/index.html']
            },
            noErrorOnMissing: true
          }
        ]
      })
    ],
    devServer: {
      static: {
        directory: path.join(__dirname, 'public')
      },
      compress: true,
      port: 8083,
      hot: true,
      open: false,
      historyApiFallback: true,
      host: 'localhost',
      proxy: {
        '/api': {
          target: 'http://localhost:3333',
          changeOrigin: true,
          secure: false
        },
        '/uploads': {
          target: 'http://localhost:3333',
          changeOrigin: true,
          secure: false
        }
      }
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all'
          },
          // 单独分离Vue相关库
          vue: {
            test: /[\\/]node_modules[\\/](vue|vue-router|vuex)[\\/]/,
            name: 'vue',
            priority: 20,
            chunks: 'all'
          },
          // 分离UI库
          ui: {
            test: /[\\/]node_modules[\\/](swiper|aos)[\\/]/,
            name: 'ui',
            priority: 15,
            chunks: 'all'
          },
          // 分离工具库
          utils: {
            test: /[\\/]node_modules[\\/](axios|file-saver)[\\/]/,
            name: 'utils',
            priority: 10,
            chunks: 'all'
          }
        }
      },
      // 生产环境下启用更多优化
      ...(isProduction && {
        usedExports: true,
        sideEffects: false,
        concatenateModules: true
      })
    },
    // 性能配置
    performance: {
      hints: isProduction ? 'warning' : false,
      maxAssetSize: 300000, // 300KB
      maxEntrypointSize: 500000, // 500KB
      assetFilter: function (assetFilename) {
        // 忽略字体文件的大小警告
        return !assetFilename.endsWith('.ttf') && !assetFilename.endsWith('.woff') && !assetFilename.endsWith('.woff2');
      }
    }
  };
};
