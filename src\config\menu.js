/**
 * 统一菜单配置
 * 同时用于生成路由和侧边栏菜单
 */

// 路由组件懒加载
const Dashboard = () => import('@/views/Dashboard.vue')
const MenuManagement = () => import('@/views/MenuManagement.vue')
const ProductCategoryManagement = () => import('@/views/ProductCategoryManagement.vue')
const ProductManagement = () => import('@/views/ProductManagement.vue')
const EnergyCaseManagement = () => import('@/views/EnergyCaseManagement.vue')
const NewsManagement = () => import('@/views/NewsManagement.vue')
const DealerManagement = () => import('@/views/DealerManagement.vue')
const ResourceLibraryManagement = () => import('@/views/ResourceLibraryManagement.vue')
const VideoTutorialManagement = () => import('@/views/VideoTutorialManagement.vue')
const HeatingKnowledgeManagement = () => import('@/views/HeatingKnowledgeManagement.vue')
const BannerManagement = () => import('@/views/BannerManagement.vue')
// 关于我们相关组件
const CompanyProfile = () => import('@/views/CompanyProfile.vue')
// const CompanyHonors = () => import('@/views/CompanyHonors.vue')
const DevelopmentHistory = () => import('@/views/DevelopmentHistory.vue')
const QrCodes = () => import('@/views/QrCodes.vue')
const ContactUsManagement = () => import('@/views/ContactUsManagement.vue')
const PageContentBlocksManagement = () => import('@/views/PageContentBlocks.vue')
const SystemConfigsManagement = () => import('@/views/SystemConfigs.vue')

/**
 * 菜单配置项说明：
 * - path: 路由路径
 * - name: 路由名称
 * - component: 路由组件
 * - meta: 路由元信息
 *   - title: 页面标题
 *   - icon: 菜单图标
 *   - requiresAuth: 是否需要登录
 *   - hidden: 是否在菜单中隐藏
 * - children: 子菜单（可选）
 */
export const menuConfig = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表盘',
      icon: 'el-icon-s-home',
      requiresAuth: true
    }
  },
  {
    path: '/menu-management',
    name: 'MenuManagement',
    component: MenuManagement,
    meta: {
      title: '菜单管理',
      icon: 'el-icon-menu',
      requiresAuth: true
    }
  },
  {
    path: '/page-content-blocks-management',
    name: 'PageContentBlocksManagement',
    component: PageContentBlocksManagement,
    meta: {
      title: '页面内容管理',
      icon: 'el-icon-menu',
      requiresAuth: true
    }
  },
  {
    path: '/system-configs-management',
    name: 'SystemConfigsManagement',
    component: SystemConfigsManagement,
    meta: {
      title: '分类配置管理',
      icon: 'el-icon-setting',
      requiresAuth: true
    }
  },
  {
    path: '/banner-management',
    name: 'BannerManagement',
    component: BannerManagement,
    meta: {
      title: 'Banner管理',
      icon: 'el-icon-picture',
      requiresAuth: true
    }
  },
  {
    path: '/product',
    name: 'Product',
    meta: {
      title: '产品管理',
      icon: 'el-icon-goods',
      requiresAuth: true
    },
    children: [
      {
        path: '/product-categories',
        name: 'ProductCategoryManagement',
        component: ProductCategoryManagement,
        meta: {
          title: '产品分类',
          icon: 'el-icon-folder',
          requiresAuth: true
        }
      },
      {
        path: '/products',
        name: 'ProductManagement',
        component: ProductManagement,
        meta: {
          title: '产品维护',
          icon: 'el-icon-goods',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/energy-cases',
    name: 'EnergyCaseManagement',
    component: EnergyCaseManagement,
    meta: {
      title: '节能案例',
      icon: 'el-icon-lightning',
      requiresAuth: true
    }
  },

  {
    path: '/dealers',
    name: 'DealerManagement',
    component: DealerManagement,
    meta: {
      title: '经销体系',
      icon: 'el-icon-office-building',
      requiresAuth: true
    }
  },
  {
    path: '/service-support',
    name: 'ServiceSupport',
    meta: {
      title: '服务支持',
      icon: 'el-icon-s-help',
      requiresAuth: true
    },
    children: [
      {
        path: '/resource-library',
        name: 'ResourceLibraryManagement',
        component: ResourceLibraryManagement,
        meta: {
          title: '文档资料',
          icon: 'el-icon-folder-opened',
          requiresAuth: true
        }
      },
      {
        path: '/video-tutorials',
        name: 'VideoTutorialManagement',
        component: VideoTutorialManagement,
        meta: {
          title: '视频教程',
          icon: 'el-icon-video-play',
          requiresAuth: true
        }
      },
      {
        path: '/heating-knowledge',
        name: 'HeatingKnowledgeManagement',
        component: HeatingKnowledgeManagement,
        meta: {
          title: '采暖知识',
          icon: 'el-icon-reading',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/about-us',
    name: 'AboutUs',
    meta: {
      title: '关于我们',
      icon: 'el-icon-office-building',
      requiresAuth: true
    },
    children: [
      {
        path: '/company-profile',
        name: 'CompanyProfile',
        component: CompanyProfile,
        meta: {
          title: '公司简介',
          icon: 'el-icon-document',
          requiresAuth: true
        }
      },
      // {
      //   path: '/company-honors',
      //   name: 'CompanyHonors',
      //   component: CompanyHonors,
      //   meta: {
      //     title: '公司荣誉',
      //     icon: 'el-icon-trophy',
      //     requiresAuth: true
      //   }
      // },
      {
        path: '/development-history',
        name: 'DevelopmentHistory',
        component: DevelopmentHistory,
        meta: {
          title: '发展历程',
          icon: 'el-icon-time',
          requiresAuth: true
        }
      },
      {
        path: '/news',
        name: 'NewsManagement',
        component: NewsManagement,
        meta: {
          title: '新闻资讯',
          icon: 'el-icon-document',
          requiresAuth: true
        }
      },
      {
        path: '/contact-us',
        name: 'ContactUsManagement',
        component: ContactUsManagement,
        meta: {
          title: '联系我们',
          icon: 'el-icon-message',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/qr-codes',
    name: 'QrCodes',
    component: QrCodes,
    meta: {
      title: '图片维护',
      icon: 'el-icon-picture',
      requiresAuth: true
    }
  },
]

/**
 * 将菜单配置转换为路由配置
 */
export function generateRoutes(menuItems) {
  const routes = []

  menuItems.forEach(item => {
    const route = {
      path: item.path,
      name: item.name,
      meta: item.meta
    }

    // 如果有组件，添加组件
    if (item.component) {
      route.component = item.component
    }

    // 如果有子菜单，递归处理
    if (item.children && item.children.length > 0) {
      route.children = generateRoutes(item.children)
    }

    routes.push(route)
  })

  return routes
}

/**
 * 获取扁平化的路由列表（用于添加到路由器中）
 */
export function getFlatRoutes(menuItems) {
  const routes = []

  menuItems.forEach(item => {
    // 如果有组件，说明是叶子节点，添加到路由中
    if (item.component) {
      routes.push({
        path: item.path,
        name: item.name,
        component: item.component,
        meta: item.meta
      })
    }

    // 如果有子菜单，递归处理
    if (item.children && item.children.length > 0) {
      routes.push(...getFlatRoutes(item.children))
    }
  })

  return routes
}
