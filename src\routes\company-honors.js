const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取公司荣誉列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    description = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (description) {
    whereConditions.push('description LIKE ?');
    queryParams.push(`%${description}%`);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM company_honors ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取荣誉列表
  const honorsQuery = `
    SELECT
      id,
      title,
      summary,
      DATE_FORMAT(award_date, '%Y-%m-%d') as award_date,
      image_url,
      description,
      sort_order,
      status,
      created_at,
      updated_at
    FROM company_honors
    ${whereClause}
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const honors = await query(honorsQuery, queryParams);

  res.json({
    success: true,
    message: '获取公司荣誉列表成功',
    data: {
      honors,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取公司荣誉详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query(`
    SELECT
      id,
      title,
      summary,
      DATE_FORMAT(award_date, '%Y-%m-%d') as award_date,
      image_url,
      description,
      sort_order,
      status,
      created_at,
      updated_at
    FROM company_honors
    WHERE id = ?
  `, [id]);

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '公司荣誉记录不存在'
    });
  }

  res.json({
    success: true,
    message: '获取公司荣誉详情成功',
    data: rows[0]
  });
}));

// 创建公司荣誉
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { title, summary, award_date, image_url, description, sort_order = 0, status = 1 } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '荣誉标题不能为空'
    });
  }

  if (!summary) {
    return res.status(400).json({
      success: false,
      message: '荣誉摘要不能为空'
    });
  }

  if (!award_date) {
    return res.status(400).json({
      success: false,
      message: '获取时间不能为空'
    });
  }

  if (!image_url) {
    return res.status(400).json({
      success: false,
      message: '荣誉图片不能为空'
    });
  }

  const result = await query(
    'INSERT INTO company_honors (title, summary, award_date, image_url, description, sort_order, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [title, summary, award_date, image_url, description || '', parseInt(sort_order), parseInt(status)]
  );

  const newRows = await query(`
    SELECT
      id,
      title,
      summary,
      DATE_FORMAT(award_date, '%Y-%m-%d') as award_date,
      image_url,
      description,
      sort_order,
      status,
      created_at,
      updated_at
    FROM company_honors
    WHERE id = ?
  `, [result.insertId]);

  res.status(201).json({
    success: true,
    message: '创建公司荣誉成功',
    data: newRows[0]
  });
}));

// 更新公司荣誉
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { title, summary, award_date, image_url, description, sort_order, status } = req.body;

  // 验证必填字段
  if (!title) {
    return res.status(400).json({
      success: false,
      message: '荣誉标题不能为空'
    });
  }

  if (!summary) {
    return res.status(400).json({
      success: false,
      message: '荣誉摘要不能为空'
    });
  }

  if (!award_date) {
    return res.status(400).json({
      success: false,
      message: '获取时间不能为空'
    });
  }

  if (!image_url) {
    return res.status(400).json({
      success: false,
      message: '荣誉图片不能为空'
    });
  }

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM company_honors WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '公司荣誉记录不存在'
    });
  }

  await query(
    'UPDATE company_honors SET title = ?, summary = ?, award_date = ?, image_url = ?, description = ?, sort_order = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [title, summary, award_date, image_url, description || '', parseInt(sort_order), parseInt(status), id]
  );

  const updatedRows = await query(`
    SELECT
      id,
      title,
      summary,
      DATE_FORMAT(award_date, '%Y-%m-%d') as award_date,
      image_url,
      description,
      sort_order,
      status,
      created_at,
      updated_at
    FROM company_honors
    WHERE id = ?
  `, [id]);

  res.json({
    success: true,
    message: '更新公司荣誉成功',
    data: updatedRows[0]
  });
}));

// 删除公司荣誉
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM company_honors WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '公司荣誉记录不存在'
    });
  }

  await query('DELETE FROM company_honors WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除公司荣誉成功'
  });
}));

module.exports = router;
