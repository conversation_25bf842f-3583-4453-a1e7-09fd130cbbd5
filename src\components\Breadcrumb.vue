<template>
  <el-breadcrumb separator="/" class="breadcrumb">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbItems"
      :key="item.path"
      :to="index === breadcrumbItems.length - 1 ? null : item.path"
    >
      <i v-if="item.meta.icon" :class="item.meta.icon"></i>
      {{ item.meta.title }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
import { menuConfig } from '@/config/menu'
import { getBreadcrumb } from '@/utils/menu'

export default {
  name: 'Breadcrumb',
  computed: {
    breadcrumbItems() {
      return getBreadcrumb(menuConfig, this.$route.path)
    }
  },
  watch: {
    $route() {
      // 路由变化时更新面包屑
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.breadcrumb {
  margin-bottom: 20px;
}

.breadcrumb .el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: #606266;
  cursor: text;
}

.breadcrumb i {
  margin-right: 5px;
}
</style>
