<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-bottom">
        <div class="footer-info">
          <div class="footer-logo-container">
            <img class="footer-logo" :src="footer_logo" alt="">
          </div>
          <div class="phone-info">
            <!-- <i class="fas fa-phone foot-icon"></i>
            <span class="foot-number">4008-206-306</span> -->
            <span>{{ dynamicContent.title }}</span>
            <span class="beian-info-separator">|</span>
            <span class="beian-info">
              <img class="jinghui" :src="require('@/assets/jinghui.png')" alt="备案图标">
              <a :href="dynamicContent.description" target="_blank" class="beian-link">
                {{ dynamicContent.subtitle }}
              </a>
            </span>
          </div>
        </div>
        <div class="footer-content">
          <div class="flow-title">
            <h4>—— 关注了解更多 ——</h4>
          </div>
          <div class="footer-section follow-us">
            <div class="qr-code">
              <img :src="wechat_qr_code" alt="微信公众号">
              <p>{{ wechat_qr_code_txt }}</p>
            </div>
            <div class="qr-code">
              <img :src="app_qr_code" alt="远程智能采暖app">
              <p>{{ app_qr_code_txt }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

  </footer>
</template>

<script>
import { API } from '@/api/index.js'
export default {
  name: 'Footer',
  data() {
    return {
      wechat_qr_code: '',
      wechat_qr_code_txt: '',
      app_qr_code: '',
      app_qr_code_txt: '',
      footer_logo: '',
      dynamicContent: null,
    }
  },
  computed: {

  },
  methods: {
    async getImageByTag() {
      const tags = ['footer_logo', 'app_qr_code', 'wechat_qr_code']
      tags.forEach(async tag => {
        const response = await API.getImageByTag(tag)
        if (response.success) {
          this[`${tag}`] = response.data.images[0].image_url;
          this[`${tag}_txt`] = response.data.images[0].description;
        } else {
          throw new Error(response.message || '获取图片失败')
        }
      })
    },
    async loadFooterContent() {
      try {

        const response = await API.getPageContentBlock('footer', 'footer_content')
        if (response.success) {
          this.dynamicContent = response.data
        }
      } catch (error) {
        console.warn('加载动态内容失败，使用默认内容:', error)
      }
    }
  },
  async mounted() {
    await this.getImageByTag()
    this.loadFooterContent()
  }
}
</script>

<style lang="less" scoped>
.footer {
  background: #fff;
  color: #000;
  padding: 28px 0;
  border-top: 1px solid #f1f1f1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  // grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  // gap: 10px;
  // margin-bottom: 30px;
  text-align: center;

  .flow-title {
    h4 {
      color: #333;
      font-size: 18px;
      margin-bottom: 15px;
    }
  }
}



.footer-section {

  p {
    margin-bottom: 0;
    line-height: 1.6;
    color: #bdc3c7;
  }


}

.contact-info {
  p {
    display: flex;
    align-items: center;
    gap: 10px;

    i {
      color: #D80514;
      width: 16px;
    }
  }
}



.qr-code {
  text-align: center;

  img {
    display: inline;
    width: 90px;
    height: 90px;
  }

  p {
    font-size: 16px;
    color: #000;
  }
}

.follow-us {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;

  .qr-code {
    margin-top: 0px;
    display: grid;
  }
}

.footer-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  text-align: center;

  .footer-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    gap: 10px;
    height: 150px;

    .footer-logo-container {
      display: flex;
      align-items: center;
    }

    .footer-logo {
      width: 260px;
      height: auto;
    }

    .phone-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;

      span {
        color: #333;
        font-size: 16px;
      }
    }

    .beian-info-separator {
      color: #333;
      font-size: 14px;
      padding: 0 10px;
    }

    .beian-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
      justify-content: center;

      span {
        color: #333;
        font-size: 14px;
      }
    }

    .jinghui {
      height: 16px;
      width: auto;
      vertical-align: middle;
    }

    .beian-link {
      color: #333;
      text-decoration: none;
      font-size: 14px;
      transition: color 0.3s ease;

      &:hover {
        color: #D80514;
      }
    }

    .icp-info {
      color: #333;
      font-size: 14px;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .footer {
    padding: 20px 0 20px;
  }

  .footer-content {
    display: none;
  }

  .footer-section {

    .flow-title {
      h4 {
        font-size: 14px;
      }
    }
  }

  .follow-us {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .qr-code {
      margin-top: 0px;
    }
  }

  .footer-bottom {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 0;

    .footer-info {
      // align-items: center;
      height: auto;

      .phone-info,
      .beian-info {
        flex-direction: column;
        gap: 5px;

        span,
        .beian-link {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          font-size: 12px;
        }

        .beian-info-separator {
          display: none;
        }

      }
    }
  }
}
</style>
