const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler, CustomError } = require('../middleware/errorHandler');

const router = express.Router();

// 获取内容块列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, pageKey, blockKey, status } = req.query;
  const offset = (page - 1) * limit;

  let whereConditions = [];
  let params = [];

  // 页面筛选
  if (pageKey && pageKey.trim() !== '') {
    whereConditions.push('page_key LIKE ?');
    params.push(`%${pageKey}%`);
  }

  // 内容块筛选
  if (blockKey && blockKey.trim() !== '') {
    whereConditions.push('block_key LIKE ?');
    params.push(`%${blockKey}%`);
  }

  // 状态筛选
  if (status !== undefined && status !== '') {
    whereConditions.push('status = ?');
    params.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 查询内容块列表
  const blocks = await query(`
    SELECT
      id,
      page_key,
      block_key,
      title,
      subtitle,
      description,
      main_image,
      sub_image,
      sort_order,
      status,
      created_at,
      updated_at
    FROM page_content_blocks
    ${whereClause}
    ORDER BY page_key ASC, sort_order ASC, id ASC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `, params);

  // 查询总数
  const countResult = await query(`
    SELECT COUNT(*) as total 
    FROM page_content_blocks 
    ${whereClause}
  `, params);

  const total = countResult[0].total;
  const pages = Math.ceil(total / limit);

  res.json({
    success: true,
    message: '获取内容块列表成功',
    data: {
      blocks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages
      }
    }
  });
}));

// 获取内容块详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const blocks = await query(`
    SELECT * FROM page_content_blocks WHERE id = ?
  `, [id]);

  if (blocks.length === 0) {
    throw new CustomError('内容块不存在', 404);
  }

  res.json({
    success: true,
    message: '获取内容块详情成功',
    data: {
      block: blocks[0]
    }
  });
}));

// 创建内容块
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page_key,
    block_key,
    title,
    subtitle = '',
    description = '',
    main_image = '',
    sub_image = '',
    sort_order = 0,
    status = 1
  } = req.body;

  // 参数验证
  if (!page_key || !block_key || !title) {
    throw new CustomError('页面标识、内容块标识和标题不能为空', 400);
  }

  // 检查是否已存在相同的页面+内容块组合
  const existingBlocks = await query(`
    SELECT id FROM page_content_blocks
    WHERE page_key = ? AND block_key = ?
  `, [page_key, block_key]);

  if (existingBlocks.length > 0) {
    throw new CustomError('该页面的内容块标识已存在', 400);
  }

  const result = await query(`
    INSERT INTO page_content_blocks
    (page_key, block_key, title, subtitle, description, main_image, sub_image, sort_order, status)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `, [page_key, block_key, title, subtitle, description, main_image, sub_image, sort_order, status]);

  res.json({
    success: true,
    message: '创建内容块成功',
    data: {
      id: result.insertId
    }
  });
}));

// 更新内容块
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    page_key,
    block_key,
    title,
    subtitle,
    description,
    main_image,
    sub_image,
    sort_order,
    status
  } = req.body;

  // 检查内容块是否存在
  const existingBlocks = await query(`
    SELECT * FROM page_content_blocks WHERE id = ?
  `, [id]);

  if (existingBlocks.length === 0) {
    throw new CustomError('内容块不存在', 404);
  }

  // 参数验证
  if (!page_key || !block_key || !title) {
    throw new CustomError('页面标识、内容块标识和标题不能为空', 400);
  }

  // 检查是否与其他记录冲突
  const duplicateBlocks = await query(`
    SELECT id FROM page_content_blocks 
    WHERE page_key = ? AND block_key = ? AND id != ?
  `, [page_key, block_key, id]);

  if (duplicateBlocks.length > 0) {
    throw new CustomError('该页面的内容块标识已存在', 400);
  }

  await query(`
    UPDATE page_content_blocks
    SET page_key = ?, block_key = ?, title = ?, subtitle = ?,
        description = ?, main_image = ?, sub_image = ?, sort_order = ?, status = ?
    WHERE id = ?
  `, [page_key, block_key, title, subtitle || '', description || '',
    main_image || '', sub_image || '', sort_order || 0, status !== undefined ? status : 1, id]);

  res.json({
    success: true,
    message: '更新内容块成功'
  });
}));

// 删除内容块
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查内容块是否存在
  const existingBlocks = await query(`
    SELECT * FROM page_content_blocks WHERE id = ?
  `, [id]);

  if (existingBlocks.length === 0) {
    throw new CustomError('内容块不存在', 404);
  }

  await query('DELETE FROM page_content_blocks WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除内容块成功'
  });
}));

// 批量更新状态
router.post('/batch-status', authenticateToken, asyncHandler(async (req, res) => {
  const { ids, status } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new CustomError('请选择要操作的内容块', 400);
  }

  if (status === undefined) {
    throw new CustomError('请指定状态', 400);
  }

  const placeholders = ids.map(() => '?').join(',');
  await query(`
    UPDATE page_content_blocks 
    SET status = ? 
    WHERE id IN (${placeholders})
  `, [status, ...ids]);

  res.json({
    success: true,
    message: '批量更新状态成功'
  });
}));

module.exports = router;
