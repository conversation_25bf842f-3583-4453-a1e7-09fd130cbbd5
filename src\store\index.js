import Vue from 'vue';
import Vuex from 'vuex';
import Cookies from 'js-cookie';
import api from '@/api';

Vue.use(Vuex);

const TOKEN_KEY = 'airfit_admin_token';
const USER_INFO_KEY = 'airfit_admin_user';

const store = new Vuex.Store({
  state: {
    token: Cookies.get(TOKEN_KEY) || '',
    userInfo: JSON.parse(localStorage.getItem(USER_INFO_KEY) || 'null'),
    sidebarCollapsed: false,
    menuList: [],
    categoryList: []
  },

  getters: {
    token: state => state.token,
    userInfo: state => state.userInfo,
    isLoggedIn: state => !!state.token,
    sidebarCollapsed: state => state.sidebarCollapsed,
    menuList: state => state.menuList
  },

  mutations: {
    SET_TOKEN(state, token) {
      state.token = token;
      if (token) {
        Cookies.set(TOKEN_KEY, token, { expires: 1 }); // 1天过期
      } else {
        Cookies.remove(TOKEN_KEY);
      }
    },

    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      if (userInfo) {
        localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
      } else {
        localStorage.removeItem(USER_INFO_KEY);
      }
    },

    TOGGLE_SIDEBAR(state) {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },

    SET_MENU_LIST(state, menuList) {
      state.menuList = menuList;
    }
  },

  actions: {
    // 登录
    async login({ commit }, { username, password }) {
      try {
        const response = await api.auth.login({ username, password });
        const { token, user } = response.data;
        
        commit('SET_TOKEN', token);
        commit('SET_USER_INFO', user);
        
        return response;
      } catch (error) {
        throw error;
      }
    },

    // 获取用户信息
    async getUserInfo({ commit, state }) {
      try {
        const response = await api.auth.getProfile();
        const { user } = response.data;
        
        commit('SET_USER_INFO', user);
        
        return response;
      } catch (error) {
        // 如果获取用户信息失败，清除token
        commit('SET_TOKEN', '');
        commit('SET_USER_INFO', null);
        throw error;
      }
    },

    // 登出
    async logout({ commit }) {
      try {
        await api.auth.logout();
      } catch (error) {
        console.error('登出请求失败:', error);
      } finally {
        commit('SET_TOKEN', '');
        commit('SET_USER_INFO', null);
        commit('SET_MENU_LIST', []);
      }
    },

    // 获取菜单列表
    async getMenuList({ commit }) {
      try {
        const response = await api.menu.getList();
        const { menus } = response.data;
        
        commit('SET_MENU_LIST', menus);
        
        return response;
      } catch (error) {
        throw error;
      }
    },

    // 切换侧边栏
    toggleSidebar({ commit }) {
      commit('TOGGLE_SIDEBAR');
    }
  }
});

export default store;
