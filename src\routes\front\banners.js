const express = require('express');
const { query } = require('../../config/database');
const { asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取轮播图列表
router.get('/', asyncHandler(async (req, res) => {
  const { limit = 10 } = req.query;
  const limitNum = parseInt(limit) || 10;

  const banners = await query(`
    SELECT
      id,
      title,
      subtitle,
      image,
      mobile_image,
      tags,
      link_url,
      link_target,
      sort_order
    FROM banners
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 100
  `);

  // 处理tags字段（JSON格式）
  const processedBanners = banners.map(banner => ({
    ...banner,
    tags: banner.tags ? (typeof banner.tags === 'string' ? JSON.parse(banner.tags) : banner.tags) : []
  }));

  res.json({
    success: true,
    message: '获取轮播图列表成功',
    data: {
      banners: processedBanners,
      total: banners.length
    }
  });
}));

// 获取单个轮播图详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const banners = await query(`
    SELECT
      id,
      title,
      subtitle,
      image,
      mobile_image,
      tags,
      link_url,
      link_target,
      sort_order,
      created_at
    FROM banners
    WHERE id = ? AND status = 1
  `, [id]);

  if (banners.length === 0) {
    return res.status(404).json({
      success: false,
      message: '轮播图不存在或已下架'
    });
  }

  const banner = banners[0];

  // 处理tags字段
  banner.tags = banner.tags ? (typeof banner.tags === 'string' ? JSON.parse(banner.tags) : banner.tags) : [];

  res.json({
    success: true,
    message: '获取轮播图详情成功',
    data: {
      banner
    }
  });
}));

// 获取首页轮播图（限制数量）
router.get('/home/<USER>', asyncHandler(async (req, res) => {
  const { limit = 5 } = req.query;

  const banners = await query(`
    SELECT
      id,
      title,
      subtitle,
      image,
      mobile_image,
      link_url,
      link_target
    FROM banners
    WHERE status = 1
    ORDER BY sort_order ASC, created_at DESC
    LIMIT ?
  `, [parseInt(limit)]);

  res.json({
    success: true,
    message: '获取首页轮播图成功',
    data: {
      banners
    }
  });
}));

module.exports = router;
