<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>Banner管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增Banner
        </el-button>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="Banner标题">
            <el-input v-model="searchForm.title" placeholder="请输入Banner标题" clearable style="width: 200px;" />
          </el-form-item>
          <el-form-item label="标签">
            <el-input v-model="searchForm.tag" placeholder="请输入标签" clearable style="width: 150px;" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Banner列表表格 -->
      <el-table v-loading="loading" :data="bannerList" border class="banner-table">
        <el-table-column prop="id" label="ID" width="80" align="center" />

        <el-table-column prop="title" label="Banner标题" min-width="200" show-overflow-tooltip />

        <el-table-column prop="subtitle" label="副标题" min-width="250" show-overflow-tooltip />

        <el-table-column prop="image" label="PC端图片" width="100" align="center">
          <template slot-scope="scope">
            <el-image v-if="scope.row.image" style="width: 60px; height: 40px" :src="scope.row.image"
              :preview-src-list="[scope.row.image]" fit="cover" />
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="mobile_image" label="移动端图片" width="100" align="center">
          <template slot-scope="scope">
            <el-image v-if="scope.row.mobile_image" style="width: 60px; height: 40px" :src="scope.row.mobile_image"
              :preview-src-list="[scope.row.mobile_image]" fit="cover" />
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="标签" width="150">
          <template slot-scope="scope">
            <el-tag v-for="tag in scope.row.tags" :key="tag" size="mini" style="margin-right: 5px; margin-bottom: 2px;">
              {{ tag }}
            </el-tag>
            <span v-if="!scope.row.tags || scope.row.tags.length === 0">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="排序" width="80" align="center" />

        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.current" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" />
      </div>
    </div>

    <!-- 新增/编辑Banner弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false"
      @close="handleDialogClose">
      <el-form ref="bannerForm" :model="bannerForm" :rules="formRules" label-width="100px">
        <el-form-item label="Banner标题" prop="title">
          <el-input v-model="bannerForm.title" placeholder="请输入Banner标题" />
        </el-form-item>

        <el-form-item label="副标题">
          <el-input v-model="bannerForm.subtitle" type="textarea" :rows="2" placeholder="请输入Banner副标题..." />
        </el-form-item>

        <el-form-item label="PC端图片" prop="image">
          <ImageUploader v-model="bannerForm.image" size="300x180" tip="建议尺寸：1920×450px，支持JPG、PNG格式，大小不超过5MB"
            :max-size="5" />
        </el-form-item>

        <el-form-item label="移动端图片">
          <ImageUploader v-model="bannerForm.mobile_image" size="300x180"
            tip="建议尺寸：750×400px，首页banner建议750*1200，支持JPG、PNG格式，大小不超过5MB（可选）" :max-size="5" />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-tag v-for="tag in bannerForm.tags" :key="tag" closable @close="removeTag(tag)"
            style="margin-right: 10px;">
            {{ tag }}
          </el-tag>
          <el-input v-if="inputVisible" ref="saveTagInput" v-model="inputValue" size="small" style="width: 100px;"
            @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm" />
          <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number v-model="bannerForm.sort_order" :min="0" :max="9999" style="width: 100%;"
                placeholder="数字越小越靠前" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="跳转链接">
          <el-input v-model="bannerForm.link_url" placeholder="请输入跳转链接，如：/products" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="链接打开方式">
              <el-radio-group v-model="bannerForm.link_target">
                <el-radio label="_blank">新窗口</el-radio>
                <el-radio label="_self">当前窗口</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="bannerForm.status">
                <el-radio :label="0">禁用</el-radio>
                <el-radio :label="1">启用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { banners } from '@/api/banners'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  components: {
    ImageUploader
  },
  name: 'BannerManagement',
  data() {
    return {
      loading: false,
      submitLoading: false,
      bannerList: [],
      searchForm: {
        title: '',
        tag: '',
        status: ''
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      dialogVisible: false,
      isEdit: false,
      bannerForm: {
        title: '',
        subtitle: '',
        image: '',
        mobile_image: '',
        tags: [],
        status: 1,
        sort_order: 0,
        link_url: '',
        link_target: '_blank'
      },
      inputVisible: false,
      inputValue: '',
      formRules: {
        title: [
          { required: true, message: 'Banner标题不能为空', trigger: 'blur' }
        ],
        image: [
          { required: true, message: 'Banner图片不能为空', trigger: 'change' }
        ],
        tags: [
          { required: true, message: '标签不能为空', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑Banner' : '新增Banner'
    }
  },
  mounted() {
    this.loadBannerList()
  },
  methods: {
    // 加载Banner列表
    async loadBannerList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.current,
          limit: this.pagination.size,
          ...this.searchForm
        }

        const response = await banners.getList(params)

        if (response.code === 200) {
          this.bannerList = response.data.list
          this.pagination = response.data.pagination
        } else {
          this.$message.error(response.message || '获取Banner列表失败')
        }
      } catch (error) {
        console.error('获取Banner列表失败:', error)
        this.$message.error('获取Banner列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadBannerList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        title: '',
        tag: '',
        status: ''
      }
      this.pagination.current = 1
      this.loadBannerList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadBannerList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadBannerList()
    },

    // 新增Banner
    handleAdd() {
      this.isEdit = false
      this.bannerForm = {
        title: '',
        subtitle: '',
        image: '',
        mobile_image: '',
        tags: [],
        status: 1,
        sort_order: 0,
        link_url: '',
        link_target: '_blank'
      }
      this.inputVisible = false
      this.inputValue = ''
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.bannerForm.clearValidate()
      })
    },

    // 编辑Banner
    async handleEdit(row) {
      this.isEdit = true

      try {
        const response = await banners.getDetail(row.id)
        if (response.code === 200) {
          this.bannerForm = { ...response.data }
          this.dialogVisible = true
        } else {
          this.$message.error(response.message || '获取Banner详情失败')
        }
      } catch (error) {
        console.error('获取Banner详情失败:', error)
        this.$message.error('获取Banner详情失败')
      }
    },

    // 删除Banner
    handleDelete(row) {
      this.$confirm(`确认删除Banner "${row.title}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await banners.delete(row.id)
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.loadBannerList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除Banner失败:', error)
          this.$message.error('删除Banner失败')
        }
      }).catch(() => {
        // 用户取消删除，不做任何操作，避免报错
      })
    },

    // 状态切换
    async handleStatusChange(row) {
      try {
        const response = await banners.toggleStatus(row.id, row.status)
        if (response.code === 200) {
          this.$message.success(`${row.status ? '启用' : '禁用'}成功`)
        } else {
          this.$message.error(response.message || '状态切换失败')
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1
        }
      } catch (error) {
        console.error('状态切换失败:', error)
        this.$message.error('状态切换失败')
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.bannerForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true

          try {
            let response
            if (this.isEdit) {
              response = await banners.update(this.bannerForm.id, this.bannerForm)
            } else {
              response = await banners.create(this.bannerForm)
            }

            if (response.code === 200) {
              this.$message.success(`${this.isEdit ? '更新' : '创建'}成功`)
              this.dialogVisible = false
              this.loadBannerList()
            } else {
              this.$message.error(response.message || `${this.isEdit ? '更新' : '创建'}失败`)
            }
          } catch (error) {
            console.error(`${this.isEdit ? '更新' : '创建'}Banner失败:`, error)
            this.$message.error(`${this.isEdit ? '更新' : '创建'}Banner失败`)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 关闭弹窗
    handleDialogClose() {
      this.$refs.bannerForm.resetFields()
      this.inputVisible = false
      this.inputValue = ''
    },

    // 显示标签输入框
    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    // 确认添加标签
    handleInputConfirm() {
      const inputValue = this.inputValue
      if (inputValue && !this.bannerForm.tags.includes(inputValue)) {
        this.bannerForm.tags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    // 移除标签
    removeTag(tag) {
      const index = this.bannerForm.tags.indexOf(tag)
      if (index > -1) {
        this.bannerForm.tags.splice(index, 1)
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },


  }
}
</script>

<style lang="less" scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;

  h2 {
    margin: 0;
    color: #333;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.banner-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 20px;
}



.dialog-footer {
  text-align: right;
}
</style>