/**
 * 设备检测工具类
 * 用于检测用户设备类型和特性
 */

class DeviceDetect {
  constructor() {
    this.userAgent = navigator.userAgent.toLowerCase()
    this.platform = navigator.platform.toLowerCase()
  }

  /**
   * 检测是否为移动设备
   * @returns {boolean}
   */
  isMobile() {
    // 检测移动设备的用户代理字符串
    const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/i
    
    // 检测触摸设备
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    
    // 检测屏幕尺寸（作为辅助判断）
    const isSmallScreen = window.innerWidth <= 768
    
    // 综合判断
    return mobileRegex.test(this.userAgent) || (isTouchDevice && isSmallScreen)
  }

  /**
   * 检测是否为平板设备
   * @returns {boolean}
   */
  isTablet() {
    const tabletRegex = /ipad|android(?!.*mobile)|tablet/i
    const isLargeTouch = 'ontouchstart' in window && window.innerWidth >= 768 && window.innerWidth <= 1024
    
    return tabletRegex.test(this.userAgent) || isLargeTouch
  }

  /**
   * 检测是否为桌面设备
   * @returns {boolean}
   */
  isDesktop() {
    return !this.isMobile() && !this.isTablet()
  }

  /**
   * 检测是否为iOS设备
   * @returns {boolean}
   */
  isIOS() {
    return /iphone|ipad|ipod/i.test(this.userAgent)
  }

  /**
   * 检测是否为Android设备
   * @returns {boolean}
   */
  isAndroid() {
    return /android/i.test(this.userAgent)
  }

  /**
   * 检测是否为微信浏览器
   * @returns {boolean}
   */
  isWeChat() {
    return /micromessenger/i.test(this.userAgent)
  }

  /**
   * 检测是否支持触摸
   * @returns {boolean}
   */
  isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }

  /**
   * 获取设备类型
   * @returns {string} 'mobile' | 'tablet' | 'desktop'
   */
  getDeviceType() {
    if (this.isMobile()) return 'mobile'
    if (this.isTablet()) return 'tablet'
    return 'desktop'
  }

  /**
   * 获取屏幕尺寸分类
   * @returns {string} 'small' | 'medium' | 'large' | 'xlarge'
   */
  getScreenSize() {
    const width = window.innerWidth
    
    if (width < 576) return 'small'      // 手机
    if (width < 768) return 'medium'     // 大屏手机
    if (width < 992) return 'large'      // 平板
    return 'xlarge'                      // 桌面
  }

  /**
   * 检测是否为移动端浏览器环境
   * 这是专门为导航菜单等UI组件设计的判断方法
   * @returns {boolean}
   */
  isMobileUI() {
    // 优先检测用户代理
    if (this.isMobile()) return true
    
    // 检测屏幕尺寸和触摸能力
    const isSmallScreen = window.innerWidth <= 768
    const isTouchCapable = this.isTouchDevice()
    
    // 如果是小屏幕且支持触摸，认为是移动端UI环境
    return isSmallScreen && isTouchCapable
  }
}

// 创建单例实例
const deviceDetect = new DeviceDetect()

// 导出常用方法
export const isMobile = () => deviceDetect.isMobile()
export const isTablet = () => deviceDetect.isTablet()
export const isDesktop = () => deviceDetect.isDesktop()
export const isIOS = () => deviceDetect.isIOS()
export const isAndroid = () => deviceDetect.isAndroid()
export const isWeChat = () => deviceDetect.isWeChat()
export const isTouchDevice = () => deviceDetect.isTouchDevice()
export const getDeviceType = () => deviceDetect.getDeviceType()
export const getScreenSize = () => deviceDetect.getScreenSize()
export const isMobileUI = () => deviceDetect.isMobileUI()

// 导出类实例
export default deviceDetect
