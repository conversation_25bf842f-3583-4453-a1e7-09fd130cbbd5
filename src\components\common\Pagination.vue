<template>
  <div class="pagination">
    <!-- 上一页 -->
    <button class="pagination-btn prev" :disabled="currentPage <= 1" @click="goToPage(currentPage - 1)">
      <i class="fas fa-chevron-left"></i>
      上一页
    </button>

    <!-- 页码 -->
    <div class="pagination-numbers">
      <!-- 第一页 -->
      <button v-if="showFirstPage" class="pagination-number" :class="{ active: currentPage === 1 }"
        @click="goToPage(1)">
        1
      </button>

      <!-- 省略号 -->
      <span v-if="showStartEllipsis" class="pagination-ellipsis">...</span>

      <!-- 中间页码 -->
      <button v-for="page in visiblePages" :key="page" class="pagination-number"
        :class="{ active: currentPage === page }" @click="goToPage(page)">
        {{ page }}
      </button>

      <!-- 省略号 -->
      <span v-if="showEndEllipsis" class="pagination-ellipsis">...</span>

      <!-- 最后一页 -->
      <button v-if="showLastPage" class="pagination-number" :class="{ active: currentPage === totalPages }"
        @click="goToPage(totalPages)">
        {{ totalPages }}
      </button>
    </div>

    <!-- 下一页 -->
    <button class="pagination-btn next" :disabled="currentPage >= totalPages" @click="goToPage(currentPage + 1)">
      下一页
      <i class="fas fa-chevron-right"></i>
    </button>

    <!-- 页码信息 -->
    <div class="pagination-info">
      第 {{ currentPage }} 页，共 {{ totalPages }} 页
    </div>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    currentPage: {
      type: Number,
      default: 1
    },
    totalPages: {
      type: Number,
      required: true
    },
    maxVisiblePages: {
      type: Number,
      default: 5
    }
  },
  computed: {
    visiblePages() {
      const { currentPage, totalPages, maxVisiblePages } = this
      const half = Math.floor(maxVisiblePages / 2)

      let start = Math.max(1, currentPage - half)
      let end = Math.min(totalPages, start + maxVisiblePages - 1)

      // 调整起始位置
      if (end - start + 1 < maxVisiblePages) {
        start = Math.max(1, end - maxVisiblePages + 1)
      }

      const pages = []
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    },

    showFirstPage() {
      return this.visiblePages[0] > 1
    },

    showLastPage() {
      return this.visiblePages[this.visiblePages.length - 1] < this.totalPages
    },

    showStartEllipsis() {
      return this.visiblePages[0] > 2
    },

    showEndEllipsis() {
      return this.visiblePages[this.visiblePages.length - 1] < this.totalPages - 1
    }
  },
  methods: {
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.$emit('page-change', page)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px 0;
  margin-bottom: 0px;

  .pagination-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover:not(:disabled) {
      background: #f5f5f5;
      border-color: #D80514;
      color: #D80514;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    i {
      font-size: 12px;
    }
  }

  .pagination-numbers {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .pagination-number {
    width: 36px;
    height: 36px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f5f5f5;
      border-color: #D80514;
      color: #D80514;
    }

    &.active {
      background: #D80514;
      border-color: #D80514;
      color: white;
    }
  }

  .pagination-ellipsis {
    padding: 0 8px;
    color: #999;
    font-size: 14px;
  }

  .pagination-info {
    margin-left: 20px;
    font-size: 14px;
    color: #666;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .pagination {
    flex-wrap: wrap;
    gap: 6px;

    .pagination-btn {
      padding: 6px 12px;
      font-size: 13px;

      span {
        display: none;
      }
    }

    .pagination-number {
      width: 32px;
      height: 32px;
      font-size: 13px;
    }

    .pagination-info {
      width: 100%;
      text-align: center;
      margin: 10px 0 0 0;
      font-size: 13px;
    }
  }
}
</style>
