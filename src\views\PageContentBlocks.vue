<template>
  <div class="page-container">
    <div class="content-card">
      <div class="page-header">
        <h2>页面内容管理</h2>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          添加内容块
        </el-button>
      </div>

      <!-- 搜索筛选 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="页面标识">
            <el-input v-model="searchForm.pageKey" placeholder="请输入页面标识" clearable style="width: 150px;" />
          </el-form-item>
          <el-form-item label="内容块标识">
            <el-input v-model="searchForm.blockKey" placeholder="请输入内容块标识" clearable style="width: 150px;" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option label="启用" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="blocksList" v-loading="loading" stripe style="width: 100%"
        @selection-change="handleSelectionChange">
        <!-- <el-table-column type="selection" width="55"></el-table-column> -->
        <el-table-column prop="page_key" label="页面标识" width="120"></el-table-column>
        <el-table-column prop="block_key" label="内容块标识" width="150"></el-table-column>
        <el-table-column prop="title" label="标题" min-width="150"></el-table-column>
        <el-table-column prop="subtitle" label="副标题" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>

        <el-table-column label="主图" width="100" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.main_image" class="table-image">
              <img :src="scope.row.main_image" alt="主图" />
            </div>
            <span v-else class="no-image">无主图</span>
          </template>
        </el-table-column>

        <el-table-column label="副图" width="100" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.sub_image" class="table-image">
              <img :src="scope.row.sub_image" alt="副图" />
            </div>
            <span v-else class="no-image">无副图</span>
          </template>
        </el-table-column>

        <el-table-column prop="sort_order" label="排序" width="80" align="center"></el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedBlocks.length > 0">
        <span>已选择 {{ selectedBlocks.length }} 项</span>
        <el-button size="small" @click="handleBatchStatus(1)">批量启用</el-button>
        <el-button size="small" @click="handleBatchStatus(0)">批量禁用</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.limit"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" />
      </div>
    </div>

    <!-- 添加/编辑弹窗 -->
    <el-dialog top="10vh" :title="isEdit ? '编辑内容块' : '添加内容块'" :visible.sync="dialogVisible" width="800px"
      @close="resetForm" class="content-dialog">
      <div class="dialog-content">
        <el-form :model="blockForm" :rules="formRules" ref="blockForm" label-width="100px">
          <el-form-item label="页面标识" prop="page_key">
            <el-input :disabled="isEdit" v-model="blockForm.page_key" placeholder="请输入页面标识，如：home, products"></el-input>
          </el-form-item>
          <el-form-item label="内容块标识" prop="block_key">
            <el-input :disabled="isEdit" v-model="blockForm.block_key"
              placeholder="请输入内容块标识，如：featured_cases"></el-input>
          </el-form-item>
          <el-form-item label="标题" prop="title">
            <el-input v-model="blockForm.title" placeholder="请输入标题"></el-input>
          </el-form-item>
          <el-form-item label="副标题" prop="subtitle">
            <el-input v-model="blockForm.subtitle" placeholder="请输入副标题"></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input type="textarea" v-model="blockForm.description" placeholder="请输入描述说明" :rows="3"></el-input>
          </el-form-item>

          <el-form-item label="主图">
            <ImageUploader v-model="blockForm.main_image" text="上传主图" tip="支持jpg、png格式，大小不超过5MB" size="400x200"
              :max-size="5" />
          </el-form-item>

          <el-form-item label="副图">
            <ImageUploader v-model="blockForm.sub_image" text="上传副图" tip="支持jpg、png格式，大小不超过5MB" size="300x150"
              :max-size="5" />
          </el-form-item>

          <el-form-item label="排序" prop="sort_order">
            <el-input-number v-model="blockForm.sort_order" :min="0" :max="999"></el-input-number>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="blockForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pageContentBlocks } from '@/api/page-content-blocks'
import { formatDateTime } from '@/utils/dateFormat'
import ImageUploader from '@/components/ImageUploader.vue'

export default {
  name: 'PageContentBlocks',
  components: {
    ImageUploader
  },
  data() {
    return {
      loading: false,
      submitting: false,
      blocksList: [],
      selectedBlocks: [],
      searchForm: {
        pageKey: '',
        blockKey: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      dialogVisible: false,
      isEdit: false,
      editId: null,
      blockForm: {
        page_key: '',
        block_key: '',
        title: '',
        subtitle: '',
        description: '',
        main_image: '',
        sub_image: '',
        sort_order: 0,
        status: 1
      },
      formRules: {
        page_key: [
          { required: true, message: '请输入页面标识', trigger: 'blur' }
        ],
        block_key: [
          { required: true, message: '请输入内容块标识', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchBlocksList()
  },
  methods: {
    formatDateTime,

    // 获取内容块列表
    async fetchBlocksList() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          pageKey: this.searchForm.pageKey,
          blockKey: this.searchForm.blockKey,
          status: this.searchForm.status
        }

        const response = await pageContentBlocks.getList(params)
        this.blocksList = response.data.blocks || []
        this.pagination = response.data.pagination || {}
      } catch (error) {
        console.error('获取内容块列表失败:', error)
        this.$message.error('获取内容块列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.fetchBlocksList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        pageKey: '',
        blockKey: '',
        status: ''
      }
      this.pagination.page = 1
      this.fetchBlocksList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.fetchBlocksList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchBlocksList()
    },

    // 选择改变
    handleSelectionChange(selection) {
      this.selectedBlocks = selection
    },

    // 添加
    handleAdd() {
      this.isEdit = false
      this.editId = null
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.editId = row.id
      this.blockForm = {
        page_key: row.page_key,
        block_key: row.block_key,
        title: row.title,
        subtitle: row.subtitle || '',
        description: row.description || '',
        main_image: row.main_image || '',
        sub_image: row.sub_image || '',
        sort_order: row.sort_order || 0,
        status: row.status
      }
      this.dialogVisible = true
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个内容块吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await pageContentBlocks.delete(row.id)
        this.$message.success('删除成功')
        this.fetchBlocksList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除内容块失败:', error)
          this.$message.error('删除内容块失败')
        }
      }
    },

    // 批量状态更新
    async handleBatchStatus(status) {
      try {
        const ids = this.selectedBlocks.map(item => item.id)
        await pageContentBlocks.batchUpdateStatus(ids, status)
        this.$message.success('批量更新状态成功')
        this.fetchBlocksList()
      } catch (error) {
        console.error('批量更新状态失败:', error)
        this.$message.error('批量更新状态失败')
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.blockForm.validate()
        this.submitting = true

        if (this.isEdit) {
          await pageContentBlocks.update(this.editId, this.blockForm)
          this.$message.success('更新内容块成功')
        } else {
          await pageContentBlocks.create(this.blockForm)
          this.$message.success('创建内容块成功')
        }

        this.dialogVisible = false
        this.fetchBlocksList()
      } catch (error) {
        if (error.fields) {
          // 表单验证错误
          return
        }
        console.error('保存内容块失败:', error)
        this.$message.error('保存内容块失败')
      } finally {
        this.submitting = false
      }
    },

    // 重置表单
    resetForm() {
      this.blockForm = {
        page_key: '',
        block_key: '',
        title: '',
        subtitle: '',
        description: '',
        sort_order: 0,
        status: 1
      }
      if (this.$refs.blockForm) {
        this.$refs.blockForm.resetFields()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.page-container {
  padding: 20px;
}

.content-card {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.batch-actions {
  margin: 20px 0;
  padding: 10px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;

  span {
    margin-right: 10px;
    color: #409eff;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.table-image {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  margin: 0 auto;
}

.table-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #999;
  font-size: 12px;
}

/* 弹窗内容滚动样式 */
.content-dialog .el-dialog__body {
  padding: 0;
  max-height: 70vh;
  overflow: hidden;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
