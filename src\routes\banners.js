const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { CustomError } = require('../middleware/errorHandler');

// 获取banner列表（支持分页、搜索、筛选）
router.get('/', async (req, res) => {
  const {
    page = 1,
    limit = 10,
    title = '',
    tag = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (tag) {
    whereConditions.push('JSON_SEARCH(tags, "one", ?) IS NOT NULL');
    queryParams.push(tag);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(status);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  try {
    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total FROM banners ${whereClause}
    `, queryParams);
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (page - 1) * limit;
    const bannersResult = await query(`
      SELECT id, title, subtitle, image, mobile_image, JSON_EXTRACT(tags, '$') as tags, status, sort_order, link_url, link_target, created_at, updated_at
      FROM banners ${whereClause}
      ORDER BY sort_order ASC, created_at DESC
      LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
    `, queryParams);

    // 处理tags字段，确保为数组格式
    bannersResult.forEach(banner => {
      // JSON_EXTRACT应该直接返回对象，但为了兼容性，仍然进行检查
      if (!Array.isArray(banner.tags)) {
        banner.tags = [];
      }
    });

    res.json({
      code: 200,
      message: '获取banner列表成功',
      data: {
        list: bannersResult,
        pagination: {
          current: parseInt(page),
          size: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取banner列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取banner列表失败',
      error: error.message
    });
  }
});

// 获取banner详情
router.get('/:id', async (req, res) => {
  const { id } = req.params;

  try {
    const result = await query('SELECT id, title, subtitle, image, mobile_image, JSON_EXTRACT(tags, \'$\') as tags, status, sort_order, link_url, link_target, created_at, updated_at FROM banners WHERE id = ?', [id]);

    if (result.length === 0) {
      throw new CustomError('banner不存在', 404);
    }

    const banner = result[0];
    // 处理tags字段，确保为数组格式
    if (!Array.isArray(banner.tags)) {
      banner.tags = [];
    }

    res.json({
      code: 200,
      message: '获取banner详情成功',
      data: banner
    });
  } catch (error) {
    console.error('获取banner详情失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '获取banner详情失败',
        error: error.message
      });
    }
  }
});

// 创建banner（需要认证）
router.post('/', authenticateToken, async (req, res) => {
  const {
    title,
    subtitle,
    image,
    mobile_image,
    tags = [],
    status = 1,
    sort_order = 0,
    link_url,
    link_target = '_blank'
  } = req.body;

  // 参数验证
  if (!title) {
    throw new CustomError('banner标题不能为空', 400);
  }

  if (!image) {
    throw new CustomError('banner图片不能为空', 400);
  }

  try {
    const result = await query(`
      INSERT INTO banners (title, subtitle, image, mobile_image, tags, status, sort_order, link_url, link_target)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [title, subtitle || null, image, mobile_image || null, JSON.stringify(tags), status, sort_order, link_url || null, link_target]);

    res.json({
      code: 200,
      message: '创建banner成功',
      data: {
        id: result.insertId
      }
    });
  } catch (error) {
    console.error('创建banner失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建banner失败',
      error: error.message
    });
  }
});

// 更新banner（需要认证）
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    title,
    subtitle,
    image,
    mobile_image,
    tags,
    status,
    sort_order,
    link_url,
    link_target
  } = req.body;

  try {
    // 检查banner是否存在
    const existingBanner = await query('SELECT id FROM banners WHERE id = ?', [id]);
    if (existingBanner.length === 0) {
      throw new CustomError('banner不存在', 404);
    }

    // 构建更新字段
    let updateFields = [];
    let updateParams = [];

    if (title !== undefined) {
      updateFields.push('title = ?');
      updateParams.push(title);
    }

    if (subtitle !== undefined) {
      updateFields.push('subtitle = ?');
      updateParams.push(subtitle);
    }

    if (image !== undefined) {
      updateFields.push('image = ?');
      updateParams.push(image);
    }

    if (mobile_image !== undefined) {
      updateFields.push('mobile_image = ?');
      updateParams.push(mobile_image);
    }

    if (tags !== undefined) {
      updateFields.push('tags = ?');
      updateParams.push(JSON.stringify(tags));
    }

    if (status !== undefined) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }

    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateParams.push(sort_order);
    }

    if (link_url !== undefined) {
      updateFields.push('link_url = ?');
      updateParams.push(link_url);
    }

    if (link_target !== undefined) {
      updateFields.push('link_target = ?');
      updateParams.push(link_target);
    }

    if (updateFields.length === 0) {
      throw new CustomError('没有要更新的字段', 400);
    }

    updateParams.push(id);

    await query(`
      UPDATE banners SET ${updateFields.join(', ')}
      WHERE id = ?
    `, updateParams);

    res.json({
      code: 200,
      message: '更新banner成功'
    });
  } catch (error) {
    console.error('更新banner失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '更新banner失败',
        error: error.message
      });
    }
  }
});

// 删除banner（需要认证）
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    // 检查banner是否存在
    const existingBanner = await query('SELECT id FROM banners WHERE id = ?', [id]);
    if (existingBanner.length === 0) {
      throw new CustomError('banner不存在', 404);
    }

    await query('DELETE FROM banners WHERE id = ?', [id]);

    res.json({
      code: 200,
      message: '删除banner成功'
    });
  } catch (error) {
    console.error('删除banner失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '删除banner失败',
        error: error.message
      });
    }
  }
});

// 切换banner状态（需要认证）
router.patch('/:id/status', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  if (status === undefined || (status !== 0 && status !== 1)) {
    throw new CustomError('状态值必须为0或1', 400);
  }

  try {
    // 检查banner是否存在
    const existingBanner = await query('SELECT id FROM banners WHERE id = ?', [id]);
    if (existingBanner.length === 0) {
      throw new CustomError('banner不存在', 404);
    }

    await query('UPDATE banners SET status = ? WHERE id = ?', [status, id]);

    res.json({
      code: 200,
      message: '切换banner状态成功'
    });
  } catch (error) {
    console.error('切换banner状态失败:', error);
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({
        code: error.statusCode,
        message: error.message
      });
    } else {
      res.status(500).json({
        code: 500,
        message: '切换banner状态失败',
        error: error.message
      });
    }
  }
});

module.exports = router; 