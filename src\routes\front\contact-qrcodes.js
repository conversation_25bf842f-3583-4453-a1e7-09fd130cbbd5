const express = require('express');
const { query } = require('../../config/database');
const { CustomError, asyncHandler } = require('../../middleware/errorHandler');

const router = express.Router();

// 获取联系我们页面的二维码列表
router.get('/', asyncHandler(async (req, res) => {
  try {
    // 获取标签为"联系我们二维码"且状态为启用的二维码数据
    const qrCodes = await query(`
      SELECT
        id,
        image_url,
        description,
        tag,
        sort_order,
        created_at,
        updated_at
      FROM qr_codes
      WHERE tag = '联系我们二维码' AND status = 1
      ORDER BY sort_order ASC, created_at DESC
    `);

    // 为每个二维码添加完整的图片URL（如果需要的话）
    const processedQrCodes = qrCodes.map(qrCode => ({
      ...qrCode,
      // 如果image_url是相对路径，可以在这里添加域名前缀
      // image_url: qrCode.image_url.startsWith('http') ? qrCode.image_url : `${process.env.BASE_URL}${qrCode.image_url}`
    }));

    res.json({
      success: true,
      message: '获取联系我们二维码成功',
      data: {
        qrCodes: processedQrCodes,
        total: processedQrCodes.length
      }
    });

  } catch (error) {
    console.error('获取联系我们二维码失败:', error);
    throw new CustomError('获取联系我们二维码失败', 500);
  }
}));

// 获取单个二维码详情
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(id)) {
    throw new CustomError('无效的二维码ID', 400);
  }

  try {
    const qrCodes = await query(`
      SELECT 
        id,
        image_url,
        description,
        tag,
        sort_order,
        created_at,
        updated_at
      FROM qr_codes 
      WHERE id = ? AND tag = '联系我们二维码' AND status = 1
    `, [id]);

    if (qrCodes.length === 0) {
      throw new CustomError('二维码不存在', 404);
    }

    const qrCode = qrCodes[0];

    res.json({
      success: true,
      message: '获取二维码详情成功',
      data: {
        qrCode
      }
    });

  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    console.error('获取二维码详情失败:', error);
    throw new CustomError('获取二维码详情失败', 500);
  }
}));

module.exports = router;
