const express = require('express');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 获取发展历程列表
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    year = '',
    title = '',
    status = ''
  } = req.query;

  // 构建查询条件
  let whereConditions = [];
  let queryParams = [];

  if (year) {
    whereConditions.push('year = ?');
    queryParams.push(parseInt(year));
  }

  if (title) {
    whereConditions.push('title LIKE ?');
    queryParams.push(`%${title}%`);
  }

  if (status !== '') {
    whereConditions.push('status = ?');
    queryParams.push(parseInt(status));
  }

  const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM development_history ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 计算分页
  const offset = (page - 1) * limit;

  // 获取发展历程列表
  const historyQuery = `
    SELECT id, year, title, subtitle, summary, image_url, status, sort_order, created_at, updated_at
    FROM development_history
    ${whereClause}
    ORDER BY year DESC, sort_order ASC, created_at DESC
    LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
  `;

  const history = await query(historyQuery, queryParams);

  res.json({
    success: true,
    message: '获取发展历程列表成功',
    data: {
      history,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: total,
        total_pages: Math.ceil(total / limit)
      }
    }
  });
}));

// 获取发展历程详情
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  const rows = await query('SELECT * FROM development_history WHERE id = ?', [id]);

  if (rows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '发展历程记录不存在'
    });
  }

  res.json({
    success: true,
    message: '获取发展历程详情成功',
    data: rows[0]
  });
}));

// 创建发展历程
router.post('/', authenticateToken, asyncHandler(async (req, res) => {
  const { year, title, subtitle, summary, image_url, sort_order = 0, status = 1 } = req.body;

  // 验证必填字段
  if (!year || !title) {
    return res.status(400).json({
      success: false,
      message: '年份和标题不能为空'
    });
  }

  const result = await query(
    'INSERT INTO development_history (year, title, subtitle, summary, image_url, sort_order, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [parseInt(year), title, subtitle || '', summary || '', image_url || '', parseInt(sort_order), parseInt(status)]
  );

  const newRows = await query('SELECT * FROM development_history WHERE id = ?', [result.insertId]);

  res.status(201).json({
    success: true,
    message: '创建发展历程成功',
    data: newRows[0]
  });
}));

// 更新发展历程
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { year, title, subtitle, summary, image_url, sort_order, status } = req.body;

  // 验证必填字段
  if (!year || !title) {
    return res.status(400).json({
      success: false,
      message: '年份和标题不能为空'
    });
  }

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM development_history WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '发展历程记录不存在'
    });
  }

  await query(
    'UPDATE development_history SET year = ?, title = ?, subtitle = ?, summary = ?, image_url = ?, sort_order = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [parseInt(year), title, subtitle || '', summary || '', image_url || '', parseInt(sort_order), parseInt(status), id]
  );

  const updatedRows = await query('SELECT * FROM development_history WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '更新发展历程成功',
    data: updatedRows[0]
  });
}));

// 删除发展历程
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;

  // 检查记录是否存在
  const existingRows = await query('SELECT id FROM development_history WHERE id = ?', [id]);
  if (existingRows.length === 0) {
    return res.status(404).json({
      success: false,
      message: '发展历程记录不存在'
    });
  }

  await query('DELETE FROM development_history WHERE id = ?', [id]);

  res.json({
    success: true,
    message: '删除发展历程成功'
  });
}));

module.exports = router;
