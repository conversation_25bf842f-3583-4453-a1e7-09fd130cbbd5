{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "src/**/*.vue"], "exclude": ["node_modules", "dist"], "vueCompilerOptions": {"target": 2.6, "globalTypesPath": "./node_modules/vue/types/index.d.ts"}}